<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Target Configuration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --secondary: #764ba2;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e5e7eb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --border: #d1d5db;
            --border-light: #e5e7eb;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
            --radius: 8px;
            --radius-xl: 12px;
            --radius-2xl: 16px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
        }

        .left-panel {
            max-width: 400px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-2xl);
            padding: 24px;
            box-shadow: var(--shadow);
        }

        .card-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border);
            border-radius: var(--radius);
            font-size: 16px;
            transition: all 0.2s ease;
            background: var(--bg-primary);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
        }

        .form-input.valid {
            border-color: var(--success);
        }

        .form-input.invalid {
            border-color: var(--danger);
        }

        .inline-note {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: var(--radius);
            padding: 12px;
            font-size: 13px;
            color: #92400e;
            margin-bottom: 16px;
        }

        .button-row {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 16px;
            border: 2px solid var(--border);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: var(--radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
        }

        .btn:hover {
            border-color: var(--primary);
            color: var(--primary);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-primary {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .actions-row {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .search-input {
            position: relative;
        }

        .search-input input {
            padding-left: 44px;
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            pointer-events: none;
        }

        .tag-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-chip {
            padding: 6px 12px;
            background: var(--bg-tertiary);
            border: 2px solid transparent;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tag-chip.active {
            background: var(--primary);
            color: white;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .bulk-actions .btn {
            font-size: 12px;
            padding: 8px 12px;
        }

        .groups {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .group {
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            overflow: hidden;
        }

        .group-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 16px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
        }

        .group-header:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--secondary));
        }

        .group-title-section {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .group-title {
            font-weight: 600;
            font-size: 16px;
        }

        .group-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .group-actions {
            display: flex;
            gap: 8px;
        }

        .group-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 6px 12px;
            border-radius: var(--radius);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .group-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .group-toggle {
            transition: transform 0.3s ease;
            font-size: 14px;
        }

        .group-toggle.expanded {
            transform: rotate(180deg);
        }

        .group-content {
            padding: 20px;
            display: none;
            background: var(--bg-primary);
        }

        .group-content.expanded {
            display: block;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .feature-item {
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            padding: 16px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .feature-item:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow);
        }

        .feature-item.selected {
            border-color: var(--primary);
            background: rgba(102, 126, 234, 0.05);
        }

        .feature-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .checkbox-input {
            width: 18px;
            height: 18px;
            margin-top: 2px;
            accent-color: var(--primary);
        }

        .feature-info {
            flex: 1;
        }

        .feature-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .feature-desc {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .feature-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .feature-tag {
            font-size: 11px;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 6px;
            border-radius: 10px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-valid {
            background: var(--success);
        }

        .status-invalid {
            background: var(--danger);
        }

        .status-pending {
            background: var(--warning);
        }

        .validation-message {
            font-size: 12px;
            margin-top: 4px;
            padding: 4px 0;
        }

        .validation-success {
            color: var(--success);
        }

        .validation-error {
            color: var(--danger);
        }

        .counts-display {
            background: var(--bg-tertiary);
            border-radius: var(--radius);
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        @media (max-width: 480px) {
            .left-panel {
                max-width: 100%;
                padding: 0 12px;
            }

            .card {
                padding: 16px;
            }

            .button-row {
                flex-direction: column;
            }

            .bulk-actions {
                flex-direction: column;
            }

            .group-actions {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="left-panel" id="leftPanel">
        <!-- Target Site & Credentials Card -->
        <div class="card" id="siteCard">
            <h2 class="card-title">
                🎯 Target Site & Credentials
                <span class="status-indicator" id="siteStatus"></span>
            </h2>
            
            <div class="form-group">
                <label class="form-label" for="siteUrl">Target URL</label>
                <input 
                    type="url" 
                    class="form-input" 
                    id="siteUrl" 
                    placeholder="https://example.com"
                    inputmode="url"
                    autocomplete="url"
                >
                <div class="validation-message" id="urlValidation"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="siteUsername">Username (optional)</label>
                <input 
                    type="text" 
                    class="form-input" 
                    id="siteUsername" 
                    placeholder="Enter username"
                    autocomplete="username"
                >
            </div>

            <div class="form-group">
                <label class="form-label" for="sitePassword">Password (optional)</label>
                <input 
                    type="password" 
                    class="form-input" 
                    id="sitePassword" 
                    placeholder="Enter password"
                    autocomplete="current-password"
                >
            </div>

            <div class="inline-note">
                ⚠️ Stored only for this session and redacted in payloads.
            </div>

            <div class="button-row">
                <button class="btn" id="validateUrl" disabled>
                    Validate URL
                </button>
                <button class="btn" id="testLogin" disabled>
                    Test Login (dry)
                </button>
            </div>
        </div>

        <!-- Pick Your Capabilities Card -->
        <div class="card" id="capabilitiesCard">
            <h2 class="card-title">
                🔧 Pick Your Capabilities
            </h2>

            <div class="actions-row">
                <div class="search-input">
                    <span class="search-icon">🔍</span>
                    <input 
                        type="text" 
                        class="form-input" 
                        id="searchFeatures" 
                        placeholder="Search features…"
                        aria-label="Search features"
                    >
                </div>

                <div class="tag-filters" id="tagFilters">
                    <!-- Tag chips will be populated by JavaScript -->
                </div>

                <div class="bulk-actions">
                    <button class="btn" id="selectVisible">
                        Select All (Visible)
                    </button>
                    <button class="btn" id="clearVisible">
                        Clear (Visible)
                    </button>
                    <button class="btn btn-primary" id="applySelection">
                        Apply (<span id="selectedCount">0</span>)
                    </button>
                </div>
            </div>

            <div class="counts-display" id="countsDisplay">
                Total: <span id="totalCount">0</span> | 
                Visible: <span id="visibleCount">0</span> | 
                Selected: <span id="selectedTotalCount">0</span> (<span id="selectedVisibleCount">0</span> visible)
            </div>

            <div class="groups" id="groups">
                <!-- Groups will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // State schema
        const state = {
            site: {
                url: "",
                username: "",
                password: "",
                valid: false
            },
            search: "",
            filters: {
                tags: [],
                status: "all"
            },
            expanded: {},
            selected: {},
            counts: {
                total: 0,
                visible: 0,
                selectedTotal: 0,
                selectedVisible: 0
            },
            categories: []
        };

        // Seed categories data
        const SEED_CATEGORIES = [
            {
                key: "scrape",
                title: "🔍 Data Scraping & Intelligence",
                items: [
                    { key: "scrape.web", label: "Web Scraping", desc: "Extract HTML data from websites in real-time.", tags: ["scraping", "html"] },
                    { key: "scrape.pdf", label: "PDF Extraction", desc: "Text/tables from PDFs.", tags: ["scraping", "pdf"] },
                    { key: "scrape.realtime", label: "Real-Time Scraping", desc: "Live data extraction with WebSocket connections.", tags: ["scraping", "realtime"] },
                    { key: "scrape.files", label: "File Data Extraction", desc: "Extract data from various file formats.", tags: ["scraping", "files"] },
                    { key: "scrape.export", label: "Structured Export", desc: "Export scraped data in multiple formats.", tags: ["export", "csv"] },
                    { key: "scrape.api", label: "API Connectors", desc: "Connect to external APIs for data enrichment.", tags: ["api", "integration"] }
                ]
            },
            {
                key: "sec",
                title: "🛡️ Security Testing",
                items: [
                    { key: "sec.net", label: "Network (25+)", desc: "Comprehensive network discovery and port scanning.", tags: ["security", "network"] },
                    { key: "sec.web", label: "Web App (40+)", desc: "OWASP Top 10 and custom web application testing.", tags: ["security", "web"] },
                    { key: "sec.pass", label: "Password (15+)", desc: "Brute force and hash cracking capabilities.", tags: ["security", "password"] },
                    { key: "sec.bin", label: "Binary (25+)", desc: "Static and dynamic binary analysis.", tags: ["security", "binary"] },
                    { key: "sec.cloud", label: "Cloud (20+)", desc: "AWS, Azure, GCP security assessment.", tags: ["security", "cloud"] },
                    { key: "sec.osint", label: "OSINT (20+)", desc: "Open source intelligence gathering.", tags: ["security", "osint"] }
                ]
            },
            {
                key: "ai",
                title: "🤖 AI Agents",
                items: [
                    { key: "ai.bugbounty", label: "BugBounty Agent", desc: "Automated vulnerability hunting workflows.", tags: ["ai", "bugbounty"] },
                    { key: "ai.ctf", label: "CTF Solver", desc: "Capture The Flag challenge automation.", tags: ["ai", "ctf"] },
                    { key: "ai.cve", label: "CVE Intelligence", desc: "Vulnerability database analysis and monitoring.", tags: ["ai", "cve"] },
                    { key: "ai.exploit", label: "Exploit Generator", desc: "Custom exploit development and payload creation.", tags: ["ai", "exploit"] },
                    { key: "ai.reverse", label: "Malware Reverse", desc: "Automated malware analysis and reverse engineering.", tags: ["ai", "malware"] },
                    { key: "ai.threat", label: "Threat Modeling", desc: "Automated threat model generation and analysis.", tags: ["ai", "threat"] },
                    { key: "ai.soceng", label: "Social Engineering Simulator", desc: "Phishing simulation and awareness training.", tags: ["ai", "social"] },
                    { key: "ai.planner", label: "Pentest Planner", desc: "Automated penetration testing workflow planning.", tags: ["ai", "pentest"] },
                    { key: "ai.audit", label: "Compliance Auditor", desc: "Regulatory compliance checking and reporting.", tags: ["ai", "compliance"] },
                    { key: "ai.graph", label: "Attack Graph Builder", desc: "Visual attack path analysis and mapping.", tags: ["ai", "graph"] },
                    { key: "ai.redteam", label: "AI Red Team", desc: "Automated red team operations and tactics.", tags: ["ai", "redteam"] },
                    { key: "ai.blueteam", label: "AI Blue Team", desc: "Automated defense and incident response.", tags: ["ai", "blueteam"] }
                ]
            },
            {
                key: "rep",
                title: "📊 Reporting & Visualization",
                items: [
                    { key: "rep.dash", label: "Interactive Dashboards", desc: "Real-time dashboards with live data updates.", tags: ["reporting", "dashboard"] },
                    { key: "rep.heatmap", label: "Vulnerability Heatmaps", desc: "Visual vulnerability distribution and severity mapping.", tags: ["reporting", "heatmap"] },
                    { key: "rep.charts", label: "Analytics Charts", desc: "Interactive charts and graphs for data analysis.", tags: ["reporting", "charts"] },
                    { key: "rep.export", label: "Export PDF/DOCX/CSV", desc: "Multi-format report generation and export.", tags: ["reporting", "export"] },
                    { key: "rep.collab", label: "Live Collaboration Boards", desc: "Real-time collaborative workspaces.", tags: ["reporting", "collaboration"] }
                ]
            },
            {
                key: "proc",
                title: "⚙️ Process Management",
                items: [
                    { key: "proc.cache", label: "Smart Caching", desc: "Intelligent caching for improved performance.", tags: ["process", "cache"] },
                    { key: "proc.optimize", label: "Resource Optimizer", desc: "Automatic resource allocation and optimization.", tags: ["process", "optimization"] },
                    { key: "proc.queue", label: "Task Scheduler & Queue", desc: "Advanced task scheduling and queue management.", tags: ["process", "queue"] },
                    { key: "proc.rbac", label: "Role-Based Access", desc: "Granular access control and permissions.", tags: ["process", "security"] },
                    { key: "proc.audit", label: "Audit Logging", desc: "Comprehensive activity logging and tracing.", tags: ["process", "audit"] },
                    { key: "proc.backup", label: "Backup & Recovery", desc: "Automated backup and disaster recovery.", tags: ["process", "backup"] }
                ]
            }
        ];

        // Event emitter
        const eventEmitter = {
            listeners: {},
            emit(event, data) {
                console.log(`Event: ${event}`, data);
                if (this.listeners[event]) {
                    this.listeners[event].forEach(callback => callback(data));
                }
            },
            on(event, callback) {
                if (!this.listeners[event]) {
                    this.listeners[event] = [];
                }
                this.listeners[event].push(callback);
            }
        };

        // URL validation
        function isValidUrl(string) {
            try {
                const url = new URL(string);
                return url.protocol === 'http:' || url.protocol === 'https:';
            } catch (_) {
                return false;
            }
        }

        // Get all unique tags
        function getAllTags() {
            const tags = new Set();
            SEED_CATEGORIES.forEach(category => {
                category.items.forEach(item => {
                    if (item.tags) {
                        item.tags.forEach(tag => tags.add(tag));
                    }
                });
            });
            return Array.from(tags).sort();
        }

        // Filter features based on current state
        function getFilteredFeatures() {
            const filtered = SEED_CATEGORIES.map(category => {
                const filteredItems = category.items.filter(item => {
                    // Search filter
                    if (state.search) {
                        const searchLower = state.search.toLowerCase();
                        const matchesSearch = 
                            item.label.toLowerCase().includes(searchLower) ||
                            item.key.toLowerCase().includes(searchLower) ||
                            (item.desc && item.desc.toLowerCase().includes(searchLower)) ||
                            (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchLower)));
                        
                        if (!matchesSearch) return false;
                    }

                    // Tag filters
                    if (state.filters.tags.length > 0) {
                        const hasMatchingTag = state.filters.tags.some(tag => 
                            item.tags && item.tags.includes(tag)
                        );
                        if (!hasMatchingTag) return false;
                    }

                    return true;
                });

                return {
                    ...category,
                    items: filteredItems,
                    itemsFiltered: filteredItems,
                    totalCount: category.items.length,
                    selectedCount: category.items.filter(item => state.selected[item.key]).length
                };
            }).filter(category => category.items.length > 0);

            return filtered;
        }

        // Update counts
        function updateCounts() {
            const allItems = SEED_CATEGORIES.flatMap(cat => cat.items);
            const filteredCategories = getFilteredFeatures();
            const visibleItems = filteredCategories.flatMap(cat => cat.items);
            const selectedKeys = Object.keys(state.selected).filter(key => state.selected[key]);
            const selectedVisible = visibleItems.filter(item => state.selected[item.key]).length;

            state.counts = {
                total: allItems.length,
                visible: visibleItems.length,
                selectedTotal: selectedKeys.length,
                selectedVisible: selectedVisible
            };

            // Update UI
            document.getElementById('totalCount').textContent = state.counts.total;
            document.getElementById('visibleCount').textContent = state.counts.visible;
            document.getElementById('selectedTotalCount').textContent = state.counts.selectedTotal;
            document.getElementById('selectedVisibleCount').textContent = state.counts.selectedVisible;
            document.getElementById('selectedCount').textContent = state.counts.selectedTotal;
        }

        // Render tag filters
        function renderTagFilters() {
            const tagFilters = document.getElementById('tagFilters');
            const allTags = getAllTags();
            
            tagFilters.innerHTML = allTags.map(tag => `
                <button 
                    class="tag-chip ${state.filters.tags.includes(tag) ? 'active' : ''}" 
                    data-tag="${tag}"
                    aria-pressed="${state.filters.tags.includes(tag)}"
                >
                    ${tag}
                </button>
            `).join('');
        }

        // Render groups
        function renderGroups() {
            const groupsContainer = document.getElementById('groups');
            const filteredCategories = getFilteredFeatures();

            groupsContainer.innerHTML = filteredCategories.map(category => {
                const isExpanded = state.expanded[category.key] !== false;
                
                return `
                    <div class="group" role="group" aria-labelledby="group-${category.key}">
                        <div 
                            class="group-header" 
                            data-group="${category.key}"
                            role="button"
                            aria-expanded="${isExpanded}"
                            tabindex="0"
                        >
                            <div class="group-title-section">
                                <span class="group-title" id="group-${category.key}">${category.title}</span>
                                <span class="group-count">${category.selectedCount} / ${category.totalCount}</span>
                            </div>
                            <div class="group-actions">
                                <button class="group-btn" data-action="select-group" data-group="${category.key}">
                                    Select Group
                                </button>
                                <button class="group-btn" data-action="clear-group" data-group="${category.key}">
                                    Clear Group
                                </button>
                            </div>
                            <span class="group-toggle ${isExpanded ? 'expanded' : ''}">
                                ▼
                            </span>
                        </div>
                        <div class="group-content ${isExpanded ? 'expanded' : ''}">
                            <div class="feature-list">
                                ${category.items.map(item => {
                                    const isSelected = state.selected[item.key] || false;
                                    
                                    return `
                                        <div class="feature-item ${isSelected ? 'selected' : ''}">
                                            <div class="feature-checkbox">
                                                <input 
                                                    type="checkbox" 
                                                    class="checkbox-input" 
                                                    id="feature-${item.key}"
                                                    data-key="${item.key}"
                                                    ${isSelected ? 'checked' : ''}
                                                    aria-describedby="desc-${item.key}"
                                                >
                                                <div class="feature-info">
                                                    <div class="feature-label">
                                                        <label for="feature-${item.key}">${item.label}</label>
                                                    </div>
                                                    ${item.desc ? `<div class="feature-desc" id="desc-${item.key}">${item.desc}</div>` : ''}
                                                    ${item.tags ? `
                                                        <div class="feature-tags">
                                                            ${item.tags.map(tag => `<span class="feature-tag">${tag}</span>`).join('')}
                                                        </div>
                                                    ` : ''}
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Event handlers
        function handleSiteUrlChange() {
            const urlInput = document.getElementById('siteUrl');
            const validateBtn = document.getElementById('validateUrl');
            const testBtn = document.getElementById('testLogin');
            const statusIndicator = document.getElementById('siteStatus');
            const validationMsg = document.getElementById('urlValidation');
            
            state.site.url = urlInput.value.trim();
            
            if (state.site.url) {
                validateBtn.disabled = false;
                
                if (isValidUrl(state.site.url)) {
                    state.site.valid = true;
                    urlInput.classList.remove('invalid');
                    urlInput.classList.add('valid');
                    statusIndicator.className = 'status-indicator status-valid';
                    validationMsg.className = 'validation-message validation-success';
                    validationMsg.textContent = '✓ Valid URL format';
                    testBtn.disabled = false;
                } else {
                    state.site.valid = false;
                    urlInput.classList.remove('valid');
                    urlInput.classList.add('invalid');
                    statusIndicator.className = 'status-indicator status-invalid';
                    validationMsg.className = 'validation-message validation-error';
                    validationMsg.textContent = '✗ Invalid URL format';
                    testBtn.disabled = true;
                }
            } else {
                state.site.valid = false;
                urlInput.classList.remove('valid', 'invalid');
                statusIndicator.className = 'status-indicator';
                validationMsg.textContent = '';
                validateBtn.disabled = true;
                testBtn.disabled = true;
            }
            
            emitSiteChanged();
        }

        function handleCredentialsChange() {
            state.site.username = document.getElementById('siteUsername').value;
            state.site.password = document.getElementById('sitePassword').value;
            emitSiteChanged();
        }

        function emitSiteChanged() {
            eventEmitter.emit('site_changed', {
                url: state.site.url,
                username: state.site.username || "",
                hasPassword: Boolean(state.site.password)
            });
        }

        function emitSelectionChanged() {
            const selectedKeys = Object.keys(state.selected).filter(key => state.selected[key]);
            const selectedByGroup = {};
            
            SEED_CATEGORIES.forEach(category => {
                selectedByGroup[category.key] = category.items
                    .filter(item => state.selected[item.key])
                    .map(item => item.key);
            });

            eventEmitter.emit('selection_changed', {
                selectedKeys,
                byGroup: selectedByGroup,
                count: selectedKeys.length
            });
        }

        function handleSearch(e) {
            state.search = e.target.value;
            renderGroups();
            updateCounts();
        }

        function handleTagFilter(e) {
            if (e.target.classList.contains('tag-chip')) {
                const tag = e.target.dataset.tag;
                const isActive = state.filters.tags.includes(tag);
                
                if (isActive) {
                    state.filters.tags = state.filters.tags.filter(t => t !== tag);
                } else {
                    state.filters.tags.push(tag);
                }
                
                renderTagFilters();
                renderGroups();
                updateCounts();
            }
        }

        function handleGroupToggle(e) {
            const groupHeader = e.target.closest('.group-header');
            if (groupHeader && !e.target.closest('.group-actions')) {
                const groupKey = groupHeader.dataset.group;
                state.expanded[groupKey] = !state.expanded[groupKey];
                renderGroups();
            }
        }

        function handleGroupAction(e) {
            if (e.target.dataset.action) {
                const action = e.target.dataset.action;
                const groupKey = e.target.dataset.group;
                const category = SEED_CATEGORIES.find(cat => cat.key === groupKey);
                
                if (category) {
                    if (action === 'select-group') {
                        category.items.forEach(item => {
                            state.selected[item.key] = true;
                        });
                    } else if (action === 'clear-group') {
                        category.items.forEach(item => {
                            state.selected[item.key] = false;
                        });
                    }
                    
                    renderGroups();
                    updateCounts();
                    emitSelectionChanged();
                }
            }
        }

        function handleFeatureToggle(e) {
            if (e.target.classList.contains('checkbox-input')) {
                const key = e.target.dataset.key;
                state.selected[key] = e.target.checked;
                renderGroups();
                updateCounts();
                emitSelectionChanged();
            }
        }

        function handleSelectVisible() {
            const filteredCategories = getFilteredFeatures();
            filteredCategories.forEach(category => {
                category.items.forEach(item => {
                    state.selected[item.key] = true;
                });
            });
            renderGroups();
            updateCounts();
            emitSelectionChanged();
        }

        function handleClearVisible() {
            const filteredCategories = getFilteredFeatures();
            filteredCategories.forEach(category => {
                category.items.forEach(item => {
                    state.selected[item.key] = false;
                });
            });
            renderGroups();
            updateCounts();
            emitSelectionChanged();
        }

        function handleApply() {
            const selectedKeys = Object.keys(state.selected).filter(key => state.selected[key]);
            const selectedByGroup = {};
            
            SEED_CATEGORIES.forEach(category => {
                selectedByGroup[category.key] = category.items
                    .filter(item => state.selected[item.key])
                    .map(item => item.key);
            });

            const payload = {
                site: {
                    url: state.site.url,
                    username: state.site.username,
                    password: "***" // Redacted for security
                },
                selectedKeys,
                byGroup: selectedByGroup,
                count: selectedKeys.length,
                ts: new Date().toISOString()
            };

            eventEmitter.emit('apply_payload', payload);
            alert(`Configuration applied! ${selectedKeys.length} features selected.`);
        }

        function handleValidateUrl() {
            const statusIndicator = document.getElementById('siteStatus');
            const validationMsg = document.getElementById('urlValidation');
            
            statusIndicator.className = 'status-indicator status-pending';
            validationMsg.className = 'validation-message';
            validationMsg.textContent = '⏳ Validating URL...';
            
            // Simulate URL validation
            setTimeout(() => {
                if (state.site.valid) {
                    statusIndicator.className = 'status-indicator status-valid';
                    validationMsg.className = 'validation-message validation-success';
                    validationMsg.textContent = '✓ URL is accessible';
                } else {
                    statusIndicator.className = 'status-indicator status-invalid';
                    validationMsg.className = 'validation-message validation-error';
                    validationMsg.textContent = '✗ URL validation failed';
                }
            }, 1500);
        }

        function handleTestLogin() {
            alert('Dry run login test - credentials would be validated without actual login.');
        }

        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Initialize the application
        function init() {
            // Initialize state
            state.categories = SEED_CATEGORIES;
            
            // Set default expanded groups
            SEED_CATEGORIES.forEach(category => {
                state.expanded[category.key] = true;
            });

            // Set up event listeners
            document.getElementById('siteUrl').addEventListener('input', debounce(handleSiteUrlChange, 300));
            document.getElementById('siteUsername').addEventListener('input', debounce(handleCredentialsChange, 300));
            document.getElementById('sitePassword').addEventListener('input', debounce(handleCredentialsChange, 300));
            document.getElementById('validateUrl').addEventListener('click', handleValidateUrl);
            document.getElementById('testLogin').addEventListener('click', handleTestLogin);
            
            document.getElementById('searchFeatures').addEventListener('input', debounce(handleSearch, 300));
            document.getElementById('tagFilters').addEventListener('click', handleTagFilter);
            document.getElementById('groups').addEventListener('click', handleGroupToggle);
            document.getElementById('groups').addEventListener('click', handleGroupAction);
            document.getElementById('groups').addEventListener('change', handleFeatureToggle);
            document.getElementById('selectVisible').addEventListener('click', handleSelectVisible);
            document.getElementById('clearVisible').addEventListener('click', handleClearVisible);
            document.getElementById('applySelection').addEventListener('click', handleApply);

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.target.classList.contains('group-header') && (e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault();
                    handleGroupToggle(e);
                }
            });

            // Initial render
            renderTagFilters();
            renderGroups();
            updateCounts();
            
            console.log('HexStrike AI Left Panel initialized');
        }

        // Start the application
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>