#!/usr/bin/env python3
"""
HexStrike AI - Comprehensive Feature Testing
Tests and demonstrates all major features of the application
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class HexStrikeFeatureTester:
    """Comprehensive feature testing for HexStrike AI"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or f"http://localhost:{os.getenv('CHAT_BOX_PORT', 8888)}"
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        
    def log_result(self, test_name: str, success: bool, details: str = "", response_time: float = 0):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'details': details,
            'response_time_ms': round(response_time * 1000, 2),
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name} ({result['response_time_ms']}ms)")
        if details:
            print(f"   {details}")
    
    def authenticate(self):
        """Authenticate with the application"""
        try:
            print("\n🔐 Authentication Test")
            start_time = time.time()
            login_data = {
                'username': 'admin',
                'password': 'hexstrike2024'
            }
            response = self.session.post(f"{self.base_url}/api/auth/login", 
                                       json=login_data, timeout=15)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if 'access_token' in data:
                    self.auth_token = data['access_token']
                    self.session.headers.update({'Authorization': f'Bearer {self.auth_token}'})
                    self.log_result("Authentication", True, "Successfully authenticated", response_time)
                    return True
                else:
                    self.log_result("Authentication", False, "No access token received", response_time)
            else:
                self.log_result("Authentication", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Authentication", False, f"Error: {str(e)}")
        
        return False
    
    def test_core_features(self):
        """Test core application features"""
        print("\n🎯 Core Features Test")
        
        # Test features endpoint
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/features", timeout=15)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                features = list(data.keys()) if isinstance(data, dict) else data
                self.log_result("Features Discovery", True, f"Found {len(features)} feature categories", response_time)
                
                # Print available features
                if isinstance(data, dict):
                    print("   Available Features:")
                    for category, items in data.items():
                        if isinstance(items, list):
                            print(f"     - {category}: {len(items)} tools")
                        else:
                            print(f"     - {category}: {items}")
            else:
                self.log_result("Features Discovery", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Features Discovery", False, f"Error: {str(e)}")
    
    def test_web_interface(self):
        """Test web interface endpoints"""
        print("\n🌐 Web Interface Test")
        
        interfaces = [
            ('/', 'Main Interface'),
            ('/chat-interface', 'Chat Interface'),
            ('/settings', 'Settings Interface')
        ]
        
        for endpoint, name in interfaces:
            try:
                start_time = time.time()
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=15)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'html' in content_type.lower():
                        self.log_result(name, True, "HTML interface loaded", response_time)
                    else:
                        self.log_result(name, True, "Interface accessible", response_time)
                else:
                    self.log_result(name, False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(name, False, f"Error: {str(e)}")
    
    def test_security_tools(self):
        """Test security tool endpoints"""
        print("\n🛡️ Security Tools Test")
        
        # Test popular security tools
        tools_to_test = [
            ('nmap', {'target': '127.0.0.1', 'options': ['-sn']}),
            ('nuclei', {'target': 'https://example.com', 'templates': ['basic']}),
            ('gobuster', {'target': 'https://example.com', 'wordlist': 'common'}),
            ('sqlmap', {'target': 'https://example.com', 'options': ['--batch']}),
            ('nikto', {'target': 'https://example.com', 'options': ['-h']})
        ]
        
        for tool, test_data in tools_to_test:
            try:
                start_time = time.time()
                response = self.session.post(f"{self.base_url}/api/tools/{tool}", 
                                           json=test_data, timeout=15)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_result(f"Tool: {tool.upper()}", True, f"Tool executed successfully", response_time)
                elif response.status_code == 400:
                    self.log_result(f"Tool: {tool.upper()}", True, "Tool validates input (expected)", response_time)
                elif response.status_code == 401:
                    self.log_result(f"Tool: {tool.upper()}", True, "Authentication required (expected)", response_time)
                else:
                    self.log_result(f"Tool: {tool.upper()}", False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(f"Tool: {tool.upper()}", False, f"Error: {str(e)}")
    
    def test_ai_features(self):
        """Test AI-powered features"""
        print("\n🤖 AI Features Test")
        
        ai_endpoints = [
            ('/api/ai/generate_payload', 'AI Payload Generation', {'target_type': 'web', 'vulnerability': 'xss'}),
            ('/api/intelligence/analyze-target', 'Target Analysis', {'target': 'example.com'}),
            ('/api/intelligence/select-tools', 'Tool Selection', {'target_info': {'type': 'web', 'technologies': ['apache']}})
        ]
        
        for endpoint, name, test_data in ai_endpoints:
            try:
                start_time = time.time()
                response = self.session.post(f"{self.base_url}{endpoint}", 
                                           json=test_data, timeout=15)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    self.log_result(name, True, "AI feature accessible", response_time)
                elif response.status_code == 400:
                    self.log_result(name, True, "Input validation working", response_time)
                elif response.status_code == 401:
                    self.log_result(name, True, "Authentication required", response_time)
                else:
                    self.log_result(name, False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(name, False, f"Error: {str(e)}")
    
    def test_workflow_features(self):
        """Test workflow and automation features"""
        print("\n⚡ Workflow Features Test")
        
        workflows = [
            ('/api/bugbounty/reconnaissance-workflow', 'Bug Bounty Recon', {'target': 'example.com'}),
            ('/api/bugbounty/vulnerability-hunting-workflow', 'Vulnerability Hunting', {'target': 'example.com'}),
            ('/api/ctf/suggest-tools', 'CTF Tool Suggestion', {'challenge_type': 'web'})
        ]
        
        for endpoint, name, test_data in workflows:
            try:
                start_time = time.time()
                response = self.session.post(f"{self.base_url}{endpoint}", 
                                           json=test_data, timeout=15)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    self.log_result(name, True, "Workflow accessible", response_time)
                elif response.status_code == 400:
                    self.log_result(name, True, "Input validation working", response_time)
                elif response.status_code == 401:
                    self.log_result(name, True, "Authentication required", response_time)
                else:
                    self.log_result(name, False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(name, False, f"Error: {str(e)}")
    
    def test_file_operations(self):
        """Test file operation features"""
        print("\n📁 File Operations Test")
        
        # Test file listing
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/files/list", timeout=15)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log_result("File Listing", True, "File operations accessible", response_time)
            elif response.status_code == 401:
                self.log_result("File Listing", True, "Authentication required", response_time)
            else:
                self.log_result("File Listing", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("File Listing", False, f"Error: {str(e)}")
    
    def test_database_features(self):
        """Test database connectivity"""
        print("\n🗄️ Database Features Test")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/database/test-supabase", timeout=15)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                self.log_result("Database Connection", True, f"Status: {status}", response_time)
            elif response.status_code == 401:
                self.log_result("Database Connection", True, "Authentication required", response_time)
            else:
                self.log_result("Database Connection", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Database Connection", False, f"Error: {str(e)}")
    
    def run_comprehensive_test(self):
        """Run comprehensive feature testing"""
        print("\n🚀 HexStrike AI - Comprehensive Feature Testing")
        print("=" * 60)
        
        # Authenticate first
        if not self.authenticate():
            print("\n❌ Authentication failed. Cannot proceed with authenticated tests.")
            return
        
        # Run all feature tests
        self.test_core_features()
        self.test_web_interface()
        self.test_security_tools()
        self.test_ai_features()
        self.test_workflow_features()
        self.test_file_operations()
        self.test_database_features()
        
        # Generate comprehensive summary
        self.generate_comprehensive_summary()
    
    def generate_comprehensive_summary(self):
        """Generate comprehensive test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Categorize results
        categories = {
            'Authentication': [],
            'Core Features': [],
            'Web Interface': [],
            'Security Tools': [],
            'AI Features': [],
            'Workflow Features': [],
            'File Operations': [],
            'Database Features': []
        }
        
        for result in self.test_results:
            test_name = result['test']
            if 'Authentication' in test_name:
                categories['Authentication'].append(result)
            elif 'Features' in test_name:
                categories['Core Features'].append(result)
            elif any(x in test_name for x in ['Interface', 'Main', 'Chat', 'Settings']):
                categories['Web Interface'].append(result)
            elif 'Tool:' in test_name:
                categories['Security Tools'].append(result)
            elif any(x in test_name for x in ['AI', 'Target Analysis', 'Tool Selection']):
                categories['AI Features'].append(result)
            elif any(x in test_name for x in ['Bug Bounty', 'CTF', 'Vulnerability Hunting', 'Recon']):
                categories['Workflow Features'].append(result)
            elif 'File' in test_name:
                categories['File Operations'].append(result)
            elif 'Database' in test_name:
                categories['Database Features'].append(result)
        
        print("\n📋 FEATURE CATEGORY BREAKDOWN:")
        for category, results in categories.items():
            if results:
                passed = sum(1 for r in results if r['success'])
                total = len(results)
                rate = (passed / total * 100) if total > 0 else 0
                status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
                print(f"   {status} {category}: {passed}/{total} ({rate:.1f}%)")
        
        # Save detailed results
        results_file = 'hexstrike_feature_test_results.json'
        with open(results_file, 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'success_rate': success_rate,
                    'timestamp': datetime.now().isoformat(),
                    'categories': {cat: {'passed': sum(1 for r in results if r['success']), 
                                        'total': len(results)} for cat, results in categories.items() if results}
                },
                'detailed_results': self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        # Application readiness assessment
        print("\n🎯 APPLICATION READINESS ASSESSMENT:")
        if success_rate >= 90:
            print("   ✅ EXCELLENT - Application is production-ready")
        elif success_rate >= 80:
            print("   ⚠️  GOOD - Application is mostly ready with minor issues")
        elif success_rate >= 70:
            print("   ⚠️  FAIR - Application needs some fixes before deployment")
        else:
            print("   ❌ POOR - Application needs significant fixes")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n" + "=" * 60)
        print("🎉 HexStrike AI Feature Testing Complete!")
        print("=" * 60)

def main():
    """Main test execution"""
    tester = HexStrikeFeatureTester()
    tester.run_comprehensive_test()
    
    # Exit with error code if critical tests failed
    failed_count = sum(1 for result in tester.test_results if not result['success'])
    sys.exit(1 if failed_count > 5 else 0)  # Allow some failures for non-critical features

if __name__ == "__main__":
    main()