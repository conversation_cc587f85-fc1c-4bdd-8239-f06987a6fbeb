﻿{
    "http_methods":  {
                         "TRACE":  403,
                         "PATCH":  501,
                         "OPTIONS":  501,
                         "HEAD":  200,
                         "POST":  403,
                         "GET":  200,
                         "DELETE":  501,
                         "security_assessment":  "Good - Dangerous methods properly restricted",
                         "PUT":  501
                     },
    "timestamp":  "2025-09-12 21:41:38",
    "ssl_analysis":  {
                         "certificate_valid":  true,
                         "status":  "SSL functional but missing HSTS",
                         "https_connection":  "Successful",
                         "hsts_header":  "Missing - Security Risk"
                     },
    "recommendations":  [
                            "Implement input validation",
                            "Add WAF protection",
                            "Enable HSTS header",
                            "Review application security controls"
                        ],
    "phase":  "Phase 3 - Baseline Scans",
    "target":  "https://example.com",
    "vulnerability_probes":  {
                                 "xss_test":  {
                                                  "risk":  "HIGH - Potential vulnerability",
                                                  "status":  200
                                              },
                                 "command_injection":  {
                                                           "risk":  "HIGH - Potential vulnerability",
                                                           "status":  200
                                                       },
                                 "overall_assessment":  "CRITICAL - Multiple potential vulnerabilities detected",
                                 "sql_injection":  {
                                                       "risk":  "HIGH - Potential vulnerability",
                                                       "status":  200
                                                   },
                                 "path_traversal":  {
                                                        "risk":  "HIGH - Potential vulnerability",
                                                        "status":  200
                                                    }
                             }
}
