<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Analysis - HexStrike AI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6 text-blue-700">Security Analysis Tools</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Tool 1 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="sec-vuln-assessment">
                <h2 class="text-xl font-semibold mb-3 text-blue-600">Vulnerability Assessment</h2>
                <p class="text-gray-700 mb-4">Comprehensive analysis of security vulnerabilities in systems and applications.</p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 2 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="sec-compliance-checker">
                <h2 class="text-xl font-semibold mb-3 text-blue-600">Compliance Checker</h2>
                <p class="text-gray-700 mb-4">Verify compliance with security standards like GDPR, HIPAA, PCI DSS, etc.</p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 3 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="sec-code-analyzer">
                <h2 class="text-xl font-semibold mb-3 text-blue-600">Code Security Analyzer</h2>
                <p class="text-gray-700 mb-4">Static and dynamic analysis of code for security vulnerabilities.</p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 4 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="sec-threat-modeling">
                <h2 class="text-xl font-semibold mb-3 text-blue-600">Threat Modeling</h2>
                <p class="text-gray-700 mb-4">Identify and analyze potential threats to systems and applications.</p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 5 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="sec-risk-assessment">
                <h2 class="text-xl font-semibold mb-3 text-blue-600">Risk Assessment</h2>
                <p class="text-gray-700 mb-4">Evaluate security risks and provide mitigation recommendations.</p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 6 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="sec-incident-response">
                <h2 class="text-xl font-semibold mb-3 text-blue-600">Incident Response</h2>
                <p class="text-gray-700 mb-4">Plan and simulate security incident response procedures.</p>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
        </div>
        
        <div class="mt-8">
            <a href="/" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script>
        // Tool functionality would be implemented here
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Security Analysis Tools Loaded');
            
            // Add event listeners to buttons
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    const toolId = this.closest('[data-tool-id]').dataset.toolId;
                    console.log(`Launching tool: ${toolId}`);
                    alert(`Tool ${toolId} would launch here in a production environment`);
                });
            });
        });
    </script>
</body>
</html>