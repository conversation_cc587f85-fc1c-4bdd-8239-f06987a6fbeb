/**
 * Chat Box AI - Modular Landing Page Styles
 * Modern, responsive, and accessible design system
 */

/* CSS Custom Properties (Brand Colors) */
:root {
    /* Primary Brand Colors */
    --color-primary-50: #fef2f2;
    --color-primary-100: #fee2e2;
    --color-primary-200: #fecaca;
    --color-primary-300: #fca5a5;
    --color-primary-400: #f87171;
    --color-primary-500: #ef4444;
    --color-primary-600: #dc2626;
    --color-primary-700: #b91c1c;
    --color-primary-800: #991b1b;
    --color-primary-900: #7f1d1d;
    
    /* Secondary Colors */
    --color-secondary-500: #ff5252;
    --color-tertiary-500: #ff8a80;
    
    /* Neutral Colors */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2d0000;
    --bg-slide: #1a0000;
    
    /* Text Colors */
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-light: #fffde7;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Dark Theme */
.dark {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
}

/* RTL Support */
[dir="rtl"] {
    direction: rtl;
}

[dir="rtl"] .rtl\:text-right {
    text-align: right;
}

[dir="rtl"] .rtl\:text-left {
    text-align: left;
}

[dir="rtl"] .rtl\:mr-4 {
    margin-right: 1rem;
    margin-left: 0;
}

[dir="rtl"] .rtl\:ml-4 {
    margin-left: 1rem;
    margin-right: 0;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: 3rem;
    color: var(--color-primary-700);
}

h2 {
    font-size: 2.5rem;
    color: var(--color-primary-600);
}

h3 {
    font-size: 2rem;
    color: var(--color-primary-600);
}

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Navigation */
.nav-link {
    @apply text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-medium;
}

/* Buttons */
.btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500;
}

.btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500;
}

.btn-outline {
    @apply border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
}

/* Cards */
.card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-xl;
}

.card-header {
    @apply p-6 border-b border-gray-200 dark:border-gray-700;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply p-6 border-t border-gray-200 dark:border-gray-700;
}

/* Section Containers */
.section-container {
    @apply py-16 px-4 sm:px-6 lg:px-8 transition-all duration-500;
}

.section-container.hidden {
    @apply opacity-0 transform scale-95 pointer-events-none;
}

/* Template Control Panel */
#templateControlPanel {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#templateControlPanel.open {
    transform: translateX(0);
}

.template-selector {
    @apply bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100;
}

.toggle-section {
    @apply transition-all duration-200;
}

.toggle-section.off {
    @apply bg-red-500 hover:bg-red-600;
}

.toggle-section.on {
    @apply bg-green-500 hover:bg-green-600;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* Accordion Styles */
.accordion-item {
    @apply border border-gray-200 dark:border-gray-700 rounded-lg mb-2 overflow-hidden;
}

.accordion-header {
    @apply w-full text-left p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 flex justify-between items-center;
}

.accordion-content {
    @apply p-4 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.accordion-content.open {
    max-height: 1000px;
}

/* Grid Layouts */
.feature-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

.pricing-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

.testimonial-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    h3 {
        font-size: 1.5rem;
    }
    
    .section-container {
        @apply py-12;
    }
    
    #templateControlPanel {
        @apply max-w-full left-4 right-4 top-20;
    }
}

/* Loading States */
.loading {
    @apply opacity-50 pointer-events-none;
}

.loading::after {
    content: '';
    @apply absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center;
    background-image: url('data:image/svg+xml;base64,PHN2ZyBhbmltYXRpb249InNwaW4gMXMgbGluZWFyIGluZmluaXRlIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJWNk0xMiAxOFYyMk02IDEySDJNMjIgMTJIMThNMTkuMDcgMTkuMDdMMTYuMjQgMTYuMjRNNy43NiA3Ljc2TDQuOTMgNC45M00xOS4wNyA0LjkzTDE2LjI0IDcuNzZNNy43NiAxNi4yNEw0LjkzIDE5LjA3IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+');
    background-repeat: no-repeat;
    background-position: center;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-500));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-500));
}

.glass {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}

.dark ::-webkit-scrollbar-track {
    background: var(--color-gray-800);
}

.dark ::-webkit-scrollbar-thumb {
    background: var(--color-gray-600);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}

/* Focus States */
.focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Print Styles */
@media print {
    #templateControlPanel,
    #panelToggle {
        display: none !important;
    }
    
    .section-container {
        page-break-inside: avoid;
    }
}