:root {
    --primary-color: #6200ea;
    --secondary-color: #03dac6;
    --background-color: #121212;
    --surface-color: #1e1e1e;
    --text-color: #ffffff;
    --error-color: #cf6679;
    --success-color: #00c853;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

header {
    background-color: var(--surface-color);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo img {
    height: 40px;
}

.logo h1 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--secondary-color);
}

nav ul {
    display: flex;
    list-style: none;
    gap: 20px;
    margin: 0;
    padding: 0;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.hero {
    padding: 4rem 0;
    text-align: center;
    background: linear-gradient(135deg, var(--surface-color), var(--background-color));
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.hero p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 2rem;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.cta-button:hover {
    background-color: #7c4dff;
}

.features {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 2rem;
    color: var(--secondary-color);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card h3 {
    color: var(--secondary-color);
    margin-top: 0;
}

.status-section {
    background-color: var(--surface-color);
    padding: 3rem 0;
    margin-top: 2rem;
}

.status-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2rem;
}

.status-item {
    text-align: center;
    flex: 1;
    min-width: 200px;
}

.status-item h3 {
    color: var(--secondary-color);
}

.status-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.operational {
    color: var(--success-color);
}

footer {
    background-color: var(--surface-color);
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-column {
    flex: 1;
    min-width: 200px;
}

.footer-column h3 {
    color: var(--secondary-color);
    margin-top: 0;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column li {
    margin-bottom: 0.5rem;
}

.footer-column a {
    color: var(--text-color);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-column a:hover {
    opacity: 1;
}

.copyright {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    nav ul {
        flex-direction: column;
        align-items: center;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
}