/**
 * Chat Box AI Chat Interface
 * Advanced UI/UX functionality for the chat window interface
 */

// DOM Elements - will be initialized after DOM is ready
let elements = {};

// State management
const state = {
  darkMode: localStorage.getItem('darkMode') === 'true' || 
           (localStorage.getItem('darkMode') === null && 
            window.matchMedia('(prefers-color-scheme: dark)').matches),
  isLoggedIn: false,
  isProcessRunning: false,
  isPaused: false,
  autoLogoutTimer: null,
  chatHistory: [],
  requestQueue: [],
  currentEnvironment: 'default',
  savedEnvironments: {},
  activeRole: 'assistant',
  conversationId: 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
  roles: {
    assistant: { name: 'General Assistant', icon: 'robot' },
    coder: { name: 'Code Expert', icon: 'code' },
    security: { name: 'Security Analyst', icon: 'shield-alt' },
    explainer: { name: 'Explainer', icon: 'chalkboard-teacher' }
  }
};

// Constants
const AUTO_LOGOUT_TIME = 30 * 60 * 1000; // 30 minutes
const MAX_CHAT_HISTORY = 50;
const MAX_QUEUE_SIZE = 10;

// Make constants available globally
window.AUTO_LOGOUT_TIME = AUTO_LOGOUT_TIME;

/**
 * Initialize the application
 */
function initApp() {
  // Initialize DOM elements after DOM is ready with error handling
  const safeGetElement = (id) => {
    const element = document.getElementById(id);
    if (!element) {
      console.warn(`Element with ID '${id}' not found in DOM`);
    }
    return element;
  };
  
  elements = {
    // Main panels
    chatPanel: safeGetElement('chatPanel'),
    logsPanel: safeGetElement('logsPanel'),
    
    // Chat elements
    chatMessages: safeGetElement('chatMessages'),
    messageInput: safeGetElement('messageInput'),
    sendBtn: safeGetElement('sendBtn'),
    uploadBtn: safeGetElement('uploadBtn'),
    
    // Process controls
    pauseBtn: safeGetElement('pauseBtn'),
    stopBtn: safeGetElement('stopBtn'),
    changeBtn: safeGetElement('changeBtn'),
    newChatBtn: safeGetElement('newChatBtn'),
    
    // Parameters section
    toggleParamsBtn: safeGetElement('toggleParamsBtn'),
    paramsSection: safeGetElement('paramsSection'),
    saveParamsBtn: safeGetElement('saveParamsBtn'),
    
    // Logs and history
    logsOutput: safeGetElement('logsOutput'),
    exportLogsBtn: safeGetElement('exportLogsBtn'),
    clearLogsBtn: safeGetElement('clearLogsBtn'),
    
    // Modals and notifications
    errorModal: safeGetElement('errorModal'),
    notificationToast: safeGetElement('notificationToast'),
    closeToastBtn: safeGetElement('closeToastBtn'),
    
    // Theme toggles
    themeToggle: safeGetElement('themeToggle'),
    
    // Menus
    userMenuBtn: safeGetElement('userMenuBtn'),
    userMenu: safeGetElement('userMenu'),
    logoutBtn: safeGetElement('logoutBtn'),
    viewOptionsBtn: safeGetElement('viewOptionsBtn'),
    viewOptionsMenu: safeGetElement('viewOptionsMenu'),
  };
  
  // Set initial theme
  if (state.darkMode) {
    document.documentElement.classList.add('dark');
  }
  
  // Attach event listeners with error handling
  try {
    attachEventListeners();
  } catch (error) {
    console.error('Error in attachEventListeners:', error);
    console.error('Error stack:', error.stack);
    console.error('Error occurred at line:', error.lineNumber || 'unknown');
    alert('Critical error in attachEventListeners: ' + error.message);
  }
  
  // Initialize resizable panels
  initResizablePanels();
  
  // Load saved data from localStorage
  loadSavedData();
  
  // Initialize encryption for sensitive data
  initEncryption();
  
  // Check if user was previously logged in
  checkPreviousSession();
  
  console.log('Chat Box AI Chat Interface initialized');
}

/**
 * Attach all event listeners
 */
function attachEventListeners() {
  alert('attachEventListeners function called - v9');
  console.log('=== FUNCTION START - attachEventListeners v9 ===');
  
  try {
    console.log('attachEventListeners called - ULTRA SAFE MODE');
    console.log('Elements object:', elements);
    console.log('typeof elements:', typeof elements);
    
    // Add a try-catch around the entire function to catch any unexpected errors
    if (!elements || typeof elements !== 'object') {
      console.error('Elements object is not properly initialized:', elements);
      return;
    }
    
    // Check for missing elements
    const missingElements = [];
    for (const [key, value] of Object.entries(elements)) {
      if (!value) {
        missingElements.push(key);
      }
    }
    
    if (missingElements.length > 0) {
      console.warn('Missing elements:', missingElements);
      alert('Missing DOM elements: ' + missingElements.join(', '));
    }
  
    // Debug function to check all elements
    const debugElements = () => {
      console.log('=== DEBUGGING ALL ELEMENTS ===');
      for (const [key, value] of Object.entries(elements)) {
        console.log(`${key}:`, value, typeof value, value ? 'EXISTS' : 'NULL');
        if (value && typeof value === 'object') {
          console.log(`  - hasAddEventListener: ${typeof value.addEventListener}`);
        }
      }
      console.log('=== END DEBUG ===');
    };
    
    // Call debug function
    debugElements();
    
    // Helper function to safely attach event listeners
    const safeAttach = (element, event, handler, elementName) => {
      try {
        console.log(`Attempting to attach ${event} listener to ${elementName}:`, element);
        
        // Multiple layers of null/undefined checks
        if (!element) {
          console.warn(`${elementName} element is null or undefined`);
          return;
        }
        
        if (typeof element !== 'object') {
          console.warn(`${elementName} element is not an object:`, typeof element);
          return;
        }
        
        if (!element.addEventListener) {
          console.warn(`${elementName} element does not have addEventListener method`);
          return;
        }
        
        if (typeof element.addEventListener !== 'function') {
          console.warn(`${elementName} element.addEventListener is not a function:`, typeof element.addEventListener);
          return;
        }
        
        // Additional debug before calling addEventListener
        console.log(`About to call addEventListener on ${elementName}`);
        
        // Safe to call addEventListener now
        element.addEventListener(event, handler);
        console.log(`Successfully attached ${event} listener to ${elementName}`);
        
      } catch (error) {
        console.error(`Error attaching listener to ${elementName}:`, error);
        console.error(`Element details:`, {
          element: element,
          elementName: elementName,
          event: event,
          handler: handler
        });
      }
    };
    
    // Theme toggles
    safeAttach(elements.themeToggle, 'click', toggleDarkMode, 'themeToggle');
    
    // User menu
    safeAttach(elements.userMenuBtn, 'click', toggleUserMenu, 'userMenuBtn');
    
    // View options menu
    safeAttach(elements.viewOptionsBtn, 'click', toggleViewOptionsMenu, 'viewOptionsBtn');
    
    // Parameters section
    safeAttach(elements.toggleParamsBtn, 'click', toggleParamsSection, 'toggleParamsBtn');
    safeAttach(elements.saveParamsBtn, 'click', saveParameters, 'saveParamsBtn');
    
    // Login and authentication
    safeAttach(elements.logoutBtn, 'click', handleLogout, 'logoutBtn');
    
    // Process controls
    safeAttach(elements.pauseBtn, 'click', togglePauseProcess, 'pauseBtn');
    safeAttach(elements.stopBtn, 'click', stopProcess, 'stopBtn');
    safeAttach(elements.changeBtn, 'click', changeRequest, 'changeBtn');
    safeAttach(elements.newChatBtn, 'click', startNewChat, 'newChatBtn');
    
    // Chat functionality
    safeAttach(elements.sendBtn, 'click', sendMessage, 'sendBtn');
    safeAttach(elements.messageInput, 'keypress', handleMessageInputKeypress, 'messageInput');
    safeAttach(elements.uploadBtn, 'click', handleFileUpload, 'uploadBtn');
    
    // Logs management
    safeAttach(elements.exportLogsBtn, 'click', exportLogs, 'exportLogsBtn');
    safeAttach(elements.clearLogsBtn, 'click', clearLogs, 'clearLogsBtn');
    
    // Notification toast
    safeAttach(elements.closeToastBtn, 'click', hideNotification, 'closeToastBtn');
    
    // Error modal - with null checks
    const closeErrorBtn = document.getElementById('closeErrorBtn');
    const retryErrorBtn = document.getElementById('retryErrorBtn');
    const ignoreErrorBtn = document.getElementById('ignoreErrorBtn');
    
    if (closeErrorBtn) {
      safeAttach(closeErrorBtn, 'click', hideErrorModal, 'closeErrorBtn');
    }
    if (retryErrorBtn) {
      safeAttach(retryErrorBtn, 'click', retryAfterError, 'retryErrorBtn');
    }
    if (ignoreErrorBtn) {
      safeAttach(ignoreErrorBtn, 'click', ignoreError, 'ignoreErrorBtn');
    }
    
    // Close menus when clicking outside
    if (document && document.addEventListener) {
      document.addEventListener('click', handleOutsideClick);
    }
    
    // Reset auto-logout timer on user activity
    if (document && document.addEventListener) {
      ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, resetAutoLogoutTimer);
      });
    }
    
  } catch (error) {
    console.error('Error in attachEventListeners:', error);
    console.error('Error line number:', error.lineNumber || 'unknown');
    alert('Error attaching event listeners: ' + error.message);
  }
  
  console.log('=== FUNCTION END - attachEventListeners v9 ===');
}

/**
 * Initialize resizable panels
 */
function initResizablePanels() {
  let isResizing = false;
  const container = document.querySelector('.flex.h-screen');
  
  // Create a resizer element if it doesn't exist
  let resizer = document.querySelector('.gutter.gutter-horizontal');
  if (!resizer) {
    resizer = document.createElement('div');
    resizer.className = 'gutter gutter-horizontal w-1 cursor-col-resize';
    container.insertBefore(resizer, elements.logsPanel);
  }
  
  if (resizer && resizer.addEventListener) {
    resizer.addEventListener('mousedown', (e) => {
      isResizing = true;
      document.body.style.cursor = 'col-resize';
      if (document && document.addEventListener) {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', () => {
          isResizing = false;
          document.body.style.cursor = '';
          if (document && document.removeEventListener) {
             document.removeEventListener('mousemove', handleMouseMove);
           }
       
           // Save panel sizes to localStorage
           savePanelSizes();
         });
       }
     });
   }
  
  function handleMouseMove(e) {
    if (!isResizing) return;
    const containerWidth = container.offsetWidth;
    const percentage = (e.clientX / containerWidth) * 100;
    
    // Limit the minimum width
    if (percentage < 20 || percentage > 80) return;
    
    elements.chatPanel.style.width = `${percentage}%`;
    elements.logsPanel.style.width = `${100 - percentage}%`;
  }
  
  // Load saved panel sizes
  loadPanelSizes();
}

/**
 * Save panel sizes to localStorage
 */
function savePanelSizes() {
  const chatPanelWidth = elements.chatPanel.style.width;
  const logsPanelWidth = elements.logsPanel.style.width;
  
  if (chatPanelWidth && logsPanelWidth) {
    localStorage.setItem('chatPanelWidth', chatPanelWidth);
    localStorage.setItem('logsPanelWidth', logsPanelWidth);
  }
}

/**
 * Load panel sizes from localStorage
 */
function loadPanelSizes() {
  const chatPanelWidth = localStorage.getItem('chatPanelWidth');
  const logsPanelWidth = localStorage.getItem('logsPanelWidth');
  
  if (chatPanelWidth && logsPanelWidth) {
    elements.chatPanel.style.width = chatPanelWidth;
    elements.logsPanel.style.width = logsPanelWidth;
  } else {
    // Default sizes
    elements.chatPanel.style.width = '50%';
    elements.logsPanel.style.width = '50%';
  }
}

/**
 * Toggle dark mode
 */
function toggleDarkMode() {
  state.darkMode = !state.darkMode;
  document.documentElement.classList.toggle('dark');
  localStorage.setItem('darkMode', state.darkMode);
}

/**
 * Toggle user menu
 */
function toggleUserMenu() {
  elements.userMenu.classList.toggle('hidden');
}

/**
 * Toggle view options menu
 */
function toggleViewOptionsMenu() {
  elements.viewOptionsMenu.classList.toggle('hidden');
}

/**
 * Handle clicks outside of menus to close them
 */
function handleOutsideClick(e) {
  if (!elements.userMenuBtn.contains(e.target) && !elements.userMenu.contains(e.target)) {
    elements.userMenu.classList.add('hidden');
  }
  if (!elements.viewOptionsBtn.contains(e.target) && !elements.viewOptionsMenu.contains(e.target)) {
    elements.viewOptionsMenu.classList.add('hidden');
  }
}

/**
 * Toggle parameters section
 */
function toggleParamsSection() {
  const isExpanded = elements.paramsSection.style.maxHeight !== '0px';
  elements.paramsSection.style.maxHeight = isExpanded ? '0px' : '250px';
  elements.toggleParamsBtn.innerHTML = isExpanded ? 
    '<i class="fas fa-chevron-down"></i> Show Additional Parameters' : 
    '<i class="fas fa-chevron-up"></i> Hide Additional Parameters';
}

/**
 * Save parameters to current environment
 */
function saveParameters() {
  const params = {
    targetUrl: document.getElementById('targetUrl').value,
    scanType: document.getElementById('scanType').value,
    apiKey: encryptData(document.getElementById('apiKey').value),
    timeout: document.getElementById('timeout').value
  };
  
  // Save to current environment
  state.savedEnvironments[state.currentEnvironment] = params;
  localStorage.setItem('savedEnvironments', JSON.stringify(state.savedEnvironments));
  
  showNotification('Parameters Saved', 'Your parameters have been saved to the current environment.');
}

/**
 * Handle login form submission
 */
async function handleLogin(e) {
  e.preventDefault();
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const remember = document.getElementById('remember').checked;
  
  if (!username || !password) {
    showErrorModal('Login Error', 'Please enter both username and password.');
    return;
  }
  
  try {
    // Make API call to backend authentication endpoint
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: username,
        password: password
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.access_token) {
      // Create user object and store session
      const user = {
        username: username,
        role: data.role || 'user',
        email: data.email || `${username}@hexstrike.ai`,
        permissions: data.permissions || ['chat'],
        loginTime: new Date().toISOString(),
        sessionId: 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      };
      
      // Store session in sessionStorage with JWT tokens
      import('./js/auth.js').then(auth => {
        auth.storeUserSession(user, data.access_token, data.refresh_token);
      });
      
      state.isLoggedIn = true;
      elements.loginModal.classList.add('hidden');
      
      // Start auto-logout timer
      startAutoLogoutTimer();
      
      // Add log entry
      addLogEntry(`User ${username} logged in successfully`, 'success');
    } else {
      showErrorModal('Login Error', data.error || 'Invalid credentials');
    }
  } catch (error) {
    console.error('Login error:', error);
    showErrorModal('Login Error', 'Failed to connect to server. Please try again.');
  }
}

/**
 * Handle OAuth login
 */
function handleOAuthLogin() {
  // In a real app, you would redirect to OAuth provider
  // For demo purposes, we'll just simulate a successful login
  
  state.isLoggedIn = true;
  elements.loginModal.classList.add('hidden');
  
  // Start auto-logout timer
  startAutoLogoutTimer();
  
  // Add log entry
  addLogEntry('User logged in via OAuth', 'success');
}

/**
 * Handle logout
 */
async function handleLogout() {
  try {
    // Use auth module logout function
    const authModule = await import('./js/auth.js');
    await authModule.logout(true);
    
    state.isLoggedIn = false;
    
    // Add null checks before accessing classList
    if (elements.loginModal && elements.loginModal.classList) {
      elements.loginModal.classList.remove('hidden');
    }
    
    if (elements.userMenu && elements.userMenu.classList) {
      elements.userMenu.classList.add('hidden');
    }
    
    // Clear auto-logout timer
    clearAutoLogoutTimer();
    
    // Add log entry
    addLogEntry('User logged out', 'info');
  } catch (error) {
    console.error('Logout error:', error);
    // Fallback: clear session manually
    state.isLoggedIn = false;
    
    // Add null checks in the error handler as well
    if (elements.loginModal && elements.loginModal.classList) {
      elements.loginModal.classList.remove('hidden');
    }
    
    if (elements.userMenu && elements.userMenu.classList) {
      elements.userMenu.classList.add('hidden');
    }
    
    clearAutoLogoutTimer();
  }
}

/**
 * Check for previous session
 */
async function checkPreviousSession() {
  try {
    const authModule = await import('./js/auth.js');
    
    if (authModule.validateSession()) {
      const user = authModule.getCurrentUser();
      
      if (user) {
        state.isLoggedIn = true;
        elements.loginModal.classList.add('hidden');
        
        // Start auto-logout timer
        startAutoLogoutTimer();
        
        // Add log entry
        addLogEntry(`User ${user.username} session restored`, 'info');
      }
    }
  } catch (error) {
    console.error('Session validation error:', error);
    // Clear any invalid session data
    state.isLoggedIn = false;
    elements.loginModal.classList.remove('hidden');
  }
}

/**
 * Start auto-logout timer
 */
function startAutoLogoutTimer() {
  clearAutoLogoutTimer();
  state.autoLogoutTimer = setTimeout(() => {
    handleLogout();
    showNotification('Session Expired', 'You have been logged out due to inactivity.');
  }, AUTO_LOGOUT_TIME);
}

/**
 * Clear auto-logout timer
 */
function clearAutoLogoutTimer() {
  clearTimeout(state.autoLogoutTimer);
}

/**
 * Reset auto-logout timer on user activity
 */
function resetAutoLogoutTimer() {
  if (state.isLoggedIn) {
    startAutoLogoutTimer();
  }
}

/**
 * Toggle pause/resume process
 */
function togglePauseProcess() {
  state.isPaused = !state.isPaused;
  
  elements.pauseBtn.innerHTML = state.isPaused ? 
    '<i class="fas fa-play mr-1"></i> Resume' : 
    '<i class="fas fa-pause mr-1"></i> Pause';
  elements.pauseBtn.classList.toggle('bg-yellow-500');
  elements.pauseBtn.classList.toggle('bg-green-500');
  elements.pauseBtn.classList.toggle('hover:bg-yellow-600');
  elements.pauseBtn.classList.toggle('hover:bg-green-600');
  
  // Add log entry
  addLogEntry(`Process ${state.isPaused ? 'paused' : 'resumed'}`, state.isPaused ? 'warning' : 'info');
  
  // Show notification
  showNotification(state.isPaused ? 'Process Paused' : 'Process Resumed', 
                   state.isPaused ? 'The current process has been paused.' : 'The process has been resumed.');
}

/**
 * Stop the current process
 */
function stopProcess() {
  if (!state.isProcessRunning) return;
  
  // Show confirmation dialog
  showErrorModal('Confirm Stop', 'Are you sure you want to stop the current process? This action cannot be undone.', 
    () => {
      state.isProcessRunning = false;
      state.isPaused = false;
      
      // Reset pause button
      elements.pauseBtn.innerHTML = '<i class="fas fa-pause mr-1"></i> Pause';
      elements.pauseBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
      elements.pauseBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-600');
      
      // Add log entry
      addLogEntry('Process stopped by user', 'error');
      
      // Show notification
      showNotification('Process Stopped', 'The current process has been stopped.');
    }
  );
}

/**
 * Change the current request
 */
function changeRequest() {
  // Toggle parameters section to show it
  if (elements.paramsSection.style.maxHeight === '0px') {
    toggleParamsSection();
  }
  
  // Add log entry
  addLogEntry('User requested to change parameters', 'info');
  
  // Show notification
  showNotification('Change Request', 'You can modify your request parameters and resubmit.');
}

/**
 * Start a new chat
 */
function startNewChat() {
  // Clear chat messages
  elements.chatMessages.innerHTML = `
    <div class="flex items-start space-x-3 animate-fade-in">
      <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
        <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
      </div>
      <div class="flex-1">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
          <p class="text-sm">Hello! I'm Chat Box AI, your cybersecurity assistant. How can I help you today?</p>
        </div>
        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
          <span>${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
          <div class="flex space-x-2 ml-2">
            <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Copy to clipboard">
              <i class="fas fa-copy"></i>
            </button>
            <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Save response">
              <i class="fas fa-bookmark"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // Clear logs
  elements.logsOutput.querySelector('.space-y-1').innerHTML = `
    <div class="text-gray-500 dark:text-gray-400">[${new Date().toLocaleTimeString()}] Starting new session...</div>
    <div class="text-blue-600 dark:text-blue-400">[${new Date().toLocaleTimeString()}] INFO: Chat Box AI ready for new requests</div>
  `;
  
  // Reset state
  state.isProcessRunning = false;
  state.isPaused = false;
  
  // Reset pause button
  elements.pauseBtn.innerHTML = '<i class="fas fa-pause mr-1"></i> Pause';
  elements.pauseBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
  elements.pauseBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-600');
  
  // Clear chat history for current session
  state.chatHistory = [];
  
  // Add welcome message to chat history
  state.chatHistory.push({
    role: 'assistant',
    content: 'Hello! I\'m Chat Box AI, your cybersecurity assistant. How can I help you today?',
    timestamp: new Date().toISOString()
  });
  
  // Show notification
  showNotification('New Chat', 'Started a new chat session.');
}

/**
 * Send a message
 */
function sendMessage() {
  // Get elements dynamically in case they were recreated
  const messageInput = document.getElementById('messageInput');
  const chatMessages = document.getElementById('chatMessages');
  
  if (!messageInput || !chatMessages) {
    console.error('Required elements not found: messageInput or chatMessages');
    return;
  }
  
  const message = messageInput.value.trim();
  if (!message) return;
  
  // Add user message to chat
  const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  const userMessageHTML = `
    <div class="flex items-start space-x-3 animate-fade-in">
      <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-2">
        <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
      </div>
      <div class="flex-1">
        <div class="bg-blue-100 dark:bg-blue-900 rounded-lg p-3">
          <p class="text-sm">${escapeHTML(message)}</p>
        </div>
        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
          <span>${timestamp}</span>
        </div>
      </div>
    </div>
  `;
  chatMessages.insertAdjacentHTML('beforeend', userMessageHTML);
  messageInput.value = '';
  
  // Add to chat history
  state.chatHistory.push({
    role: 'user',
    content: message,
    timestamp: new Date().toISOString()
  });
  
  // Limit chat history size
  if (state.chatHistory.length > MAX_CHAT_HISTORY) {
    state.chatHistory.shift();
  }
  
  // Scroll to bottom
  chatMessages.scrollTop = chatMessages.scrollHeight;
  
  // Add log entry
  addLogEntry(`User message: ${message}`, 'info');
  
  // Set process as running
  state.isProcessRunning = true;
  
  // Simulate AI response after a delay
  setTimeout(() => {
    // Add AI typing indicator
    const aiTypingHTML = `
      <div class="flex items-start space-x-3 animate-fade-in" id="ai-typing">
        <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
          <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
        </div>
        <div class="flex-1">
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    `;
    chatMessages.insertAdjacentHTML('beforeend', aiTypingHTML);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Generate AI response
    setTimeout(async () => {
      // Remove typing indicator
      document.getElementById('ai-typing').remove();
      
      // Generate AI response based on user message
      const aiResponse = await generateAIResponse(message);
      
      // Add AI response to chat
      const aiMessageHTML = `
        <div class="flex items-start space-x-3 animate-fade-in">
          <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
            <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
          </div>
          <div class="flex-1">
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
              <p class="text-sm">${aiResponse}</p>
            </div>
            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
              <span>${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
              <div class="flex space-x-2 ml-2">
                <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Copy to clipboard" onclick="copyToClipboard('${escapeHTML(aiResponse)}')">
                  <i class="fas fa-copy"></i>
                </button>
                <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Save response">
                  <i class="fas fa-bookmark"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
      chatMessages.insertAdjacentHTML('beforeend', aiMessageHTML);
      chatMessages.scrollTop = chatMessages.scrollHeight;
      
      // Add to chat history
      state.chatHistory.push({
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date().toISOString()
      });
      
      // Add log entry
      addLogEntry(`AI response generated`, 'success');
      
      // Set process as not running
      state.isProcessRunning = false;
    }, 1500);
  }, 500);
}

/**
 * Generate AI response by calling the backend API
 */
async function generateAIResponse(message) {
  try {
    const authModule = await import('./js/auth.js');
    const user = authModule.getCurrentUser();
    
    // Use authenticated fetch with automatic token refresh
    const response = await authModule.authenticatedFetch('http://localhost:3000/api/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: message,
        conversation_id: state.conversationId || 'default',
        user_id: user?.username || 'demo_user'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data.response || 'Sorry, I encountered an error processing your request.';
  } catch (error) {
    console.error('Error calling backend API:', error);
    addLogEntry(`API Error: ${error.message}`, 'error');
    
    // Handle authentication errors
    if (error.message === 'Authentication required') {
      state.isLoggedIn = false;
      elements.loginModal.classList.remove('hidden');
      return 'Please log in to continue using the chat service.';
    }
    
    return 'Sorry, I\'m having trouble connecting to the backend service. Please check if the server is running.';
  }
}

/**
 * Make authenticated API call with proper error handling
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} API response data
 */
async function makeAuthenticatedAPICall(url, options = {}) {
  try {
    const authModule = await import('./js/auth.js');
    
    // Use authenticated fetch with automatic token refresh
    const response = await authModule.authenticatedFetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API call error:', error);
    
    // Handle authentication errors
    if (error.message === 'Authentication required') {
      state.isLoggedIn = false;
      elements.loginModal.classList.remove('hidden');
      addLogEntry('Session expired. Please log in again.', 'warning');
      throw new Error('Authentication required');
    }
    
    throw error;
  }
}

/**
 * Handle message input keypress
 */
function handleMessageInputKeypress(e) {
  if (e.key === 'Enter') {
    sendMessage();
  }
}

/**
 * Handle file upload
 */
function handleFileUpload() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.csv,.json,.txt,.pdf';
  input.onchange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Add log entry
      addLogEntry(`File uploaded: ${file.name} (${formatFileSize(file.size)})`, 'info');
      
      // Show notification
      showNotification('File Uploaded', `File "${file.name}" has been uploaded.`);
      
      // In a real app, you would handle the file upload here
      // For demo purposes, we'll just add a message about the file
      elements.messageInput.value = `I've uploaded ${file.name} for analysis.`;
    }
  };
  input.click();
}

/**
 * Format file size in human-readable format
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Add log entry to the logs output
 */
function addLogEntry(message, level = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  let className = '';
  let prefix = '';
  
  switch (level) {
    case 'info':
      className = 'text-blue-600 dark:text-blue-400';
      prefix = 'INFO';
      break;
    case 'success':
      className = 'text-green-600 dark:text-green-400';
      prefix = 'SUCCESS';
      break;
    case 'warning':
      className = 'text-yellow-600 dark:text-yellow-400';
      prefix = 'WARNING';
      break;
    case 'error':
      className = 'text-red-600 dark:text-red-400';
      prefix = 'ERROR';
      break;
    case 'critical':
      className = 'text-red-600 dark:text-red-400 font-bold';
      prefix = 'CRITICAL';
      break;
    default:
      className = 'text-gray-500 dark:text-gray-400';
      break;
  }
  
  const logEntry = `<div class="${className}">[${timestamp}] ${prefix}: ${escapeHTML(message)}</div>`;
  elements.logsOutput.querySelector('.space-y-1').insertAdjacentHTML('beforeend', logEntry);
  elements.logsOutput.scrollTop = elements.logsOutput.scrollHeight;
}

/**
 * Export logs to a file
 */
function exportLogs() {
  const logs = elements.logsOutput.innerText;
  const blob = new Blob([logs], {type: 'text/plain'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `chat_box_logs_${new Date().toISOString().slice(0,10)}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  // Show notification
  showNotification('Logs Exported', 'Logs have been exported as a text file.');
  
  // Add log entry
  addLogEntry('Logs exported to file', 'info');
}

/**
 * Clear logs
 */
function clearLogs() {
  // In a real app, you would confirm before clearing
  elements.logsOutput.querySelector('.space-y-1').innerHTML = `
    <div class="text-gray-500 dark:text-gray-400">[${new Date().toLocaleTimeString()}] Logs cleared</div>
  `;
  
  // Show notification
  showNotification('Logs Cleared', 'All logs have been cleared.');
  
  // Add log entry
  addLogEntry('Logs cleared by user', 'info');
}

/**
 * Show notification toast
 */
function showNotification(title, message) {
  const notificationTitle = elements.notificationToast.querySelector('h4');
  const notificationMessage = elements.notificationToast.querySelector('p');
  
  notificationTitle.textContent = title;
  notificationMessage.textContent = message;
  elements.notificationToast.classList.remove('hidden');
  
  // Auto-hide after 5 seconds
  setTimeout(() => {
    hideNotification();
  }, 5000);
}

/**
 * Hide notification toast
 */
function hideNotification() {
  elements.notificationToast.classList.add('hidden');
}

/**
 * Show error modal
 */
function showErrorModal(title, message, onRetry = null) {
  const errorTitle = elements.errorModal.querySelector('h2');
  const errorMessage = document.getElementById('errorMessage');
  const retryBtn = document.getElementById('retryErrorBtn');
  
  errorTitle.textContent = title;
  errorMessage.textContent = message;
  elements.errorModal.classList.remove('hidden');
  
  // Set custom retry handler if provided
  if (onRetry) {
    const originalHandler = retryBtn.onclick;
    retryBtn.onclick = () => {
      elements.errorModal.classList.add('hidden');
      onRetry();
      // Restore original handler
      retryBtn.onclick = originalHandler;
    };
  }
}

/**
 * Hide error modal
 */
function hideErrorModal() {
  elements.errorModal.classList.add('hidden');
}

/**
 * Retry after error
 */
function retryAfterError() {
  elements.errorModal.classList.add('hidden');
  showNotification('Retry', 'Retrying the operation...');
  
  // Add log entry
  addLogEntry('Retrying operation after error', 'info');
  
  // In a real app, you would retry the operation here
}

/**
 * Ignore error
 */
function ignoreError() {
  elements.errorModal.classList.add('hidden');
  showNotification('Ignored', 'Error has been ignored. Continuing with the process.');
  
  // Add log entry
  addLogEntry('Error ignored by user', 'warning');
  
  // In a real app, you would continue the process here
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    showNotification('Copied', 'Text copied to clipboard.');
  }).catch(err => {
    console.error('Failed to copy text: ', err);
    showNotification('Copy Failed', 'Failed to copy text to clipboard.');
  });
}

/**
 * Initialize encryption for sensitive data
 * In a real app, you would use a proper encryption library
 */
function initEncryption() {
  // Simple encryption for demo purposes
  // In a real app, use a proper encryption library
}

/**
 * Encrypt sensitive data
 * This is a very basic implementation for demo purposes
 */
function encryptData(data) {
  // In a real app, use a proper encryption method
  return btoa(data);
}

/**
 * Decrypt sensitive data
 * This is a very basic implementation for demo purposes
 */
function decryptData(data) {
  // In a real app, use a proper decryption method
  try {
    return atob(data);
  } catch (e) {
    console.error('Decryption failed:', e);
    return 'decryption_failed';
  }
}

/**
 * Load saved data from localStorage
 */
function loadSavedData() {
  // Load saved environments
  const savedEnvironments = localStorage.getItem('savedEnvironments');
  if (savedEnvironments) {
    try {
      state.savedEnvironments = JSON.parse(savedEnvironments);
    } catch (e) {
      console.error('Failed to parse saved environments:', e);
      state.savedEnvironments = {};
    }
  }
  
  // Load chat history
  const chatHistory = localStorage.getItem('chatHistory');
  if (chatHistory) {
    try {
      state.chatHistory = JSON.parse(chatHistory);
    } catch (e) {
      console.error('Failed to parse chat history:', e);
      state.chatHistory = [];
    }
  }
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHTML(str) {
  return str.replace(/[&<>"']/g, (match) => {
    return {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    }[match];
  });
}

// Initialize the application when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initApp);

// Function to reattach event listeners for dynamically created elements
function reattachChatEventListeners() {
  const sendBtn = document.getElementById('sendBtn');
  const messageInput = document.getElementById('messageInput');
  const uploadBtn = document.getElementById('uploadBtn');
  
  if (sendBtn) {
    // Remove existing listeners to avoid duplicates
    sendBtn.replaceWith(sendBtn.cloneNode(true));
    const newSendBtn = document.getElementById('sendBtn');
    if (newSendBtn && newSendBtn.addEventListener) {
      newSendBtn.addEventListener('click', sendMessage);
    }
  }
  
  if (messageInput) {
    // Remove existing listeners to avoid duplicates
    messageInput.replaceWith(messageInput.cloneNode(true));
    const newMessageInput = document.getElementById('messageInput');
    if (newMessageInput && newMessageInput.addEventListener) {
      newMessageInput.addEventListener('keypress', handleMessageInputKeypress);
    }
  }
  
  if (uploadBtn) {
    // Remove existing listeners to avoid duplicates
    uploadBtn.replaceWith(uploadBtn.cloneNode(true));
    const newUploadBtn = document.getElementById('uploadBtn');
    if (newUploadBtn && newUploadBtn.addEventListener) {
      newUploadBtn.addEventListener('click', handleFileUpload);
    }
  }
}

// Make functions globally accessible
window.copyToClipboard = copyToClipboard;
window.sendMessage = sendMessage;
window.handleMessageInputKeypress = handleMessageInputKeypress;
window.handleFileUpload = handleFileUpload;
window.reattachChatEventListeners = reattachChatEventListeners;

// Initialize server routes
function initRoutes() {
  // Serve static files
  const serveStaticFile = (path, contentType = 'text/html') => {
    return (req, res) => {
      fs.readFile(path, (err, data) => {
        if (err) {
          res.writeHead(404);
          res.end('File not found');
          return;
        }
        res.writeHead(200, {'Content-Type': contentType});
        res.end(data);
      });
    };
  };
  
  // Add routes for penetration testing and security analysis pages
  app.get('/penetration-testing', serveStaticFile('./penetration-testing.html'));
  app.get('/security-analysis', serveStaticFile('./security-analysis.html'));
  
  // Log all registered routes
  console.log('Routes registered successfully');
}