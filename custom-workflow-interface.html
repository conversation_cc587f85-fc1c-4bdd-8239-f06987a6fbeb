<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Custom Workflow</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --accent-color: #818cf8;
            --background-color: #f9fafb;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
            --border-color: #e5e7eb;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
        }

        .dark-mode {
            --primary-color: #818cf8;
            --secondary-color: #6366f1;
            --accent-color: #4f46e5;
            --background-color: #111827;
            --card-bg: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #e5e7eb;
            --border-color: #374151;
        }

        .container-fluid {
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .logo i {
            font-size: 1.75rem;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 1rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .workflow-builder {
            min-height: 400px;
            border: 2px dashed var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .workflow-step {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .workflow-step:hover {
            border-color: var(--primary-color);
        }

        .workflow-step .step-actions {
            display: flex;
            gap: 0.5rem;
        }

        .workflow-step .step-actions button {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.875rem;
        }

        .workflow-step .step-actions button:hover {
            color: var(--primary-color);
        }

        .workflow-tools {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .tool-item {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }

        .tool-item:hover {
            border-color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.05);
        }

        .tool-item i {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .form-control, .form-select {
            border-color: var(--border-color);
            color: var(--text-primary);
            background-color: var(--card-bg);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        .connection-line {
            stroke: var(--primary-color);
            stroke-width: 2;
        }

        .toggle-theme {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 1.25rem;
        }

        .toggle-theme:hover {
            color: var(--primary-color);
        }

        .workflow-canvas {
            position: relative;
            min-height: 500px;
            background-color: var(--card-bg);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .workflow-node {
            position: absolute;
            width: 200px;
            background-color: var(--card-bg);
            border: 2px solid var(--primary-color);
            border-radius: 0.5rem;
            padding: 1rem;
            cursor: move;
        }

        .node-handle {
            width: 12px;
            height: 12px;
            background-color: var(--primary-color);
            border-radius: 50%;
            position: absolute;
        }

        .node-handle.input {
            top: 50%;
            left: -6px;
            transform: translateY(-50%);
        }

        .node-handle.output {
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
        }

        .workflow-templates {
            margin-bottom: 2rem;
        }

        .template-card {
            cursor: pointer;
            height: 100%;
        }

        .template-card .card-body {
            display: flex;
            flex-direction: column;
        }

        .template-card .template-description {
            flex-grow: 1;
        }

        .workflow-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }

        .workflow-actions .btn-group {
            display: flex;
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="header">
            <div class="logo">
                <i class="fas fa-project-diagram"></i>
                <span>HexStrike AI - Custom Workflow</span>
            </div>
            <div class="d-flex align-items-center gap-3">
                <button class="toggle-theme" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                <a href="main-capability-selector.html" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Workflow Configuration
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="workflowName" class="form-label">Workflow Name</label>
                                <input type="text" class="form-control" id="workflowName" placeholder="Enter workflow name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workflowType" class="form-label">Workflow Type</label>
                                <select class="form-select" id="workflowType">
                                    <option selected>Select workflow type</option>
                                    <option value="security">Security Analysis</option>
                                    <option value="data">Data Processing</option>
                                    <option value="automation">Task Automation</option>
                                    <option value="integration">System Integration</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="triggerType" class="form-label">Trigger Type</label>
                                <select class="form-select" id="triggerType">
                                    <option selected>Select trigger type</option>
                                    <option value="manual">Manual Execution</option>
                                    <option value="scheduled">Scheduled</option>
                                    <option value="event">Event-based</option>
                                    <option value="api">API Call</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="outputFormat" class="form-label">Output Format</label>
                                <select class="form-select" id="outputFormat">
                                    <option selected>Select output format</option>
                                    <option value="json">JSON</option>
                                    <option value="csv">CSV</option>
                                    <option value="html">HTML Report</option>
                                    <option value="pdf">PDF Report</option>
                                    <option value="dashboard">Interactive Dashboard</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12 col-lg-3">
                <div class="card h-100">
                    <div class="card-header">
                        Workflow Components
                    </div>
                    <div class="card-body">
                        <div class="workflow-tools">
                            <div class="tool-item" draggable="true" data-type="input">
                                <i class="fas fa-sign-in-alt"></i>
                                <div>Input Source</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="process">
                                <i class="fas fa-cogs"></i>
                                <div>Process</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="decision">
                                <i class="fas fa-code-branch"></i>
                                <div>Decision</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="output">
                                <i class="fas fa-sign-out-alt"></i>
                                <div>Output</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="api">
                                <i class="fas fa-plug"></i>
                                <div>API Call</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="transform">
                                <i class="fas fa-exchange-alt"></i>
                                <div>Transform</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="filter">
                                <i class="fas fa-filter"></i>
                                <div>Filter</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="loop">
                                <i class="fas fa-redo"></i>
                                <div>Loop</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="delay">
                                <i class="fas fa-clock"></i>
                                <div>Delay</div>
                            </div>
                            <div class="tool-item" draggable="true" data-type="notification">
                                <i class="fas fa-bell"></i>
                                <div>Notification</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-9">
                <div class="workflow-actions">
                    <div class="btn-group">
                        <button class="btn btn-primary" id="saveWorkflow">
                            <i class="fas fa-save me-2"></i>Save Workflow
                        </button>
                        <button class="btn btn-outline-primary" id="testWorkflow">
                            <i class="fas fa-play me-2"></i>Test Workflow
                        </button>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="clearCanvas">
                            <i class="fas fa-trash me-2"></i>Clear Canvas
                        </button>
                        <button class="btn btn-outline-primary" id="exportWorkflow">
                            <i class="fas fa-file-export me-2"></i>Export
                        </button>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        Workflow Builder
                    </div>
                    <div class="card-body p-0">
                        <div class="workflow-canvas" id="workflowCanvas">
                            <!-- Workflow nodes will be added here dynamically -->
                            <div class="workflow-node" style="top: 50px; left: 50px;">
                                <div class="node-handle input"></div>
                                <div class="node-handle output"></div>
                                <h6>Input Source</h6>
                                <p class="mb-0 text-secondary">Data Entry Point</p>
                            </div>
                            <div class="workflow-node" style="top: 50px; left: 300px;">
                                <div class="node-handle input"></div>
                                <div class="node-handle output"></div>
                                <h6>Process</h6>
                                <p class="mb-0 text-secondary">Data Transformation</p>
                            </div>
                            <div class="workflow-node" style="top: 200px; left: 300px;">
                                <div class="node-handle input"></div>
                                <div class="node-handle output"></div>
                                <h6>Output</h6>
                                <p class="mb-0 text-secondary">Results Display</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Workflow Templates
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card template-card">
                                    <div class="card-body">
                                        <h5 class="card-title">Security Scan Workflow</h5>
                                        <p class="card-text template-description">Automated security scanning with vulnerability assessment and reporting.</p>
                                        <button class="btn btn-sm btn-outline-primary">Use Template</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card template-card">
                                    <div class="card-body">
                                        <h5 class="card-title">Data Processing Pipeline</h5>
                                        <p class="card-text template-description">Extract, transform, and load data with validation and error handling.</p>
                                        <button class="btn btn-sm btn-outline-primary">Use Template</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card template-card">
                                    <div class="card-body">
                                        <h5 class="card-title">API Integration Flow</h5>
                                        <p class="card-text template-description">Connect multiple APIs with authentication and data mapping.</p>
                                        <button class="btn btn-sm btn-outline-primary">Use Template</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggling
            const themeToggle = document.getElementById('themeToggle');
            const body = document.body;
            const themeIcon = themeToggle.querySelector('i');

            themeToggle.addEventListener('click', function() {
                body.classList.toggle('dark-mode');
                if (body.classList.contains('dark-mode')) {
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                } else {
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                }
            });

            // Workflow node dragging functionality
            const workflowNodes = document.querySelectorAll('.workflow-node');
            const workflowCanvas = document.getElementById('workflowCanvas');

            workflowNodes.forEach(node => {
                let isDragging = false;
                let offsetX, offsetY;

                node.addEventListener('mousedown', function(e) {
                    isDragging = true;
                    offsetX = e.clientX - node.getBoundingClientRect().left;
                    offsetY = e.clientY - node.getBoundingClientRect().top;
                    node.style.zIndex = 1000;
                });

                document.addEventListener('mousemove', function(e) {
                    if (!isDragging) return;
                    
                    const canvasRect = workflowCanvas.getBoundingClientRect();
                    let newX = e.clientX - canvasRect.left - offsetX;
                    let newY = e.clientY - canvasRect.top - offsetY;
                    
                    // Ensure the node stays within the canvas
                    newX = Math.max(0, Math.min(newX, canvasRect.width - node.offsetWidth));
                    newY = Math.max(0, Math.min(newY, canvasRect.height - node.offsetHeight));
                    
                    node.style.left = newX + 'px';
                    node.style.top = newY + 'px';
                });

                document.addEventListener('mouseup', function() {
                    if (isDragging) {
                        isDragging = false;
                        node.style.zIndex = '';
                    }
                });
            });

            // Draggable workflow tools
            const toolItems = document.querySelectorAll('.tool-item');
            
            toolItems.forEach(tool => {
                tool.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', tool.dataset.type);
                });
            });

            workflowCanvas.addEventListener('dragover', function(e) {
                e.preventDefault();
            });

            workflowCanvas.addEventListener('drop', function(e) {
                e.preventDefault();
                const type = e.dataTransfer.getData('text/plain');
                if (!type) return;
                
                const canvasRect = workflowCanvas.getBoundingClientRect();
                const x = e.clientX - canvasRect.left;
                const y = e.clientY - canvasRect.top;
                
                addWorkflowNode(type, x, y);
            });

            function addWorkflowNode(type, x, y) {
                const node = document.createElement('div');
                node.className = 'workflow-node';
                node.style.top = (y - 30) + 'px';
                node.style.left = (x - 100) + 'px';
                
                const inputHandle = document.createElement('div');
                inputHandle.className = 'node-handle input';
                
                const outputHandle = document.createElement('div');
                outputHandle.className = 'node-handle output';
                
                const title = document.createElement('h6');
                const description = document.createElement('p');
                description.className = 'mb-0 text-secondary';
                
                switch(type) {
                    case 'input':
                        title.textContent = 'Input Source';
                        description.textContent = 'Data Entry Point';
                        break;
                    case 'process':
                        title.textContent = 'Process';
                        description.textContent = 'Data Transformation';
                        break;
                    case 'decision':
                        title.textContent = 'Decision';
                        description.textContent = 'Conditional Logic';
                        break;
                    case 'output':
                        title.textContent = 'Output';
                        description.textContent = 'Results Display';
                        break;
                    case 'api':
                        title.textContent = 'API Call';
                        description.textContent = 'External Service';
                        break;
                    case 'transform':
                        title.textContent = 'Transform';
                        description.textContent = 'Data Conversion';
                        break;
                    case 'filter':
                        title.textContent = 'Filter';
                        description.textContent = 'Data Filtering';
                        break;
                    case 'loop':
                        title.textContent = 'Loop';
                        description.textContent = 'Iteration';
                        break;
                    case 'delay':
                        title.textContent = 'Delay';
                        description.textContent = 'Time Pause';
                        break;
                    case 'notification':
                        title.textContent = 'Notification';
                        description.textContent = 'Alert System';
                        break;
                    default:
                        title.textContent = 'Custom Node';
                        description.textContent = 'Custom Logic';
                }
                
                node.appendChild(inputHandle);
                node.appendChild(outputHandle);
                node.appendChild(title);
                node.appendChild(description);
                
                workflowCanvas.appendChild(node);
                
                // Make the new node draggable
                let isDragging = false;
                let offsetX, offsetY;

                node.addEventListener('mousedown', function(e) {
                    isDragging = true;
                    offsetX = e.clientX - node.getBoundingClientRect().left;
                    offsetY = e.clientY - node.getBoundingClientRect().top;
                    node.style.zIndex = 1000;
                });

                document.addEventListener('mousemove', function(e) {
                    if (!isDragging) return;
                    
                    const canvasRect = workflowCanvas.getBoundingClientRect();
                    let newX = e.clientX - canvasRect.left - offsetX;
                    let newY = e.clientY - canvasRect.top - offsetY;
                    
                    // Ensure the node stays within the canvas
                    newX = Math.max(0, Math.min(newX, canvasRect.width - node.offsetWidth));
                    newY = Math.max(0, Math.min(newY, canvasRect.height - node.offsetHeight));
                    
                    node.style.left = newX + 'px';
                    node.style.top = newY + 'px';
                });

                document.addEventListener('mouseup', function() {
                    if (isDragging) {
                        isDragging = false;
                        node.style.zIndex = '';
                    }
                });
            }

            // Button functionality
            document.getElementById('clearCanvas').addEventListener('click', function() {
                if (confirm('Are you sure you want to clear the workflow canvas? This action cannot be undone.')) {
                    const nodes = workflowCanvas.querySelectorAll('.workflow-node');
                    nodes.forEach(node => node.remove());
                }
            });

            document.getElementById('saveWorkflow').addEventListener('click', function() {
                alert('Workflow saved successfully!');
            });

            document.getElementById('testWorkflow').addEventListener('click', function() {
                alert('Workflow test started. Check the console for results.');
                console.log('Workflow test initiated');
            });

            document.getElementById('exportWorkflow').addEventListener('click', function() {
                const workflowName = document.getElementById('workflowName').value || 'custom-workflow';
                alert(`Workflow exported as ${workflowName}.json`);
            });
        });
    </script>
</body>
</html>