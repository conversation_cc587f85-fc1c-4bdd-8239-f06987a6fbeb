<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - AI Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --secondary: #764ba2;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #3b82f6;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e5e7eb;
            --bg-accent: #f0f4ff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --border: #d1d5db;
            --border-light: #e5e7eb;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
            --radius: 8px;
            --radius-xl: 12px;
            --radius-2xl: 16px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
        }

        .ai-guide {
            max-width: 400px;
            margin: 0 auto;
            position: sticky;
            top: 20px;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 700;
            flex: 1;
        }

        .ai-status {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .card-content {
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .suggestion-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .chip {
            background: var(--bg-accent);
            border: 1px solid rgba(102, 126, 234, 0.2);
            color: var(--primary);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .chip:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-1px);
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
            max-height: 400px;
            overflow-y: auto;
        }

        .message {
            display: flex;
            gap: 12px;
            align-items: flex-start;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .message.user .message-avatar {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }

        .message-content {
            background: var(--bg-secondary);
            padding: 12px 16px;
            border-radius: var(--radius-xl);
            max-width: 280px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: var(--primary);
            color: white;
        }

        .message.assistant .message-content {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
        }

        .recommendation {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--radius-xl);
            padding: 16px;
            margin: 12px 0;
        }

        .recommendation-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            font-weight: 600;
            color: #059669;
        }

        .recommendation-list {
            list-style: none;
            margin: 8px 0;
        }

        .recommendation-list li {
            padding: 4px 0;
            font-size: 13px;
            color: var(--text-secondary);
        }

        .recommendation-list li:before {
            content: "✓";
            color: var(--success);
            margin-right: 8px;
            font-weight: bold;
        }

        .apply-btn {
            background: var(--success);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--radius);
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 8px;
        }

        .apply-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: var(--radius-xl);
            padding: 12px 16px;
            margin: 8px 0;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .warning-icon {
            color: var(--warning);
            font-size: 16px;
            margin-top: 2px;
        }

        .warning-text {
            font-size: 13px;
            color: #92400e;
            line-height: 1.4;
        }

        .chat-input-container {
            display: flex;
            gap: 8px;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid var(--border-light);
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid var(--border);
            border-radius: var(--radius-xl);
            font-size: 14px;
            transition: all 0.2s ease;
            background: var(--bg-primary);
        }

        .chat-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
        }

        .send-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: var(--radius-xl);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .send-btn:hover {
            background: var(--primary-dark);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--bg-tertiary);
            border-radius: var(--radius);
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--text-muted);
        }

        .status-dot.active {
            background: var(--success);
        }

        .summary-card {
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: var(--radius-xl);
            padding: 16px;
            margin: 12px 0;
        }

        .summary-header {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin: 8px 0;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: var(--radius);
            border: 1px solid var(--border-light);
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            color: var(--primary);
        }

        .stat-label {
            font-size: 11px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media (max-width: 480px) {
            .ai-guide {
                max-width: 100%;
                position: static;
            }

            .card-content {
                padding: 16px;
            }

            .chat-container {
                max-height: 300px;
            }

            .message-content {
                max-width: 220px;
            }

            .summary-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="ai-guide" id="aiGuide">
        <div class="card">
            <div class="card-header">
                <span class="card-title">🤖 AI Guide</span>
                <div class="ai-status" id="aiStatus"></div>
            </div>
            
            <div class="card-content">
                <div class="status-indicator" id="statusIndicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Waiting for target site configuration...</span>
                </div>

                <div class="suggestion-chips" id="suggestionChips">
                    <button class="chip" data-question="What do I need for login-only pages?">
                        What do I need for login-only pages?
                    </button>
                    <button class="chip" data-question="Which security tests fit web apps?">
                        Which security tests fit web apps?
                    </button>
                    <button class="chip" data-question="Best export format for stakeholders?">
                        Best export format for stakeholders?
                    </button>
                    <button class="chip" data-question="How to speed up large crawls?">
                        How to speed up large crawls?
                    </button>
                </div>

                <div class="chat-container" id="chatContainer">
                    <!-- Messages will be populated by JavaScript -->
                </div>

                <div class="chat-input-container">
                    <input 
                        type="text" 
                        class="chat-input" 
                        id="chatInput" 
                        placeholder="Ask the AI…"
                        aria-label="Ask the AI assistant"
                    >
                    <button class="send-btn" id="sendBtn" disabled>
                        Send
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // State management
        const state = {
            site: {
                url: "",
                username: "",
                hasPassword: false,
                isSecure: false
            },
            selection: {
                selectedKeys: [],
                byGroup: {},
                count: 0
            },
            messages: [],
            recommendations: []
        };

        // Event emitter for cross-component communication
        const eventEmitter = {
            listeners: {},
            emit(event, data) {
                console.log(`AI Guide Event: ${event}`, data);
                if (this.listeners[event]) {
                    this.listeners[event].forEach(callback => callback(data));
                }
            },
            on(event, callback) {
                if (!this.listeners[event]) {
                    this.listeners[event] = [];
                }
                this.listeners[event].push(callback);
            }
        };

        // Knowledge base for AI responses
        const knowledgeBase = {
            "What do I need for login-only pages?": {
                response: "For login-protected pages, you'll need:",
                recommendations: ["scrape.api", "proc.queue", "scrape.files"],
                details: [
                    "API Connectors to handle authentication flows",
                    "Task Scheduler for session management",
                    "File Data Extraction for form handling"
                ]
            },
            "Which security tests fit web apps?": {
                response: "For web applications, I recommend:",
                recommendations: ["sec.web", "sec.pass", "rep.heatmap", "rep.export"],
                details: [
                    "Web App Testing (OWASP Top 10)",
                    "Password Security Assessment",
                    "Vulnerability Heatmaps for visualization",
                    "Professional reports for stakeholders"
                ]
            },
            "Best export format for stakeholders?": {
                response: "For stakeholder reports, consider:",
                recommendations: ["rep.export", "rep.dash", "rep.charts"],
                details: [
                    "PDF/DOCX exports for formal documentation",
                    "Interactive dashboards for live monitoring",
                    "Analytics charts for trend analysis"
                ]
            },
            "How to speed up large crawls?": {
                response: "To optimize large-scale operations:",
                recommendations: ["proc.optimize", "proc.cache", "proc.queue"],
                details: [
                    "Resource Optimizer for efficient allocation",
                    "Smart Caching to reduce redundant requests",
                    "Task Scheduler for parallel processing"
                ]
            }
        };

        // Subscribe to events from left panel
        function subscribeToEvents() {
            // Listen for site changes
            window.addEventListener('message', (event) => {
                if (event.data.type === 'site_changed') {
                    handleSiteChanged(event.data.payload);
                }
                if (event.data.type === 'selection_changed') {
                    handleSelectionChanged(event.data.payload);
                }
                if (event.data.type === 'apply_payload') {
                    handleApplyPayload(event.data.payload);
                }
            });
        }

        // Handle site configuration changes
        function handleSiteChanged(data) {
            state.site = {
                url: data.url,
                username: data.username || "",
                hasPassword: data.hasPassword || false,
                isSecure: data.url.startsWith('https://')
            };

            updateStatus();
            generateSiteAdvice();
        }

        // Handle selection changes
        function handleSelectionChanged(data) {
            state.selection = {
                selectedKeys: data.selectedKeys || [],
                byGroup: data.byGroup || {},
                count: data.count || 0
            };

            generateSelectionAdvice();
        }

        // Handle final payload application
        function handleApplyPayload(payload) {
            generateSummary(payload);
        }

        // Update status indicator
        function updateStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            if (state.site.url) {
                statusDot.classList.add('active');
                statusText.textContent = `Connected to ${new URL(state.site.url).hostname}`;
            } else {
                statusDot.classList.remove('active');
                statusText.textContent = 'Waiting for target site configuration...';
            }
        }

        // Generate site-specific advice
        function generateSiteAdvice() {
            const messages = [];

            if (!state.site.url) {
                addMessage('assistant', 'Enter a target URL to get tailored advice for your testing scenario.');
                return;
            }

            // Security warning for HTTP
            if (state.site.url.startsWith('http://')) {
                addWarningMessage('⚠️ Non-HTTPS detected', 'Consider switching to HTTPS for secure data transmission.');
            }

            // Login-specific advice
            if (state.site.username || state.site.hasPassword) {
                const recommendations = ['scrape.api', 'proc.queue'];
                addRecommendationMessage(
                    '🔐 Login Detected',
                    'For authenticated pages, I recommend enabling API Connectors and Task Scheduler.',
                    recommendations
                );
            }

            // General site advice
            const hostname = new URL(state.site.url).hostname;
            addMessage('assistant', `Ready to analyze ${hostname}. Select your capabilities and I'll provide targeted recommendations.`);
        }

        // Generate selection-specific advice
        function generateSelectionAdvice() {
            if (state.selection.count === 0) return;

            const recommendations = [];
            const selectedKeys = state.selection.selectedKeys;

            // Scraping + login recommendations
            if ((selectedKeys.includes('scrape.web') || selectedKeys.includes('scrape.realtime')) && 
                (state.site.username || state.site.hasPassword)) {
                recommendations.push('scrape.api', 'proc.queue');
            }

            // Security testing recommendations
            if (selectedKeys.some(key => key.startsWith('sec.'))) {
                recommendations.push('sec.web', 'rep.heatmap', 'rep.export');
            }

            // Performance recommendations for large selections
            if (state.selection.count > 10) {
                recommendations.push('proc.optimize', 'proc.cache');
            }

            // Generate recommendation if we have suggestions
            if (recommendations.length > 0) {
                const uniqueRecommendations = [...new Set(recommendations)].filter(
                    key => !selectedKeys.includes(key)
                );

                if (uniqueRecommendations.length > 0) {
                    addRecommendationMessage(
                        '💡 Smart Recommendations',
                        `Based on your ${state.selection.count} selected features, I suggest adding these complementary capabilities:`,
                        uniqueRecommendations
                    );
                }
            }
        }

        // Generate final summary
        function generateSummary(payload) {
            const groupCounts = Object.entries(payload.byGroup)
                .filter(([key, items]) => items.length > 0)
                .map(([key, items]) => `${getGroupName(key)}: ${items.length}`)
                .join(', ');

            addSummaryMessage(
                '✅ Configuration Applied',
                `Successfully configured ${payload.count} features across ${Object.keys(payload.byGroup).filter(k => payload.byGroup[k].length > 0).length} categories.`,
                {
                    'Total Features': payload.count,
                    'Categories': Object.keys(payload.byGroup).filter(k => payload.byGroup[k].length > 0).length,
                    'Target Site': new URL(payload.site.url).hostname,
                    'Auth Required': payload.site.username ? 'Yes' : 'No'
                }
            );

            addMessage('assistant', 'Your HexStrike AI configuration is ready! Would you like to proceed with the scan or need help with advanced settings?');
        }

        // Helper function to get group display name
        function getGroupName(key) {
            const names = {
                'scrape': 'Data Scraping',
                'sec': 'Security Testing',
                'ai': 'AI Agents',
                'rep': 'Reporting',
                'proc': 'Process Management'
            };
            return names[key] || key;
        }

        // Add different types of messages
        function addMessage(role, content) {
            const message = { role, content, timestamp: new Date() };
            state.messages.push(message);
            renderMessage(message);
            scrollToBottom();
        }

        function addWarningMessage(title, content) {
            const warningHtml = `
                <div class="warning">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-text">
                        <strong>${title}</strong><br>
                        ${content}
                    </div>
                </div>
            `;
            addMessage('assistant', warningHtml);
        }

        function addRecommendationMessage(title, content, recommendations) {
            state.recommendations = recommendations;
            const recommendationHtml = `
                <div class="recommendation">
                    <div class="recommendation-header">
                        💡 ${title}
                    </div>
                    <div>${content}</div>
                    <ul class="recommendation-list">
                        ${recommendations.map(key => `<li>${getFeatureName(key)}</li>`).join('')}
                    </ul>
                    <button class="apply-btn" onclick="applyRecommendations()">
                        Apply Recommendations (${recommendations.length})
                    </button>
                </div>
            `;
            addMessage('assistant', recommendationHtml);
        }

        function addSummaryMessage(title, content, stats) {
            const summaryHtml = `
                <div class="summary-card">
                    <div class="summary-header">
                        📊 ${title}
                    </div>
                    <div>${content}</div>
                    <div class="summary-stats">
                        ${Object.entries(stats).map(([label, value]) => `
                            <div class="stat-item">
                                <div class="stat-number">${value}</div>
                                <div class="stat-label">${label}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            addMessage('assistant', summaryHtml);
        }

        // Get feature display name
        function getFeatureName(key) {
            const names = {
                'scrape.api': 'API Connectors',
                'proc.queue': 'Task Scheduler & Queue',
                'sec.web': 'Web App Testing (40+)',
                'rep.heatmap': 'Vulnerability Heatmaps',
                'rep.export': 'Export PDF/DOCX/CSV',
                'proc.optimize': 'Resource Optimizer',
                'proc.cache': 'Smart Caching'
            };
            return names[key] || key;
        }

        // Apply recommendations
        function applyRecommendations() {
            if (state.recommendations.length > 0) {
                // Publish recommendations back to left panel
                window.parent.postMessage({
                    type: 'apply_recommendations',
                    payload: {
                        select: state.recommendations,
                        deselect: []
                    }
                }, '*');

                addMessage('assistant', `✅ Applied ${state.recommendations.length} recommendations to your configuration.`);
                state.recommendations = [];
            }
        }

        // Render message in chat
        function renderMessage(message) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.role}`;
            
            const avatar = message.role === 'assistant' ? '🤖' : '👤';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${message.content}</div>
            `;
            
            chatContainer.appendChild(messageDiv);
        }

        // Scroll chat to bottom
        function scrollToBottom() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Handle suggestion chip clicks
        function handleChipClick(e) {
            if (e.target.classList.contains('chip')) {
                const question = e.target.dataset.question;
                const chatInput = document.getElementById('chatInput');
                chatInput.value = question;
                handleSendMessage();
            }
        }

        // Handle chat input
        function handleSendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage('user', message);
            chatInput.value = '';
            
            // Generate AI response
            setTimeout(() => {
                generateAIResponse(message);
            }, 500);
        }

        // Generate AI response based on user input
        function generateAIResponse(userMessage) {
            const knowledge = knowledgeBase[userMessage];
            
            if (knowledge) {
                addRecommendationMessage(
                    'AI Recommendation',
                    knowledge.response,
                    knowledge.recommendations
                );
                
                // Add detailed explanation
                const details = knowledge.details.map(detail => `• ${detail}`).join('\n');
                addMessage('assistant', details);
            } else {
                // Generic helpful response
                if (userMessage.toLowerCase().includes('security')) {
                    addMessage('assistant', 'For security testing, I recommend starting with Web App Testing and adding Vulnerability Heatmaps for visual analysis. Would you like specific recommendations based on your target site?');
                } else if (userMessage.toLowerCase().includes('scraping') || userMessage.toLowerCase().includes('data')) {
                    addMessage('assistant', 'For data extraction, consider your target site\'s authentication requirements. If login is needed, enable API Connectors and Task Scheduler for session management.');
                } else if (userMessage.toLowerCase().includes('report') || userMessage.toLowerCase().includes('export')) {
                    addMessage('assistant', 'For reporting, PDF/DOCX exports work well for stakeholders, while interactive dashboards are great for ongoing monitoring. What type of audience will view your results?');
                } else {
                    addMessage('assistant', 'I can help you optimize your HexStrike AI configuration. Try asking about specific areas like security testing, data scraping, or reporting options.');
                }
            }
        }

        // Enable/disable send button based on input
        function updateSendButton() {
            const chatInput = document.getElementById('chatInput');
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = !chatInput.value.trim();
        }

        // Initialize the AI Guide
        function init() {
            // Set up event listeners
            document.getElementById('suggestionChips').addEventListener('click', handleChipClick);
            
            const chatInput = document.getElementById('chatInput');
            const sendBtn = document.getElementById('sendBtn');
            
            chatInput.addEventListener('input', updateSendButton);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                }
            });
            
            sendBtn.addEventListener('click', handleSendMessage);
            
            // Subscribe to events from other components
            subscribeToEvents();
            
            // Initial welcome message
            addMessage('assistant', 'Hi! I\'m your AI Guide. I\'ll help you configure HexStrike AI based on your target site and selected capabilities. Start by entering a target URL in the left panel.');
            
            console.log('AI Guide initialized and ready');
        }

        // Start the application
        document.addEventListener('DOMContentLoaded', init);

        // Global function for recommendation buttons
        window.applyRecommendations = applyRecommendations;
    </script>
</body>
</html>