<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Advanced Penetration Testing Framework</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="min-h-screen flex flex-col">
        <header class="bg-white dark:bg-gray-800 shadow">
            <div class="container mx-auto px-4 py-6">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <img src="logo.svg" alt="HexStrike AI Logo" class="h-10 w-10 mr-3">
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">HexStrike AI</h1>
                    </div>
                    <nav class="hidden md:flex space-x-6">
                        <a href="/" class="text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500">Dashboard</a>
                        <a href="/penetration-testing.html" class="text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500">Penetration Testing</a>
                        <a href="/security-analysis.html" class="text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500">Security Analysis</a>
                        <a href="/chat-window.html" class="text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500">AI Assistant</a>
                    </nav>
                    <div class="flex items-center">
                        <button id="theme-toggle" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg text-sm p-2">
                            <i class="fas fa-moon"></i>
                        </button>
                        <div class="relative ml-3">
                            <button id="user-menu-button" class="flex text-sm bg-gray-800 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                                <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <main class="flex-grow container mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-shield-alt text-red-600 text-2xl mr-3"></i>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Penetration Testing</h2>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">Automated penetration testing with advanced AI-driven tools.</p>
                    <a href="/penetration-testing.html" class="inline-flex items-center text-red-600 hover:text-red-800">
                        Launch Tool <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-search text-red-600 text-2xl mr-3"></i>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Security Analysis</h2>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">Comprehensive security analysis and vulnerability assessment.</p>
                    <a href="/security-analysis.html" class="inline-flex items-center text-red-600 hover:text-red-800">
                        Launch Tool <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-robot text-red-600 text-2xl mr-3"></i>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">AI Assistant</h2>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">Get help from our AI-powered security assistant.</p>
                    <a href="/chat-window.html" class="inline-flex items-center text-red-600 hover:text-red-800">
                        Launch Tool <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <div class="mt-10 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Platform Status</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Version</h3>
                        <p class="text-gray-600 dark:text-gray-400">6.0</p>
                    </div>
                    <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Status</h3>
                        <p class="text-green-600">Operational</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="bg-white dark:bg-gray-800 shadow">
            <div class="container mx-auto px-4 py-6">
                <p class="text-center text-gray-500 dark:text-gray-400">© 2025 HexStrike AI. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>