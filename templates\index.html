<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Advanced Penetration Testing Framework</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }
        
        .container {
            flex: 1;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero p {
            font-size: 1.2rem;
            color: #b0b0b0;
            margin-bottom: 2rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }
        
        .feature-card h3 {
            color: #ff6b6b;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .endpoints {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .endpoints h2 {
            color: #4ecdc4;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .endpoint-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .endpoint {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        
        .endpoint-path {
            font-family: 'Courier New', monospace;
            color: #4ecdc4;
            font-weight: bold;
        }
        
        .status {
            text-align: center;
            padding: 2rem;
        }
        
        .status-badge {
            display: inline-block;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 0.5rem 2rem;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }
        
        .actions {
            text-align: center;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🔥 HexStrike AI</div>
    </div>
    
    <div class="container">
        <div class="hero">
            <h1>Advanced Penetration Testing Framework</h1>
            <p>AI-Powered Security Testing Platform with 150+ Tools</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h3>🎯 Bug Bounty Automation</h3>
                <p>Automated reconnaissance, vulnerability hunting, and business logic testing workflows designed for bug bounty hunters.</p>
            </div>
            
            <div class="feature-card">
                <h3>🏆 CTF Challenge Solving</h3>
                <p>Advanced cryptography solvers, forensics analyzers, and binary exploitation tools for competitive CTF events.</p>
            </div>
            
            <div class="feature-card">
                <h3>🔴 Red Team Operations</h3>
                <p>Comprehensive penetration testing suite with AI-driven tool selection and intelligent parameter optimization.</p>
            </div>
            
            <div class="feature-card">
                <h3>🔬 Security Research</h3>
                <p>Zero-day research capabilities, exploit generation, and advanced vulnerability intelligence gathering.</p>
            </div>
            
            <div class="feature-card">
                <h3>🤖 AI-Driven Intelligence</h3>
                <p>Smart tool selection, automated attack chain creation, and intelligent error recovery systems.</p>
            </div>
            
            <div class="feature-card">
                <h3>⚡ Advanced Error Recovery</h3>
                <p>Robust error handling with automatic parameter adjustment and alternative tool suggestions.</p>
            </div>
        </div>
        
        <div class="endpoints">
            <h2>🚀 Available API Endpoints</h2>
            <div class="endpoint-list">
                <div class="endpoint">
                    <div class="endpoint-path">/health</div>
                    <div>System Health Check</div>
                </div>
                <div class="endpoint">
                    <div class="endpoint-path">/api/auth</div>
                    <div>Authentication System</div>
                </div>
                <div class="endpoint">
                    <div class="endpoint-path">/api/tools</div>
                    <div>150+ Security Tools</div>
                </div>
                <div class="endpoint">
                    <div class="endpoint-path">/api/intelligence</div>
                    <div>AI-Driven Analysis</div>
                </div>
                <div class="endpoint">
                    <div class="endpoint-path">/api/bugbounty</div>
                    <div>Bug Bounty Workflows</div>
                </div>
                <div class="endpoint">
                    <div class="endpoint-path">/api/ctf</div>
                    <div>CTF Challenge Tools</div>
                </div>
            </div>
        </div>
        
        <div class="status">
            <div class="status-badge">🟢 OPERATIONAL</div>
        </div>
        
        <div class="actions">
            <a href="/health" class="btn">🔍 Health Check</a>
            <a href="/api" class="btn">📚 API Documentation</a>
            <a href="/chat-interface" class="btn">💬 Chat Interface</a>
        </div>
    </div>
</body>
</html>