<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links a {
            color: #4ecdc4;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            background: rgba(78, 205, 196, 0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            color: #ff6b6b;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .page-header p {
            color: #b0b0b0;
            font-size: 1.1rem;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .settings-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .card-header .icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
        
        .card-header h3 {
            color: #4ecdc4;
            font-size: 1.2rem;
        }
        
        .setting-group {
            margin-bottom: 1.5rem;
        }
        
        .setting-group:last-child {
            margin-bottom: 0;
        }
        
        .setting-label {
            display: block;
            color: #ffffff;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .setting-description {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .setting-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.75rem;
            color: #ffffff;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .setting-input:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }
        
        .setting-input::placeholder {
            color: #888;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #333;
            transition: 0.4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #4ecdc4;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .api-key-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .api-key-info {
            flex: 1;
        }
        
        .api-key-name {
            font-weight: 500;
            color: #4ecdc4;
        }
        
        .api-key-value {
            font-family: monospace;
            color: #b0b0b0;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .api-key-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        }
        
        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #666, #555);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(102, 102, 102, 0.4);
        }
        
        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ff6b6b;
        }
        
        .status-indicator.connected {
            background: #4ecdc4;
        }
        
        .status-text {
            font-size: 0.9rem;
            color: #b0b0b0;
        }
        
        .save-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .save-section h3 {
            color: #ff6b6b;
            margin-bottom: 1rem;
        }
        
        .save-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(78, 205, 196, 0.9);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.error {
            background: rgba(255, 107, 107, 0.9);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            margin: 10% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header h3 {
            color: #4ecdc4;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #fff;
        }
        
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
            
            .save-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🔥 HexStrike AI</div>
        <div class="nav-links">
            <a href="/">🏠 Home</a>
            <a href="/chat-interface">💬 Chat</a>
            <a href="/settings" class="active">⚙️ Settings</a>
            <a href="/health">🔍 Health</a>
        </div>
    </div>
    
    <div class="container">
        <div class="page-header">
            <h1>⚙️ Settings & Configuration</h1>
            <p>Configure your HexStrike AI experience with custom settings, API keys, and feature preferences</p>
        </div>
        
        <div class="settings-grid">
            <!-- API Management -->
            <div class="settings-card">
                <div class="card-header">
                    <span class="icon">🔑</span>
                    <h3>API Key Management</h3>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">OpenAI API Key</label>
                    <div class="setting-description">Required for AI-powered security analysis and tool recommendations</div>
                    <input type="password" class="setting-input" id="openaiKey" placeholder="sk-..." value="">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Shodan API Key</label>
                    <div class="setting-description">For enhanced reconnaissance and vulnerability scanning</div>
                    <input type="password" class="setting-input" id="shodanKey" placeholder="Enter Shodan API key" value="">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">VirusTotal API Key</label>
                    <div class="setting-description">For malware analysis and threat intelligence</div>
                    <input type="password" class="setting-input" id="virusTotalKey" placeholder="Enter VirusTotal API key" value="">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Custom API Keys</label>
                    <div class="setting-description">Add additional API keys for third-party services</div>
                    <div id="customApiKeys">
                        <!-- Custom API keys will be populated here -->
                    </div>
                    <button class="btn btn-small" onclick="addCustomApiKey()">+ Add API Key</button>
                </div>
            </div>
            
            <!-- Feature Toggles -->
            <div class="settings-card">
                <div class="card-header">
                    <span class="icon">🎛️</span>
                    <h3>Feature Controls</h3>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">AI-Powered Tool Selection</label>
                    <div class="setting-description">Let AI automatically choose the best tools for your tasks</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="aiToolSelection" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Automatic Vulnerability Scanning</label>
                    <div class="setting-description">Enable continuous vulnerability monitoring</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="autoVulnScan">
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Advanced Logging</label>
                    <div class="setting-description">Enable detailed logging for debugging and analysis</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="advancedLogging">
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Real-time Notifications</label>
                    <div class="setting-description">Get instant alerts for security findings</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="realTimeNotifications" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Dark Mode</label>
                    <div class="setting-description">Toggle between light and dark themes</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="darkMode" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            
            <!-- Connection Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <span class="icon">🔗</span>
                    <h3>Connection Settings</h3>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Database Connection</label>
                    <div class="setting-description">Configure database connection for storing results</div>
                    <input type="text" class="setting-input" id="dbHost" placeholder="localhost" value="localhost">
                    <div class="connection-status">
                        <div class="status-indicator connected" id="dbStatus"></div>
                        <span class="status-text">Connected</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Redis Cache</label>
                    <div class="setting-description">Redis server for caching and session management</div>
                    <input type="text" class="setting-input" id="redisHost" placeholder="localhost:6379" value="localhost:6379">
                    <div class="connection-status">
                        <div class="status-indicator" id="redisStatus"></div>
                        <span class="status-text">Disconnected</span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Proxy Settings</label>
                    <div class="setting-description">Configure proxy for external tool connections</div>
                    <input type="text" class="setting-input" id="proxyUrl" placeholder="http://proxy:8080" value="">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Timeout Settings</label>
                    <div class="setting-description">Default timeout for tool execution (seconds)</div>
                    <input type="number" class="setting-input" id="toolTimeout" placeholder="300" value="300" min="30" max="3600">
                </div>
            </div>
            
            <!-- Security Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <span class="icon">🛡️</span>
                    <h3>Security & Privacy</h3>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Enable Authentication</label>
                    <div class="setting-description">Require login for accessing the application</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="enableAuth">
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Session Timeout</label>
                    <div class="setting-description">Automatic logout after inactivity (minutes)</div>
                    <input type="number" class="setting-input" id="sessionTimeout" placeholder="60" value="60" min="5" max="480">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Data Retention</label>
                    <div class="setting-description">How long to keep scan results and logs (days)</div>
                    <input type="number" class="setting-input" id="dataRetention" placeholder="30" value="30" min="1" max="365">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Encrypt Stored Data</label>
                    <div class="setting-description">Encrypt sensitive data at rest</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="encryptData" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            
            <!-- Tool Configuration -->
            <div class="settings-card">
                <div class="card-header">
                    <span class="icon">🔧</span>
                    <h3>Tool Configuration</h3>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Default Scan Intensity</label>
                    <div class="setting-description">Default intensity level for security scans</div>
                    <select class="setting-input" id="scanIntensity">
                        <option value="low">Low (Fast, Basic)</option>
                        <option value="medium" selected>Medium (Balanced)</option>
                        <option value="high">High (Thorough, Slow)</option>
                        <option value="aggressive">Aggressive (Maximum Coverage)</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Concurrent Tool Limit</label>
                    <div class="setting-description">Maximum number of tools running simultaneously</div>
                    <input type="number" class="setting-input" id="concurrentLimit" placeholder="5" value="5" min="1" max="20">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Auto-Update Tools</label>
                    <div class="setting-description">Automatically update security tools and databases</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="autoUpdateTools" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Custom Tool Paths</label>
                    <div class="setting-description">Specify custom installation paths for tools</div>
                    <textarea class="setting-input" id="customPaths" rows="3" placeholder="nmap=/usr/local/bin/nmap\ngobuster=/opt/gobuster/gobuster"></textarea>
                </div>
            </div>
            
            <!-- Notification Settings -->
            <div class="settings-card">
                <div class="card-header">
                    <span class="icon">🔔</span>
                    <h3>Notifications</h3>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Email Notifications</label>
                    <div class="setting-description">Send scan results and alerts via email</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="emailNotifications">
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Email Address</label>
                    <div class="setting-description">Email address for notifications</div>
                    <input type="email" class="setting-input" id="notificationEmail" placeholder="<EMAIL>" value="">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Slack Webhook</label>
                    <div class="setting-description">Send notifications to Slack channel</div>
                    <input type="url" class="setting-input" id="slackWebhook" placeholder="https://hooks.slack.com/..." value="">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">Discord Webhook</label>
                    <div class="setting-description">Send notifications to Discord channel</div>
                    <input type="url" class="setting-input" id="discordWebhook" placeholder="https://discord.com/api/webhooks/..." value="">
                </div>
            </div>
        </div>
        
        <!-- Save Section -->
        <div class="save-section">
            <h3>💾 Save Configuration</h3>
            <p>Save your settings to apply changes and persist your configuration</p>
            <div class="save-buttons">
                <button class="btn" onclick="saveSettings()">💾 Save All Settings</button>
                <button class="btn btn-secondary" onclick="resetSettings()">🔄 Reset to Defaults</button>
                <button class="btn btn-secondary" onclick="exportSettings()">📤 Export Config</button>
                <button class="btn btn-secondary" onclick="importSettings()">📥 Import Config</button>
            </div>
        </div>
    </div>
    
    <!-- Notification -->
    <div id="notification" class="notification"></div>
    
    <!-- Add API Key Modal -->
    <div id="apiKeyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Custom API Key</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="setting-group">
                <label class="setting-label">Service Name</label>
                <input type="text" class="setting-input" id="newApiName" placeholder="e.g., SecurityTrails, Censys">
            </div>
            <div class="setting-group">
                <label class="setting-label">API Key</label>
                <input type="password" class="setting-input" id="newApiKey" placeholder="Enter API key">
            </div>
            <div class="setting-group">
                <label class="setting-label">Description (Optional)</label>
                <input type="text" class="setting-input" id="newApiDescription" placeholder="Brief description of the service">
            </div>
            <div style="text-align: center; margin-top: 1.5rem;">
                <button class="btn" onclick="saveCustomApiKey()">Add API Key</button>
                <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </div>
    </div>
    
    <script>
        // Load settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            loadCustomApiKeys();
        });
        
        // Save settings function
        function saveSettings() {
            const settings = {
                // API Keys
                openaiKey: document.getElementById('openaiKey').value,
                shodanKey: document.getElementById('shodanKey').value,
                virusTotalKey: document.getElementById('virusTotalKey').value,
                
                // Feature toggles
                aiToolSelection: document.getElementById('aiToolSelection').checked,
                autoVulnScan: document.getElementById('autoVulnScan').checked,
                advancedLogging: document.getElementById('advancedLogging').checked,
                realTimeNotifications: document.getElementById('realTimeNotifications').checked,
                darkMode: document.getElementById('darkMode').checked,
                
                // Connection settings
                dbHost: document.getElementById('dbHost').value,
                redisHost: document.getElementById('redisHost').value,
                proxyUrl: document.getElementById('proxyUrl').value,
                toolTimeout: document.getElementById('toolTimeout').value,
                
                // Security settings
                enableAuth: document.getElementById('enableAuth').checked,
                sessionTimeout: document.getElementById('sessionTimeout').value,
                dataRetention: document.getElementById('dataRetention').value,
                encryptData: document.getElementById('encryptData').checked,
                
                // Tool configuration
                scanIntensity: document.getElementById('scanIntensity').value,
                concurrentLimit: document.getElementById('concurrentLimit').value,
                autoUpdateTools: document.getElementById('autoUpdateTools').checked,
                customPaths: document.getElementById('customPaths').value,
                
                // Notifications
                emailNotifications: document.getElementById('emailNotifications').checked,
                notificationEmail: document.getElementById('notificationEmail').value,
                slackWebhook: document.getElementById('slackWebhook').value,
                discordWebhook: document.getElementById('discordWebhook').value
            };
            
            // Save to localStorage
            localStorage.setItem('hexstrike_settings', JSON.stringify(settings));
            
            // Send to server (if API endpoint exists)
            fetch('/api/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                showNotification('Settings saved successfully!', 'success');
            })
            .catch(error => {
                console.log('Server save failed, using local storage only');
                showNotification('Settings saved locally!', 'success');
            });
        }
        
        // Load settings function
        function loadSettings() {
            const saved = localStorage.getItem('hexstrike_settings');
            if (saved) {
                try {
                    const settings = JSON.parse(saved);
                    
                    // Load API keys (mask them for security)
                    if (settings.openaiKey) document.getElementById('openaiKey').value = '••••••••';
                    if (settings.shodanKey) document.getElementById('shodanKey').value = '••••••••';
                    if (settings.virusTotalKey) document.getElementById('virusTotalKey').value = '••••••••';
                    
                    // Load feature toggles
                    document.getElementById('aiToolSelection').checked = settings.aiToolSelection ?? true;
                    document.getElementById('autoVulnScan').checked = settings.autoVulnScan ?? false;
                    document.getElementById('advancedLogging').checked = settings.advancedLogging ?? false;
                    document.getElementById('realTimeNotifications').checked = settings.realTimeNotifications ?? true;
                    document.getElementById('darkMode').checked = settings.darkMode ?? true;
                    
                    // Load connection settings
                    document.getElementById('dbHost').value = settings.dbHost || 'localhost';
                    document.getElementById('redisHost').value = settings.redisHost || 'localhost:6379';
                    document.getElementById('proxyUrl').value = settings.proxyUrl || '';
                    document.getElementById('toolTimeout').value = settings.toolTimeout || '300';
                    
                    // Load security settings
                    document.getElementById('enableAuth').checked = settings.enableAuth ?? false;
                    document.getElementById('sessionTimeout').value = settings.sessionTimeout || '60';
                    document.getElementById('dataRetention').value = settings.dataRetention || '30';
                    document.getElementById('encryptData').checked = settings.encryptData ?? true;
                    
                    // Load tool configuration
                    document.getElementById('scanIntensity').value = settings.scanIntensity || 'medium';
                    document.getElementById('concurrentLimit').value = settings.concurrentLimit || '5';
                    document.getElementById('autoUpdateTools').checked = settings.autoUpdateTools ?? true;
                    document.getElementById('customPaths').value = settings.customPaths || '';
                    
                    // Load notifications
                    document.getElementById('emailNotifications').checked = settings.emailNotifications ?? false;
                    document.getElementById('notificationEmail').value = settings.notificationEmail || '';
                    document.getElementById('slackWebhook').value = settings.slackWebhook || '';
                    document.getElementById('discordWebhook').value = settings.discordWebhook || '';
                } catch (e) {
                    console.error('Error loading settings:', e);
                }
            }
        }
        
        // Reset settings to defaults
        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
                localStorage.removeItem('hexstrike_settings');
                localStorage.removeItem('hexstrike_custom_apis');
                location.reload();
            }
        }
        
        // Export settings
        function exportSettings() {
            const settings = localStorage.getItem('hexstrike_settings');
            const customApis = localStorage.getItem('hexstrike_custom_apis');
            
            const exportData = {
                settings: settings ? JSON.parse(settings) : {},
                customApis: customApis ? JSON.parse(customApis) : []
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'hexstrike-settings.json';
            a.click();
            URL.revokeObjectURL(url);
            
            showNotification('Settings exported successfully!', 'success');
        }
        
        // Import settings
        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const importData = JSON.parse(e.target.result);
                            
                            if (importData.settings) {
                                localStorage.setItem('hexstrike_settings', JSON.stringify(importData.settings));
                            }
                            
                            if (importData.customApis) {
                                localStorage.setItem('hexstrike_custom_apis', JSON.stringify(importData.customApis));
                            }
                            
                            showNotification('Settings imported successfully!', 'success');
                            setTimeout(() => location.reload(), 1500);
                        } catch (error) {
                            showNotification('Error importing settings: Invalid file format', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }
        
        // Custom API key management
        function addCustomApiKey() {
            document.getElementById('apiKeyModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('apiKeyModal').style.display = 'none';
            document.getElementById('newApiName').value = '';
            document.getElementById('newApiKey').value = '';
            document.getElementById('newApiDescription').value = '';
        }
        
        function saveCustomApiKey() {
            const name = document.getElementById('newApiName').value.trim();
            const key = document.getElementById('newApiKey').value.trim();
            const description = document.getElementById('newApiDescription').value.trim();
            
            if (!name || !key) {
                showNotification('Please fill in both service name and API key', 'error');
                return;
            }
            
            const customApis = JSON.parse(localStorage.getItem('hexstrike_custom_apis') || '[]');
            customApis.push({
                id: Date.now(),
                name: name,
                key: key,
                description: description
            });
            
            localStorage.setItem('hexstrike_custom_apis', JSON.stringify(customApis));
            loadCustomApiKeys();
            closeModal();
            showNotification('Custom API key added successfully!', 'success');
        }
        
        function loadCustomApiKeys() {
            const customApis = JSON.parse(localStorage.getItem('hexstrike_custom_apis') || '[]');
            const container = document.getElementById('customApiKeys');
            
            container.innerHTML = '';
            
            customApis.forEach(api => {
                const div = document.createElement('div');
                div.className = 'api-key-item';
                div.innerHTML = `
                    <div class="api-key-info">
                        <div class="api-key-name">${api.name}</div>
                        <div class="api-key-value">••••••••</div>
                        ${api.description ? `<div style="font-size: 0.8rem; color: #888; margin-top: 0.25rem;">${api.description}</div>` : ''}
                    </div>
                    <div class="api-key-actions">
                        <button class="btn btn-small btn-danger" onclick="removeCustomApiKey(${api.id})">Remove</button>
                    </div>
                `;
                container.appendChild(div);
            });
        }
        
        function removeCustomApiKey(id) {
            if (confirm('Are you sure you want to remove this API key?')) {
                const customApis = JSON.parse(localStorage.getItem('hexstrike_custom_apis') || '[]');
                const filtered = customApis.filter(api => api.id !== id);
                localStorage.setItem('hexstrike_custom_apis', JSON.stringify(filtered));
                loadCustomApiKeys();
                showNotification('API key removed successfully!', 'success');
            }
        }
        
        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('apiKeyModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>