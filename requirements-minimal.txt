# Minimal Local Development Requirements (no compilation needed)
# Core Flask dependencies
flask==3.0.0
flask-cors==4.0.0
flask-jwt-extended==4.6.0
flask-limiter==3.5.0
flask-sqlalchemy==3.1.1
flask-migrate==4.0.5
requests==2.31.0
psutil==5.9.6
cryptography==41.0.7

# Database (SQLite)
sqlalchemy==2.0.23
alembic==1.12.1

# Validation
pydantic>=2.5.3
marshmallow==3.20.1

# Monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Web scraping (without aiohttp)
beautifulsoup4==4.12.2

# Werkzeug for password hashing
werkzeug==3.0.1

# Python-dotenv for environment variables
python-dotenv==1.0.0

# Development tools
pytest==7.4.3
pytest-cov==4.1.0
bandit==1.7.5
black==23.11.0
isort==5.12.0