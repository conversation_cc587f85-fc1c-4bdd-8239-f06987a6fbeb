# Worker Dockerfile for HexStrike AI Background Tasks
# Includes security tools and sandboxing capabilities

# Stage 1: Base image with security tools
FROM python:3.11-slim-bullseye AS base

# Security: Create non-root user
RUN groupadd -r worker && useradd -r -g worker -s /bin/false worker

# Install system dependencies and security tools
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    curl \
    wget \
    ca-certificates \
    nmap \
    gobuster \
    nikto \
    sqlmap \
    dirb \
    dnsutils \
    netcat-openbsd \
    git \
    unzip \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Nuclei
RUN wget -q https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_linux_amd64.zip && \
    unzip nuclei_2.9.15_linux_amd64.zip && \
    mv nuclei /usr/local/bin/ && \
    rm nuclei_2.9.15_linux_amd64.zip && \
    chmod +x /usr/local/bin/nuclei

# Stage 2: Dependencies installation
FROM base AS dependencies

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    python3-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy worker requirements
COPY requirements-worker.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements-worker.txt

# Stage 3: Worker application
FROM base AS worker

# Copy Python packages from dependencies stage
COPY --from=dependencies /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Set working directory
WORKDIR /app

# Copy worker-specific files
COPY --chown=worker:worker worker.py .
COPY --chown=worker:worker queue_manager.py .
COPY --chown=worker:worker security_middleware.py .
COPY --chown=worker:worker models.py .
COPY --chown=worker:worker config.py .

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/temp /app/scans && \
    chown -R worker:worker /app/logs /app/temp /app/scans

# Security: Set proper permissions
RUN chmod -R 755 /app && \
    chmod -R 644 /app/*.py && \
    chmod +x /app/worker.py

# Security: Switch to non-root user
USER worker

# Environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONIOENCODING=utf-8 \
    WORKER_CONCURRENCY=2 \
    WORKER_TIMEOUT=300

# Health check for worker
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD python -c "import redis; r=redis.Redis(host='redis'); r.ping()" || exit 1

# Start worker
CMD ["python", "worker.py"]