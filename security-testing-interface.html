<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Testing Interface - HexStrike AI</title>
    <style>
        :root {
            --primary-color: #e63946;
            --secondary-color: #457b9d;
            --dark-bg: #1d3557;
            --light-bg: #f1faee;
            --accent-color: #a8dadc;
            --text-light: #f1faee;
            --text-dark: #1d3557;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 20px 0;
            text-align: center;
        }
        
        header h1 {
            margin-bottom: 10px;
        }
        
        .security-form {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="url"],
        select,
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #c1121f;
        }
        
        .results {
            margin-top: 30px;
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .results pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        .back-button {
            display: inline-block;
            margin-top: 20px;
            background-color: var(--secondary-color);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
        }
        
        .back-button:hover {
            background-color: #3d6990;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .tool-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border-left: 5px solid var(--primary-color);
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .tool-card.selected {
            border-left: 5px solid #2ecc71;
        }
        
        .tool-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .tool-result {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: #f8f9fa;
            display: none;
        }
        
        .tool-result.running {
            display: block;
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
        }
        
        .tool-result.success {
            display: block;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .tool-result.error {
            display: block;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>HexStrike AI - Security Testing Interface</h1>
            <p>Advanced penetration testing and security assessment tools</p>
        </div>
    </header>
    
    <div class="container">
        <div class="security-form">
            <h2>Target Configuration</h2>
            <form id="securityForm">
                <div class="form-group">
                    <label for="targetUrl">Target URL or IP:</label>
                    <input type="text" id="targetUrl" name="targetUrl" placeholder="https://example.com or ***********" required>
                </div>
                
                <div class="form-group">
                    <label for="scanType">Scan Type:</label>
                    <select id="scanType" name="scanType">
                        <option value="quick">Quick Scan</option>
                        <option value="full">Full Scan</option>
                        <option value="vuln">Vulnerability Assessment</option>
                        <option value="custom">Custom Scan</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="options">Advanced Options:</label>
                    <textarea id="options" name="options" rows="5" placeholder="--exclude-dirs=/admin,/backup
--max-scan-duration=60m
--authentication=basic
--custom-headers=X-Test: value"></textarea>
                </div>
                
                <button type="submit">Start Security Scan</button>
            </form>
        </div>
        
        <div class="tool-grid">
            <div class="tool-card" data-tool="network-scanner">
                <h3>Network Scanner</h3>
                <p>Discover hosts, open ports, and services on the target network.</p>
                <button class="select-tool">Select Tool</button>
                <div class="tool-result" id="network-scanner-result">
                    <span class="result-message">Test not started</span>
                </div>
            </div>
            
            <div class="tool-card" data-tool="web-vulnerability">
                <h3>Web Vulnerability Scanner</h3>
                <p>Identify common web vulnerabilities like XSS, SQLi, and CSRF.</p>
                <button class="select-tool">Select Tool</button>
                <div class="tool-result" id="web-vulnerability-result">
                    <span class="result-message">Test not started</span>
                </div>
            </div>
            
            <div class="tool-card" data-tool="api-security">
                <h3>API Security Tester</h3>
                <p>Test REST and GraphQL APIs for security issues and misconfigurations.</p>
                <button class="select-tool">Select Tool</button>
                <div class="tool-result" id="api-security-result">
                    <span class="result-message">Test not started</span>
                </div>
            </div>
            
            <div class="tool-card" data-tool="password-cracker">
                <h3>Password Cracker</h3>
                <p>Test password strength using dictionary and brute force attacks.</p>
                <button class="select-tool">Select Tool</button>
                <div class="tool-result" id="password-cracker-result">
                    <span class="result-message">Test not started</span>
                </div>
            </div>
            
            <div class="tool-card" data-tool="exploit-framework">
                <h3>Exploit Framework</h3>
                <p>Execute and test exploits against known vulnerabilities.</p>
                <button class="select-tool">Select Tool</button>
                <div class="tool-result" id="exploit-framework-result">
                    <span class="result-message">Test not started</span>
                </div>
            </div>
            
            <div class="tool-card" data-tool="traffic-analyzer">
                <h3>Traffic Analyzer</h3>
                <p>Capture and analyze network traffic for security issues.</p>
                <button class="select-tool">Select Tool</button>
                <div class="tool-result" id="traffic-analyzer-result">
                    <span class="result-message">Test not started</span>
                </div>
            </div>
        </div>
        
        <div class="results" style="display: none;">
            <h2>Scan Results</h2>
            <pre id="resultsOutput">Results will appear here...</pre>
        </div>
        
        <a href="/" class="back-button">Back to Main Menu</a>
    </div>
    
    <script>
        // Handle tool selection and test execution
        document.addEventListener('DOMContentLoaded', function() {
            // Get all tool selection buttons
            const selectButtons = document.querySelectorAll('.select-tool');
            
            // Add click event listener to each button
            selectButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Get the parent tool card
                    const toolCard = this.closest('.tool-card');
                    const toolId = toolCard.getAttribute('data-tool');
                    const resultElement = document.getElementById(`${toolId}-result`);
                    
                    // Toggle selection state
                    if (toolCard.classList.contains('selected')) {
                        // Deselect the tool
                        toolCard.classList.remove('selected');
                        resultElement.classList.remove('running', 'success', 'error');
                        resultElement.style.display = 'none';
                    } else {
                        // Select the tool
                        toolCard.classList.add('selected');
                        // Note: We don't remove selection from other tools to allow multiple selections
                    }
                });
            });
        });

        // Handle form submission and run selected tests
        document.getElementById('securityForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get all selected tools
            const selectedTools = document.querySelectorAll('.tool-card.selected');
            
            // If no tools selected, show alert
            if (selectedTools.length === 0) {
                alert('Please select at least one security testing tool');
                return;
            }
            
            // Show loading state for main results
            const resultsSection = document.querySelector('.results');
            resultsSection.style.display = 'block';
            document.getElementById('resultsOutput').textContent = 'Security scan in progress...';
            
            // Process each selected tool
            selectedTools.forEach(tool => {
                const toolId = tool.getAttribute('data-tool');
                const resultElement = document.getElementById(`${toolId}-result`);
                
                // Show running state
                resultElement.classList.add('running');
                resultElement.classList.remove('success', 'error');
                resultElement.style.display = 'block';
                resultElement.querySelector('.result-message').textContent = `${tool.querySelector('h3').textContent} test in progress...`;
                
                // Simulate test execution with random timing
                const testDuration = 1000 + Math.random() * 4000; // 1-5 seconds
                
                setTimeout(() => {
                    // Randomly determine success or failure (80% success rate)
                    const isSuccess = Math.random() > 0.2;
                    
                    resultElement.classList.remove('running');
                    if (isSuccess) {
                        resultElement.classList.add('success');
                        resultElement.querySelector('.result-message').textContent = 
                            `${tool.querySelector('h3').textContent} completed successfully. Found ${Math.floor(Math.random() * 5)} issues.`;
                    } else {
                        resultElement.classList.add('error');
                        resultElement.querySelector('.result-message').textContent = 
                            `${tool.querySelector('h3').textContent} encountered an error. Please try again.`;
                    }
                }, testDuration);
            });
            
            // In a real implementation, this would send the data to a backend
            // For demo purposes, we'll simulate a response after a delay
            setTimeout(() => {
                const demoResult = {
                    "scan_id": "SEC-" + Math.floor(Math.random() * 1000000),
                    "target": document.getElementById('targetUrl').value,
                    "scan_type": document.getElementById('scanType').value,
                    "timestamp": new Date().toISOString(),
                    "duration": "00:02:45",
                    "findings": [
                        {
                            "severity": "HIGH",
                            "category": "Injection",
                            "description": "SQL Injection vulnerability detected in login form",
                            "evidence": "Parameter 'username' is vulnerable to ' OR 1=1 --",
                            "remediation": "Implement prepared statements and input validation"
                        },
                        {
                            "severity": "MEDIUM",
                            "category": "Information Disclosure",
                            "description": "Server version information exposed in headers",
                            "evidence": "Server: Apache/2.4.41 (Ubuntu)",
                            "remediation": "Configure server to hide version information"
                        },
                        {
                            "severity": "LOW",
                            "category": "Security Headers",
                            "description": "Missing security headers",
                            "evidence": "X-Content-Type-Options, X-Frame-Options headers not present",
                            "remediation": "Add appropriate security headers to server responses"
                        }
                    ],
                    "summary": {
                        "high": 1,
                        "medium": 2,
                        "low": 3,
                        "info": 5
                    }
                };
                
                document.getElementById('resultsOutput').textContent = JSON.stringify(demoResult, null, 2);
            }, 3000);
        });
    </script>
</body>
</html>