
# Security Headers Configuration for Nginx
# Add these to your nginx.conf or site configuration

# Security Headers
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
add_header X-XSS-Protection "1; mode=block";
add_header Referrer-Policy "strict-origin-when-cross-origin";
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()";

# Content Security Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';";

# HSTS (only enable after SS<PERSON> is properly configured)
# add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# Hide server information
server_tokens off;

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Apply rate limiting to specific endpoints
location /api/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://backend;
}

location /auth/login {
    limit_req zone=login burst=5 nodelay;
    proxy_pass http://backend;
}
