{"mcpServers": {"chat-box-ai": {"command": "python3", "args": ["/path/chat_box_mcp.py", "--server", "http://IPADDRESS:8888", "--timeout", "120"], "description": "Chat Box AI v6.0 - Advanced Cybersecurity Automation Platform with Security Controls", "timeout": 120, "retryPolicy": {"maxRetries": 2, "retryDelay": 5000, "backoffMultiplier": 2.0}, "resourceLimits": {"maxMemoryMB": 512, "maxCpuPercent": 50, "maxExecutionTimeMs": 300000}, "security": {"allowNetworkAccess": false, "allowFileSystemAccess": false, "allowProcessExecution": false, "sandboxed": true}, "alwaysAllow": ["nmap_scan", "gobuster_scan", "nuclei_scan"], "requireApproval": ["prowler_scan", "trivy_scan", "scout_suite_assessment", "cloudmapper_analysis", "sqlmap_scan", "nikto_scan", "wpscan_scan", "burp_scan", "zap_scan"], "blocked": ["pacu_exploitation", "metasploit_exploit", "empire_agent", "cobalt_strike", "<PERSON><PERSON><PERSON><PERSON>", "bloodhound", "crackmapexec", "kube_hunter_scan"], "rateLimits": {"requestsPerMinute": 10, "requestsPerHour": 100, "concurrentRequests": 3}, "logging": {"level": "INFO", "auditEnabled": true, "sensitiveDataMasking": true}}}}