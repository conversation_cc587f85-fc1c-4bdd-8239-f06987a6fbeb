#!/usr/bin/env python3
"""
HexStrike AI - Supabase Client Integration
Provides Supabase client for real-time features and direct database access
"""

import os
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class SupabaseManager:
    """Manages Supabase client connection and operations"""
    
    def __init__(self):
        self._client: Optional[Client] = None
    
    def init_app(self, app):
        """Initialize Supabase client with Flask app"""
        supabase_url = app.config.get('SUPABASE_URL')
        supabase_key = app.config.get('SUPABASE_ANON_KEY')
        
        logger.info(f"Supabase URL: {supabase_url}")
        logger.info(f"Supabase Key present: {bool(supabase_key)}")
        
        if not supabase_url or not supabase_key:
            logger.warning("Supabase configuration missing. Some features may not work.")
            logger.warning(f"URL missing: {not supabase_url}, Key missing: {not supabase_key}")
            return
        
        try:
            self._client = create_client(supabase_url, supabase_key)
            logger.info("Supabase client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    @property
    def client(self) -> Optional[Client]:
        """Get the Supabase client instance"""
        return self._client
    
    def test_connection(self) -> Dict[str, Any]:
        """Test Supabase connection"""
        if not self._client:
            return {"status": "error", "message": "Supabase client not initialized"}
        
        try:
            # Simple test using Supabase auth to verify connection
            # This doesn't require table access permissions
            response = self._client.auth.get_session()
            return {
                "status": "success", 
                "message": "Supabase connection successful",
                "client_initialized": True
            }
        except Exception as e:
            return {
                "status": "error", 
                "message": f"Supabase connection failed: {str(e)}"
            }
    
    def create_table_if_not_exists(self, table_name: str, schema: Dict[str, str]) -> bool:
        """Create table if it doesn't exist (using SQL)"""
        if not self._client:
            logger.error("Supabase client not initialized")
            return False
        
        try:
            # Build CREATE TABLE SQL
            columns = []
            for col_name, col_type in schema.items():
                columns.append(f"{col_name} {col_type}")
            
            sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
            
            # Execute SQL using Supabase RPC or direct SQL execution
            # Note: This requires appropriate permissions
            result = self._client.rpc('exec_sql', {'sql': sql}).execute()
            logger.info(f"Table {table_name} created/verified successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create table {table_name}: {e}")
            return False
    
    def insert_data(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Insert data into Supabase table"""
        if not self._client:
            return {"status": "error", "message": "Supabase client not initialized"}
        
        try:
            result = self._client.table(table_name).insert(data).execute()
            return {
                "status": "success",
                "message": "Data inserted successfully",
                "data": result.data
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to insert data: {str(e)}"
            }
    
    def select_data(self, table_name: str, filters: Optional[Dict[str, Any]] = None, 
                   columns: str = "*", limit: Optional[int] = None) -> Dict[str, Any]:
        """Select data from Supabase table"""
        if not self._client:
            return {"status": "error", "message": "Supabase client not initialized"}
        
        try:
            query = self._client.table(table_name).select(columns)
            
            # Apply filters
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            # Apply limit
            if limit:
                query = query.limit(limit)
            
            result = query.execute()
            return {
                "status": "success",
                "message": "Data retrieved successfully",
                "data": result.data
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to select data: {str(e)}"
            }
    
    def update_data(self, table_name: str, data: Dict[str, Any], 
                   filters: Dict[str, Any]) -> Dict[str, Any]:
        """Update data in Supabase table"""
        if not self._client:
            return {"status": "error", "message": "Supabase client not initialized"}
        
        try:
            query = self._client.table(table_name).update(data)
            
            # Apply filters
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            return {
                "status": "success",
                "message": "Data updated successfully",
                "data": result.data
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to update data: {str(e)}"
            }
    
    def delete_data(self, table_name: str, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Delete data from Supabase table"""
        if not self._client:
            return {"status": "error", "message": "Supabase client not initialized"}
        
        try:
            query = self._client.table(table_name).delete()
            
            # Apply filters
            for key, value in filters.items():
                query = query.eq(key, value)
            
            result = query.execute()
            return {
                "status": "success",
                "message": "Data deleted successfully",
                "data": result.data
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to delete data: {str(e)}"
            }

# Global Supabase manager instance
supabase_manager = SupabaseManager()

def get_supabase_client() -> Optional[Client]:
    """Get the global Supabase client instance"""
    return supabase_manager.client