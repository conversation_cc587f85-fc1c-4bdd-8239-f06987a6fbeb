/**
 * Chat Box AI - Landing Page Application
 * Handles template switching, accordion functionality, and interactive features
 */

class LandingPageApp {
    constructor() {
        this.currentTemplates = {
            hero: 'modern',
            features: 'accordion',
            pricing: 'cards',
            testimonials: 'carousel',
            contact: 'form'
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeAccordion();
        this.initializeTabs();
        this.initializeCarousel();
        this.initializeTheme();
        this.loadInitialTemplates();
    }
    
    setupEventListeners() {
        // Template selector events
        document.querySelectorAll('.template-selector').forEach(selector => {
            selector.addEventListener('change', (e) => {
                const section = e.target.dataset.section;
                const template = e.target.value;
                this.switchTemplate(section, template);
            });
        });
        
        // Section visibility toggles
        document.querySelectorAll('.section-toggle').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                const section = e.target.dataset.section;
                this.toggleSection(section, e.target.checked);
            });
        });
        
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Template manager toggle
        const managerToggle = document.getElementById('manager-toggle');
        if (managerToggle) {
            managerToggle.addEventListener('click', () => this.toggleTemplateManager());
        }
    }
    
    switchTemplate(section, template) {
        if (!templates[section] || !templates[section][template]) {
            console.error(`Template ${template} not found for section ${section}`);
            return;
        }
        
        const sectionElement = document.getElementById(section);
        if (sectionElement) {
            sectionElement.innerHTML = templates[section][template];
            this.currentTemplates[section] = template;
            
            // Reinitialize interactive elements for the new template
            this.reinitializeSection(section);
            
            // Add animation
            sectionElement.style.opacity = '0';
            setTimeout(() => {
                sectionElement.style.opacity = '1';
                sectionElement.style.transition = 'opacity 0.3s ease-in-out';
            }, 50);
        }
    }
    
    toggleSection(section, visible) {
        const sectionElement = document.getElementById(section);
        if (sectionElement) {
            if (visible) {
                sectionElement.style.display = 'block';
                sectionElement.classList.add('animate-fade-in');
            } else {
                sectionElement.style.display = 'none';
                sectionElement.classList.remove('animate-fade-in');
            }
        }
    }
    
    reinitializeSection(section) {
        switch (section) {
            case 'features':
                this.initializeAccordion();
                this.initializeTabs();
                break;
            case 'testimonials':
                this.initializeCarousel();
                break;
        }
    }
    
    initializeAccordion() {
        document.querySelectorAll('.accordion-header').forEach(header => {
            header.addEventListener('click', () => {
                const target = header.dataset.target;
                const content = document.getElementById(target);
                const icon = header.querySelector('.fa-chevron-down');
                
                if (content) {
                    const isOpen = content.classList.contains('active');
                    
                    // Close all other accordions
                    document.querySelectorAll('.accordion-content').forEach(item => {
                        item.classList.remove('active');
                    });
                    document.querySelectorAll('.accordion-header .fa-chevron-down').forEach(i => {
                        i.style.transform = 'rotate(0deg)';
                    });
                    
                    if (!isOpen) {
                        content.classList.add('active');
                        if (icon) icon.style.transform = 'rotate(180deg)';
                    }
                }
            });
        });
    }
    
    initializeTabs() {
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                
                // Remove active class from all tabs and buttons
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active', 'text-primary-600', 'border-primary-600');
                    btn.classList.add('text-gray-500');
                });
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Add active class to clicked button and corresponding content
                button.classList.add('active', 'text-primary-600', 'border-primary-600');
                button.classList.remove('text-gray-500');
                
                const content = document.getElementById(tabId);
                if (content) {
                    content.classList.remove('hidden');
                }
            });
        });
    }
    
    initializeCarousel() {
        let currentSlide = 0;
        const slides = document.querySelectorAll('.testimonial-slide');
        
        if (slides.length === 0) return;
        
        const showSlide = (index) => {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        };
        
        const nextSlide = () => {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        };
        
        // Auto-advance carousel every 5 seconds
        setInterval(nextSlide, 5000);
        
        // Add navigation dots if they don't exist
        const carousel = document.querySelector('.testimonial-carousel');
        if (carousel && !carousel.querySelector('.carousel-dots')) {
            const dotsContainer = document.createElement('div');
            dotsContainer.className = 'carousel-dots flex justify-center space-x-2 mt-6';
            
            slides.forEach((_, index) => {
                const dot = document.createElement('button');
                dot.className = `w-3 h-3 rounded-full transition-colors duration-200 ${
                    index === 0 ? 'bg-primary-600' : 'bg-gray-300 dark:bg-gray-600'
                }`;
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    showSlide(currentSlide);
                    
                    // Update dot styles
                    dotsContainer.querySelectorAll('button').forEach((d, i) => {
                        d.className = `w-3 h-3 rounded-full transition-colors duration-200 ${
                            i === index ? 'bg-primary-600' : 'bg-gray-300 dark:bg-gray-600'
                        }`;
                    });
                });
                dotsContainer.appendChild(dot);
            });
            
            carousel.appendChild(dotsContainer);
        }
    }
    
    initializeTheme() {
        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add('dark');
        }
        
        // Update theme toggle button state
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const isDark = document.documentElement.classList.contains('dark');
            themeToggle.innerHTML = isDark ? 
                '<i class="fas fa-sun"></i>' : 
                '<i class="fas fa-moon"></i>';
        }
    }
    
    toggleTheme() {
        const isDark = document.documentElement.classList.toggle('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
        
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.innerHTML = isDark ? 
                '<i class="fas fa-sun"></i>' : 
                '<i class="fas fa-moon"></i>';
        }
    }
    
    toggleTemplateManager() {
        const manager = document.getElementById('template-manager');
        if (manager) {
            manager.classList.toggle('hidden');
            
            const toggle = document.getElementById('manager-toggle');
            if (toggle) {
                const isHidden = manager.classList.contains('hidden');
                toggle.innerHTML = isHidden ? 
                    '<i class="fas fa-cog mr-2"></i>Show Templates' :
                    '<i class="fas fa-times mr-2"></i>Hide Templates';
            }
        }
    }
    
    loadInitialTemplates() {
        // Load default templates for each section
        Object.keys(this.currentTemplates).forEach(section => {
            const template = this.currentTemplates[section];
            this.switchTemplate(section, template);
        });
        
        // Set initial selector values
        document.querySelectorAll('.template-selector').forEach(selector => {
            const section = selector.dataset.section;
            if (this.currentTemplates[section]) {
                selector.value = this.currentTemplates[section];
            }
        });
    }
    
    // Export configuration for sharing/saving
    exportConfig() {
        const config = {
            templates: this.currentTemplates,
            theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
            visibleSections: {}
        };
        
        // Check which sections are visible
        Object.keys(this.currentTemplates).forEach(section => {
            const element = document.getElementById(section);
            config.visibleSections[section] = element ? 
                element.style.display !== 'none' : true;
        });
        
        return config;
    }
    
    // Import configuration
    importConfig(config) {
        if (config.templates) {
            Object.keys(config.templates).forEach(section => {
                this.switchTemplate(section, config.templates[section]);
            });
        }
        
        if (config.theme) {
            const isDark = config.theme === 'dark';
            document.documentElement.classList.toggle('dark', isDark);
            localStorage.setItem('theme', config.theme);
        }
        
        if (config.visibleSections) {
            Object.keys(config.visibleSections).forEach(section => {
                this.toggleSection(section, config.visibleSections[section]);
            });
        }
    }
}

// Utility functions for smooth scrolling and animations
function smoothScrollTo(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.landingPageApp = new LandingPageApp();
    
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            smoothScrollTo(targetId);
        });
    });
    
    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);
    
    // Observe all sections for animation
    document.querySelectorAll('section, .card, .feature-item').forEach(el => {
        observer.observe(el);
    });
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LandingPageApp;
}