#!/usr/bin/env python3
"""
HexStrike AI - Real Application Endpoint Testing
Tests actual endpoints available in chat_box_server.py
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class RealEndpointTester:
    """Test actual application endpoints"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or f"http://localhost:{os.getenv('CHAT_BOX_PORT', 8888)}"
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        
    def log_result(self, test_name: str, success: bool, details: str = "", response_time: float = 0):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'details': details,
            'response_time_ms': round(response_time * 1000, 2),
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name} ({result['response_time_ms']}ms)")
        if details:
            print(f"   {details}")
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('status') == 'healthy':
                        self.log_result("Health Check", True, f"Status: {data.get('status')}", response_time)
                    else:
                        self.log_result("Health Check", True, f"Response: {data}", response_time)
                except json.JSONDecodeError:
                    self.log_result("Health Check", True, "Non-JSON response but 200 OK", response_time)
            else:
                self.log_result("Health Check", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Health Check", False, f"Error: {str(e)}")
    
    def test_root_endpoint(self):
        """Test root endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log_result("Root Endpoint", True, f"Response received", response_time)
                except json.JSONDecodeError:
                    # Might be HTML response
                    if 'html' in response.headers.get('content-type', '').lower():
                        self.log_result("Root Endpoint", True, "HTML response received", response_time)
                    else:
                        self.log_result("Root Endpoint", True, "Non-JSON response but 200 OK", response_time)
            else:
                self.log_result("Root Endpoint", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Root Endpoint", False, f"Error: {str(e)}")
    
    def test_authentication(self):
        """Test authentication endpoint"""
        try:
            # Test login
            start_time = time.time()
            login_data = {
                'username': 'admin',
                'password': 'hexstrike2024'
            }
            response = self.session.post(f"{self.base_url}/api/auth/login", 
                                       json=login_data, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'access_token' in data:
                        self.auth_token = data['access_token']
                        self.session.headers.update({'Authorization': f'Bearer {self.auth_token}'})
                        self.log_result("Authentication Login", True, "Token received", response_time)
                    else:
                        self.log_result("Authentication Login", True, "Login successful (no token)", response_time)
                except json.JSONDecodeError:
                    self.log_result("Authentication Login", False, "Invalid JSON response", response_time)
            else:
                self.log_result("Authentication Login", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Authentication Login", False, f"Error: {str(e)}")
    
    def test_token_verification(self):
        """Test token verification endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/auth/verify", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log_result("Token Verification", True, f"Response: {data}", response_time)
                except json.JSONDecodeError:
                    self.log_result("Token Verification", True, "Non-JSON response but 200 OK", response_time)
            elif response.status_code == 401:
                self.log_result("Token Verification", True, "Correctly requires authentication", response_time)
            else:
                self.log_result("Token Verification", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Token Verification", False, f"Error: {str(e)}")
    
    def test_features_endpoint(self):
        """Test features endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/features", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log_result("Features Endpoint", True, f"Features available: {len(data) if isinstance(data, (list, dict)) else 'unknown'}", response_time)
                except json.JSONDecodeError:
                    self.log_result("Features Endpoint", True, "Non-JSON response but 200 OK", response_time)
            else:
                self.log_result("Features Endpoint", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Features Endpoint", False, f"Error: {str(e)}")
    
    def test_chat_interface(self):
        """Test chat interface endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/chat-interface", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log_result("Chat Interface", True, "Interface accessible", response_time)
            else:
                self.log_result("Chat Interface", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Chat Interface", False, f"Error: {str(e)}")
    
    def test_settings_endpoints(self):
        """Test settings endpoints"""
        # Test GET settings
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/settings", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log_result("Settings GET", True, "Settings retrieved", response_time)
            elif response.status_code == 401:
                self.log_result("Settings GET", True, "Correctly requires authentication", response_time)
            else:
                self.log_result("Settings GET", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Settings GET", False, f"Error: {str(e)}")
    
    def test_database_connection(self):
        """Test database connection endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/database/test-supabase", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log_result("Database Connection", True, f"Connection status: {data.get('status', 'unknown')}", response_time)
                except json.JSONDecodeError:
                    self.log_result("Database Connection", True, "Non-JSON response but 200 OK", response_time)
            elif response.status_code == 401:
                self.log_result("Database Connection", True, "Correctly requires authentication", response_time)
            else:
                self.log_result("Database Connection", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Database Connection", False, f"Error: {str(e)}")
    
    def test_tool_endpoints(self):
        """Test some tool endpoints"""
        tools = ['nmap', 'nuclei', 'gobuster']
        
        for tool in tools:
            try:
                start_time = time.time()
                test_data = {'target': 'example.com', 'options': []}
                response = self.session.post(f"{self.base_url}/api/tools/{tool}", 
                                           json=test_data, timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    self.log_result(f"Tool {tool.upper()}", True, "Tool endpoint accessible", response_time)
                elif response.status_code == 401:
                    self.log_result(f"Tool {tool.upper()}", True, "Correctly requires authentication", response_time)
                elif response.status_code == 400:
                    self.log_result(f"Tool {tool.upper()}", True, "Tool validates input (400 expected)", response_time)
                else:
                    self.log_result(f"Tool {tool.upper()}", False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(f"Tool {tool.upper()}", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all endpoint tests"""
        print("\n🚀 Starting HexStrike AI Real Endpoint Tests...\n")
        
        # Basic connectivity tests
        self.test_health_endpoint()
        self.test_root_endpoint()
        
        # Authentication tests
        self.test_authentication()
        self.test_token_verification()
        
        # Core functionality tests
        self.test_features_endpoint()
        self.test_chat_interface()
        self.test_settings_endpoints()
        self.test_database_connection()
        
        # Tool endpoint tests
        self.test_tool_endpoints()
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📊 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Save detailed results
        results_file = 'real_endpoint_test_results.json'
        with open(results_file, 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'success_rate': success_rate,
                    'timestamp': datetime.now().isoformat()
                },
                'detailed_results': self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")

def main():
    """Main test execution"""
    tester = RealEndpointTester()
    tester.run_all_tests()
    
    # Exit with error code if tests failed
    failed_count = sum(1 for result in tester.test_results if not result['success'])
    sys.exit(1 if failed_count > 0 else 0)

if __name__ == "__main__":
    main()