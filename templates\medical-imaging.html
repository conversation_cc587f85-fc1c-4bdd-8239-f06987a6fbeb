<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Imaging (PACS) - HexStrike AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 0;
            text-align: center;
        }
        .tabs {
            display: flex;
            background-color: #34495e;
            margin-bottom: 20px;
        }
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            color: white;
            background-color: transparent;
            border: none;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .tab.active {
            background-color: #2980b9;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tab-content.active {
            display: block;
        }
        .study-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .study-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .study-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .study-card h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .study-card p {
            margin: 5px 0;
            color: #7f8c8d;
        }
        .study-card .btn {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        .study-card .btn:hover {
            background-color: #2980b9;
        }
        .viewer-container {
            display: flex;
            height: 600px;
            background-color: #000;
            border-radius: 5px;
            overflow: hidden;
        }
        .thumbnail-panel {
            width: 200px;
            background-color: #2c3e50;
            padding: 10px;
            overflow-y: auto;
        }
        .thumbnail {
            width: 100%;
            height: 100px;
            background-color: #34495e;
            margin-bottom: 10px;
            border-radius: 3px;
            cursor: pointer;
            background-size: cover;
            background-position: center;
        }
        .thumbnail.active {
            border: 2px solid #3498db;
        }
        .main-viewer {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        .main-viewer img {
            max-width: 100%;
            max-height: 100%;
        }
        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            display: flex;
            gap: 10px;
        }
        .control-btn {
            background-color: transparent;
            border: 1px solid #fff;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        .control-btn:hover {
            background-color: rgba(255,255,255,0.1);
        }
        .advanced-viewer {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .advanced-controls {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: #2c3e50;
        }
        .advanced-view {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #000;
        }
        .advanced-view img {
            max-width: 100%;
            max-height: 100%;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: #2ecc71;
            color: white;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            transform: translateX(200%);
            transition: transform 0.3s ease-out;
            z-index: 1000;
        }
        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <header>
        <h1>Medical Imaging (PACS)</h1>
    </header>
    
    <div class="container">
        <div class="tabs">
            <button class="tab active" onclick="openTab('image-viewer')">Image Viewer</button>
            <button class="tab" onclick="openTab('advanced-viewer')">Advanced Viewer</button>
            <button class="tab" onclick="openTab('reports')">Reports</button>
        </div>
        
        <div id="image-viewer" class="tab-content active">
            <h2>Study Selection</h2>
            <div class="study-list">
                <div class="study-card">
                    <h3>MR Brain</h3>
                    <p>Patient ID: MR-12345</p>
                    <p>Date: 2025-09-15</p>
                    <p>Type: MR</p>
                    <button class="btn" onclick="loadStudy('MR-12345', 'MR')">View Study</button>
                </div>
                <div class="study-card">
                    <h3>CT Chest</h3>
                    <p>Patient ID: CT-67890</p>
                    <p>Date: 2025-09-14</p>
                    <p>Type: CT</p>
                    <button class="btn" onclick="loadStudy('CT-67890', 'CT')">View Study</button>
                </div>
                <div class="study-card">
                    <h3>CR Hand</h3>
                    <p>Patient ID: CR-24680</p>
                    <p>Date: 2025-09-13</p>
                    <p>Type: CR</p>
                    <button class="btn" onclick="loadStudy('CR-24680', 'CR')">View Study</button>
                </div>
            </div>
            
            <div id="viewer-container" class="viewer-container" style="display: none;">
                <div class="thumbnail-panel" id="thumbnail-panel">
                    <!-- Thumbnails will be added here dynamically -->
                </div>
                <div class="main-viewer">
                    <img id="main-image" src="" alt="Medical Image">
                    <div class="controls">
                        <button class="control-btn" onclick="zoomIn()">Zoom In</button>
                        <button class="control-btn" onclick="zoomOut()">Zoom Out</button>
                        <button class="control-btn" onclick="resetView()">Reset</button>
                        <button class="control-btn" id="advanced-viewer-btn" onclick="openAdvancedViewer()">Open Advanced Viewer</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="advanced-viewer" class="tab-content">
            <div class="advanced-viewer">
                <div class="advanced-controls">
                    <div>
                        <button class="control-btn">3D View</button>
                        <button class="control-btn">MPR</button>
                        <button class="control-btn">Measurements</button>
                    </div>
                    <div>
                        <button class="control-btn">Export</button>
                        <button class="control-btn">Settings</button>
                    </div>
                </div>
                <div class="advanced-view" id="advanced-view">
                    <img id="advanced-image" src="" alt="Advanced Medical Image View">
                </div>
            </div>
        </div>
        
        <div id="reports" class="tab-content">
            <h2>Study Reports</h2>
            <p>No reports available for the selected study.</p>
        </div>
    </div>
    
    <div class="notification" id="notification">
        Image loaded in Advanced Viewer tab
    </div>

    <script>
        // Current study information
        let currentStudy = {
            id: null,
            type: null,
            images: []
        };
        
        // Sample image data for different study types
        const studyImages = {
            'MR-12345': [
                '/assets/mr_brain_1.jpg',
                '/assets/mr_brain_2.jpg',
                '/assets/mr_brain_3.jpg',
                '/assets/mr_brain_4.jpg'
            ],
            'CT-67890': [
                '/assets/ct_chest_1.jpg',
                '/assets/ct_chest_2.jpg',
                '/assets/ct_chest_3.jpg'
            ],
            'CR-24680': [
                '/assets/cr_hand_1.jpg',
                '/assets/cr_hand_2.jpg'
            ]
        };
        
        // Since we don't have actual images, we'll use placeholder images
        const placeholderImages = {
            'MR': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMzMzMyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNmZmZmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1SIEJyYWluIEltYWdlPC90ZXh0Pjwvc3ZnPg==',
            'CT': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMzMzMyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNmZmZmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPkNUIENoZXN0IEltYWdlPC90ZXh0Pjwvc3ZnPg==',
            'CR': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iIzMzMzMzMyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNmZmZmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPkNSIEhhbmQgSW1hZ2U8L3RleHQ+PC9zdmc+'
        };
        
        // Function to open a tab
        function openTab(tabName) {
            // Hide all tab contents
            const tabContents = document.getElementsByClassName('tab-content');
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }
            
            // Remove active class from all tabs
            const tabs = document.getElementsByClassName('tab');
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // Show the selected tab content and mark the tab as active
            document.getElementById(tabName).classList.add('active');
            
            // Find and activate the corresponding tab button
            const tabButtons = document.getElementsByClassName('tab');
            for (let i = 0; i < tabButtons.length; i++) {
                if (tabButtons[i].textContent.toLowerCase().includes(tabName.replace('-', ' '))) {
                    tabButtons[i].classList.add('active');
                }
            }
        }
        
        // Function to load a study
        function loadStudy(studyId, studyType) {
            currentStudy.id = studyId;
            currentStudy.type = studyType;
            
            // Show the viewer container
            document.getElementById('viewer-container').style.display = 'flex';
            
            // Clear existing thumbnails
            const thumbnailPanel = document.getElementById('thumbnail-panel');
            thumbnailPanel.innerHTML = '';
            
            // Generate thumbnails (in a real app, these would be actual study images)
            const numImages = studyType === 'MR' ? 4 : (studyType === 'CT' ? 3 : 2);
            for (let i = 0; i < numImages; i++) {
                const thumbnail = document.createElement('div');
                thumbnail.className = 'thumbnail';
                thumbnail.style.backgroundImage = `url(${placeholderImages[studyType]})`;
                thumbnail.dataset.index = i;
                thumbnail.onclick = function() {
                    selectImage(i);
                };
                thumbnailPanel.appendChild(thumbnail);
            }
            
            // Select the first image
            selectImage(0);
        }
        
        // Function to select an image
        function selectImage(index) {
            // Update thumbnails
            const thumbnails = document.getElementsByClassName('thumbnail');
            for (let i = 0; i < thumbnails.length; i++) {
                thumbnails[i].classList.remove('active');
            }
            thumbnails[index].classList.add('active');
            
            // Update main image
            document.getElementById('main-image').src = placeholderImages[currentStudy.type];
        }
        
        // Function to open the advanced viewer
        function openAdvancedViewer() {
            // Show notification
            const notification = document.getElementById('notification');
            notification.classList.add('show');
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
            
            // Switch to advanced viewer tab
            openTab('advanced-viewer');
            
            // Load the current image in the advanced viewer
            document.getElementById('advanced-image').src = placeholderImages[currentStudy.type];
        }
        
        // Zoom functions
        function zoomIn() {
            const img = document.getElementById('main-image');
            const currentWidth = img.clientWidth;
            img.style.width = (currentWidth * 1.1) + 'px';
        }
        
        function zoomOut() {
            const img = document.getElementById('main-image');
            const currentWidth = img.clientWidth;
            img.style.width = (currentWidth * 0.9) + 'px';
        }
        
        function resetView() {
            const img = document.getElementById('main-image');
            img.style.width = '';
        }
    </script>
</body>
</html>