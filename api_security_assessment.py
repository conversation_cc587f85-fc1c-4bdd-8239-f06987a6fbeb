#!/usr/bin/env python3
"""
HexStrike AI - API Security Assessment Tool
Phase 5: API & Cloud Security Testing

This tool performs comprehensive API security testing including:
- REST endpoint fuzzing
- Authentication schema review
- Rate limiting tests
- Input validation testing
- Authorization bypass attempts
"""

import requests
import json
import time
import threading
from datetime import datetime
from urllib.parse import urljoin
import base64
import hashlib
import random
import string
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_security_assessment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class SecurityFinding:
    """Represents a security finding from API testing"""
    id: str
    severity: str  # Critical, High, Medium, Low
    title: str
    description: str
    endpoint: str
    method: str
    cwe: str
    cvss_score: float
    evidence: str
    reproduction_steps: List[str]
    remediation: str
    timestamp: str

class APISecurityAssessment:
    """Main API Security Assessment class"""
    
    def __init__(self, base_url: str, session_id: str):
        self.base_url = base_url.rstrip('/')
        self.session_id = session_id
        self.session = requests.Session()
        self.findings: List[SecurityFinding] = []
        self.test_results = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_limits_hit': 0,
            'avg_response_time': 0,
            'vulnerabilities_found': 0
        }
        
        # Common payloads for testing
        self.xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "'><script>alert('XSS')</script>",
            "\"><script>alert('XSS')</script>"
        ]
        
        self.sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1 --"
        ]
        
        self.path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
        ]
        
        # Set session headers
        self.session.headers.update({
            'User-Agent': 'HexStrike-Security-Assessment/1.0',
            'Accept': 'application/json, text/html, */*',
            'Content-Type': 'application/json'
        })
    
    def log_finding(self, finding: SecurityFinding):
        """Log a security finding"""
        self.findings.append(finding)
        logger.warning(f"SECURITY FINDING [{finding.severity}]: {finding.title} - {finding.endpoint}")
        self.test_results['vulnerabilities_found'] += 1
    
    def make_request(self, method: str, endpoint: str, **kwargs) -> Optional[requests.Response]:
        """Make a request with error handling and metrics tracking"""
        url = urljoin(self.base_url, endpoint)
        start_time = time.time()
        
        try:
            self.test_results['total_requests'] += 1
            response = self.session.request(method, url, timeout=10, **kwargs)
            
            # Update metrics
            response_time = time.time() - start_time
            self.test_results['avg_response_time'] = (
                (self.test_results['avg_response_time'] * (self.test_results['total_requests'] - 1) + response_time) /
                self.test_results['total_requests']
            )
            
            if response.status_code == 429:
                self.test_results['rate_limits_hit'] += 1
                logger.info(f"Rate limit hit on {endpoint}")
                time.sleep(2)  # Backoff
            elif response.status_code < 400:
                self.test_results['successful_requests'] += 1
            else:
                self.test_results['failed_requests'] += 1
            
            return response
            
        except requests.exceptions.RequestException as e:
            self.test_results['failed_requests'] += 1
            logger.error(f"Request failed for {endpoint}: {str(e)}")
            return None
    
    def test_authentication_bypass(self):
        """Test for authentication bypass vulnerabilities"""
        logger.info("Testing authentication bypass...")
        
        # Test endpoints that should require authentication
        protected_endpoints = [
            '/api/chat',
            '/api/auth/verify',
            '/api/security/dashboard'
        ]
        
        for endpoint in protected_endpoints:
            # Test without authentication
            response = self.make_request('GET', endpoint)
            if response and response.status_code == 200:
                self.log_finding(SecurityFinding(
                    id=f"AUTH-BYPASS-{len(self.findings)+1:03d}",
                    severity="High",
                    title="Authentication Bypass",
                    description=f"Endpoint {endpoint} accessible without authentication",
                    endpoint=endpoint,
                    method="GET",
                    cwe="CWE-287",
                    cvss_score=7.5,
                    evidence=f"HTTP {response.status_code} response received without authentication",
                    reproduction_steps=[f"1. Send GET request to {endpoint} without authentication headers"],
                    remediation="Implement proper authentication checks on all protected endpoints",
                    timestamp=datetime.now().isoformat()
                ))
    
    def test_input_validation(self):
        """Test input validation on API endpoints"""
        logger.info("Testing input validation...")
        
        # Test XSS on various endpoints
        test_endpoints = [
            ('/api/auth/login', 'POST', {'username': '', 'password': ''}),
            ('/api/auth/register', 'POST', {'username': '', 'password': '', 'email': ''}),
            ('/api/chat', 'POST', {'message': ''})
        ]
        
        for endpoint, method, base_payload in test_endpoints:
            for xss_payload in self.xss_payloads[:3]:  # Limit to avoid too many requests
                test_payload = base_payload.copy()
                for key in test_payload.keys():
                    test_payload[key] = xss_payload
                
                response = self.make_request(method, endpoint, json=test_payload)
                if response and xss_payload in response.text:
                    self.log_finding(SecurityFinding(
                        id=f"XSS-{len(self.findings)+1:03d}",
                        severity="High",
                        title="Cross-Site Scripting (XSS)",
                        description=f"XSS vulnerability found in {endpoint}",
                        endpoint=endpoint,
                        method=method,
                        cwe="CWE-79",
                        cvss_score=6.1,
                        evidence=f"Payload '{xss_payload}' reflected in response",
                        reproduction_steps=[
                            f"1. Send {method} request to {endpoint}",
                            f"2. Include payload: {xss_payload}",
                            "3. Observe payload reflected in response"
                        ],
                        remediation="Implement proper output encoding and input validation",
                        timestamp=datetime.now().isoformat()
                    ))
    
    def test_rate_limiting(self):
        """Test rate limiting implementation"""
        logger.info("Testing rate limiting...")
        
        # Test rate limiting on login endpoint
        endpoint = '/api/auth/login'
        rapid_requests = 20
        
        start_time = time.time()
        rate_limited = False
        
        for i in range(rapid_requests):
            response = self.make_request('POST', endpoint, json={
                'username': f'test{i}',
                'password': 'invalid'
            })
            
            if response and response.status_code == 429:
                rate_limited = True
                break
            
            time.sleep(0.1)  # Small delay between requests
        
        if not rate_limited:
            self.log_finding(SecurityFinding(
                id=f"RATE-LIMIT-{len(self.findings)+1:03d}",
                severity="Medium",
                title="Missing Rate Limiting",
                description=f"No rate limiting detected on {endpoint}",
                endpoint=endpoint,
                method="POST",
                cwe="CWE-770",
                cvss_score=5.3,
                evidence=f"Sent {rapid_requests} requests without rate limiting",
                reproduction_steps=[
                    f"1. Send {rapid_requests} rapid requests to {endpoint}",
                    "2. Observe no rate limiting response (HTTP 429)"
                ],
                remediation="Implement rate limiting to prevent abuse",
                timestamp=datetime.now().isoformat()
            ))
    
    def test_security_headers(self):
        """Test for security headers"""
        logger.info("Testing security headers...")
        
        response = self.make_request('GET', '/')
        if not response:
            return
        
        required_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=',
            'Content-Security-Policy': 'default-src'
        }
        
        missing_headers = []
        for header, expected in required_headers.items():
            if header not in response.headers:
                missing_headers.append(header)
            elif isinstance(expected, list):
                if not any(exp in response.headers[header] for exp in expected):
                    missing_headers.append(header)
            elif expected not in response.headers[header]:
                missing_headers.append(header)
        
        if missing_headers:
            self.log_finding(SecurityFinding(
                id=f"SEC-HEADERS-{len(self.findings)+1:03d}",
                severity="Medium",
                title="Missing Security Headers",
                description=f"Missing security headers: {', '.join(missing_headers)}",
                endpoint="/",
                method="GET",
                cwe="CWE-693",
                cvss_score=4.3,
                evidence=f"Headers missing: {missing_headers}",
                reproduction_steps=[
                    "1. Send GET request to application root",
                    "2. Check response headers for security headers"
                ],
                remediation="Add missing security headers to prevent various attacks",
                timestamp=datetime.now().isoformat()
            ))
    
    def test_information_disclosure(self):
        """Test for information disclosure"""
        logger.info("Testing information disclosure...")
        
        # Test for verbose error messages
        test_endpoints = [
            '/api/nonexistent',
            '/api/auth/login',
            '/api/chat'
        ]
        
        for endpoint in test_endpoints:
            response = self.make_request('GET', endpoint)
            if response and response.status_code >= 400:
                # Check for sensitive information in error responses
                sensitive_patterns = [
                    'traceback', 'stack trace', 'exception',
                    'database', 'sql', 'mysql', 'postgresql',
                    'file not found', 'path', 'directory'
                ]
                
                response_text = response.text.lower()
                found_patterns = [p for p in sensitive_patterns if p in response_text]
                
                if found_patterns:
                    self.log_finding(SecurityFinding(
                        id=f"INFO-DISC-{len(self.findings)+1:03d}",
                        severity="Low",
                        title="Information Disclosure",
                        description=f"Verbose error messages on {endpoint}",
                        endpoint=endpoint,
                        method="GET",
                        cwe="CWE-209",
                        cvss_score=3.1,
                        evidence=f"Found patterns: {found_patterns}",
                        reproduction_steps=[
                            f"1. Send GET request to {endpoint}",
                            "2. Observe verbose error message in response"
                        ],
                        remediation="Implement generic error messages for production",
                        timestamp=datetime.now().isoformat()
                    ))
    
    def run_assessment(self):
        """Run the complete API security assessment"""
        logger.info(f"Starting API Security Assessment for {self.base_url}")
        logger.info(f"Session ID: {self.session_id}")
        
        start_time = time.time()
        
        # Run all tests
        test_methods = [
            self.test_security_headers,
            self.test_authentication_bypass,
            self.test_input_validation,
            self.test_rate_limiting,
            self.test_information_disclosure
        ]
        
        for test_method in test_methods:
            try:
                test_method()
                time.sleep(1)  # Brief pause between test suites
            except Exception as e:
                logger.error(f"Error in {test_method.__name__}: {str(e)}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Generate summary
        logger.info(f"Assessment completed in {duration:.2f} seconds")
        logger.info(f"Total findings: {len(self.findings)}")
        
        # Count findings by severity
        severity_counts = {}
        for finding in self.findings:
            severity_counts[finding.severity] = severity_counts.get(finding.severity, 0) + 1
        
        logger.info(f"Findings by severity: {severity_counts}")
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate assessment report"""
        report = {
            'assessment_info': {
                'target': self.base_url,
                'session_id': self.session_id,
                'timestamp': datetime.now().isoformat(),
                'total_findings': len(self.findings)
            },
            'metrics': self.test_results,
            'findings': [{
                'id': f.id,
                'severity': f.severity,
                'title': f.title,
                'description': f.description,
                'endpoint': f.endpoint,
                'method': f.method,
                'cwe': f.cwe,
                'cvss_score': f.cvss_score,
                'evidence': f.evidence,
                'reproduction_steps': f.reproduction_steps,
                'remediation': f.remediation,
                'timestamp': f.timestamp
            } for f in self.findings],
            'summary': {
                'critical': len([f for f in self.findings if f.severity == 'Critical']),
                'high': len([f for f in self.findings if f.severity == 'High']),
                'medium': len([f for f in self.findings if f.severity == 'Medium']),
                'low': len([f for f in self.findings if f.severity == 'Low'])
            }
        }
        
        return report

def main():
    """Main function to run the assessment"""
    # Configuration
    BASE_URL = "http://localhost:8888"
    SESSION_ID = "hexstrike_assessment_" + datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Run assessment
    assessment = APISecurityAssessment(BASE_URL, SESSION_ID)
    report = assessment.run_assessment()
    
    # Save report
    report_filename = f"api_security_report_{SESSION_ID}.json"
    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Report saved to {report_filename}")
    
    return report

if __name__ == "__main__":
    main()