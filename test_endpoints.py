#!/usr/bin/env python3
"""
HexStrike AI - Comprehensive Endpoint Testing
Tests all API endpoints for production readiness
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class EndpointTester:
    """Comprehensive API endpoint testing"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or f"http://localhost:{os.getenv('CHAT_BOX_PORT', 8888)}"
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        
    def log_result(self, test_name: str, success: bool, details: str = "", response_time: float = 0):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'details': details,
            'response_time_ms': round(response_time * 1000, 2),
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name} ({result['response_time_ms']}ms)")
        if details:
            print(f"   {details}")
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'healthy':
                    self.log_result("Health Check", True, f"Status: {data.get('status')}", response_time)
                else:
                    self.log_result("Health Check", False, f"Unexpected status: {data.get('status')}", response_time)
            else:
                self.log_result("Health Check", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Health Check", False, f"Error: {str(e)}")
    
    def test_root_endpoint(self):
        """Test root documentation endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if 'service' in data and 'documentation' in data:
                    self.log_result("Root Documentation", True, f"Service: {data.get('service')}", response_time)
                else:
                    self.log_result("Root Documentation", False, "Missing required fields", response_time)
            else:
                self.log_result("Root Documentation", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Root Documentation", False, f"Error: {str(e)}")
    
    def test_authentication(self):
        """Test authentication endpoint"""
        try:
            # Test login
            start_time = time.time()
            login_data = {
                'username': 'admin',
                'password': 'hexstrike2024'
            }
            response = self.session.post(f"{self.base_url}/api/auth/login", 
                                       json=login_data, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if 'access_token' in data:
                    self.auth_token = data['access_token']
                    self.session.headers.update({'Authorization': f'Bearer {self.auth_token}'})
                    self.log_result("Authentication Login", True, "Token received", response_time)
                else:
                    self.log_result("Authentication Login", False, "No access token in response", response_time)
            else:
                self.log_result("Authentication Login", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Authentication Login", False, f"Error: {str(e)}")
    
    def test_token_verification(self):
        """Test token verification endpoint"""
        if not self.auth_token:
            self.log_result("Token Verification", False, "No auth token available")
            return
            
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/auth/verify", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('valid') is True:
                    self.log_result("Token Verification", True, f"User: {data.get('user')}", response_time)
                else:
                    self.log_result("Token Verification", False, "Token not valid", response_time)
            else:
                self.log_result("Token Verification", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_result("Token Verification", False, f"Error: {str(e)}")
    
    def test_public_endpoints(self):
        """Test public endpoints"""
        endpoints = [
            ('/api/test/public', 'Public Test Endpoint'),
            ('/api/features', 'Features Endpoint')
        ]
        
        for endpoint, name in endpoints:
            try:
                start_time = time.time()
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    self.log_result(name, True, "Response received", response_time)
                else:
                    self.log_result(name, False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(name, False, f"Error: {str(e)}")
    
    def test_protected_endpoints(self):
        """Test protected endpoints"""
        if not self.auth_token:
            self.log_result("Protected Endpoints", False, "No auth token available")
            return
            
        endpoints = [
            ('/api/test/protected', 'Protected Test Endpoint')
        ]
        
        for endpoint, name in endpoints:
            try:
                start_time = time.time()
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_result(name, True, f"User: {data.get('user', 'Unknown')}", response_time)
                elif response.status_code == 401:
                    self.log_result(name, False, "Authentication required", response_time)
                else:
                    self.log_result(name, False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_result(name, False, f"Error: {str(e)}")
    
    def test_cors_headers(self):
        """Test CORS headers"""
        try:
            start_time = time.time()
            response = self.session.options(f"{self.base_url}/api/test/public", 
                                          headers={'Origin': 'http://localhost:3000'})
            response_time = time.time() - start_time
            
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
            }
            
            if any(cors_headers.values()):
                self.log_result("CORS Headers", True, f"Headers present: {list(cors_headers.keys())}", response_time)
            else:
                self.log_result("CORS Headers", False, "No CORS headers found", response_time)
                
        except Exception as e:
            self.log_result("CORS Headers", False, f"Error: {str(e)}")
    
    def test_security_headers(self):
        """Test security headers"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            response_time = time.time() - start_time
            
            security_headers = {
                'X-Content-Type-Options': response.headers.get('X-Content-Type-Options'),
                'X-Frame-Options': response.headers.get('X-Frame-Options'),
                'X-XSS-Protection': response.headers.get('X-XSS-Protection'),
                'Strict-Transport-Security': response.headers.get('Strict-Transport-Security')
            }
            
            present_headers = [k for k, v in security_headers.items() if v]
            
            if len(present_headers) >= 2:
                self.log_result("Security Headers", True, f"Headers: {', '.join(present_headers)}", response_time)
            else:
                self.log_result("Security Headers", False, f"Only {len(present_headers)} security headers found", response_time)
                
        except Exception as e:
            self.log_result("Security Headers", False, f"Error: {str(e)}")
    
    def test_rate_limiting(self):
        """Test rate limiting (if enabled)"""
        try:
            # Make multiple rapid requests
            responses = []
            for i in range(5):
                start_time = time.time()
                response = self.session.get(f"{self.base_url}/health", timeout=5)
                response_time = time.time() - start_time
                responses.append((response.status_code, response_time))
                time.sleep(0.1)  # Small delay between requests
            
            # Check if any requests were rate limited
            rate_limited = any(status == 429 for status, _ in responses)
            avg_response_time = sum(rt for _, rt in responses) / len(responses)
            
            if rate_limited:
                self.log_result("Rate Limiting", True, "Rate limiting active", avg_response_time)
            else:
                self.log_result("Rate Limiting", True, "No rate limiting detected (may be disabled)", avg_response_time)
                
        except Exception as e:
            self.log_result("Rate Limiting", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run comprehensive endpoint tests"""
        print("\n🧪 HexStrike AI - Comprehensive Endpoint Testing")
        print("=" * 50)
        print(f"Target: {self.base_url}")
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Run tests in order
        self.test_health_endpoint()
        self.test_root_endpoint()
        self.test_authentication()
        self.test_token_verification()
        self.test_public_endpoints()
        self.test_protected_endpoints()
        self.test_cors_headers()
        self.test_security_headers()
        self.test_rate_limiting()
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        avg_response_time = sum(result['response_time_ms'] for result in self.test_results) / total_tests
        
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"⚡ Avg Response Time: {avg_response_time:.1f}ms")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        print(f"\n📝 Detailed results saved to: endpoint_test_results.json")
        
        # Save detailed results
        with open('endpoint_test_results.json', 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'success_rate': round((passed_tests/total_tests)*100, 1),
                    'avg_response_time_ms': round(avg_response_time, 1),
                    'test_timestamp': datetime.now().isoformat()
                },
                'results': self.test_results
            }, f, indent=2)
        
        return passed_tests == total_tests

def main():
    """Main testing function"""
    # Check if server is running
    port = os.getenv('CHAT_BOX_PORT', 8888)
    base_url = f"http://localhost:{port}"
    
    print(f"🔍 Checking if server is running on {base_url}...")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=15)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running")
        print(f"💡 Start the server with: python test_server.py")
        return False
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")
        return False
    
    # Run tests
    tester = EndpointTester(base_url)
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! API is ready for production.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please review and fix issues before deployment.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)