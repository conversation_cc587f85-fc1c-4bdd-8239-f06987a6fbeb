#!/usr/bin/env python3
"""
HexStrike AI - Interactive Feature Demonstration
Demonstrates how to use various features of the application
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class HexStrikeDemo:
    """Interactive demonstration of HexStrike AI features"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or f"http://localhost:{os.getenv('CHAT_BOX_PORT', 8888)}"
        self.session = requests.Session()
        self.auth_token = None
        
    def authenticate(self):
        """Authenticate with the application"""
        print("🔐 Authenticating with HexStrike AI...")
        try:
            login_data = {
                'username': 'admin',
                'password': 'hexstrike2024'
            }
            response = self.session.post(f"{self.base_url}/api/auth/login", 
                                       json=login_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'access_token' in data:
                    self.auth_token = data['access_token']
                    self.session.headers.update({'Authorization': f'Bearer {self.auth_token}'})
                    print("✅ Authentication successful!")
                    return True
            
            print("❌ Authentication failed")
            return False
                
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False
    
    def demo_features_discovery(self):
        """Demonstrate features discovery"""
        print("\n🎯 Discovering Available Features...")
        try:
            response = self.session.get(f"{self.base_url}/api/features", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Available Feature Categories:")
                
                if isinstance(data, dict):
                    for category, items in data.items():
                        if isinstance(items, list):
                            print(f"   📂 {category.title()}: {len(items)} tools available")
                            # Show first few tools as examples
                            if len(items) > 0:
                                examples = items[:3] if len(items) > 3 else items
                                print(f"      Examples: {', '.join(examples)}")
                        else:
                            print(f"   📂 {category.title()}: {items}")
                else:
                    print(f"   Found {len(data)} features")
            else:
                print(f"❌ Failed to get features: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error discovering features: {str(e)}")
    
    def demo_security_tool(self, tool_name: str, demo_data: dict):
        """Demonstrate a security tool"""
        print(f"\n🛡️ Demonstrating {tool_name.upper()} Tool...")
        try:
            response = self.session.post(f"{self.base_url}/api/tools/{tool_name}", 
                                       json=demo_data, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {tool_name.upper()} executed successfully!")
                
                # Show relevant output fields
                if 'output' in data:
                    output = data['output'][:200] + "..." if len(data['output']) > 200 else data['output']
                    print(f"   Output: {output}")
                if 'command' in data:
                    print(f"   Command: {data['command']}")
                if 'execution_time' in data:
                    print(f"   Execution Time: {data['execution_time']}s")
                    
            elif response.status_code == 400:
                print(f"⚠️  {tool_name.upper()}: Input validation triggered (this is expected for demo data)")
            else:
                print(f"❌ {tool_name.upper()} failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error with {tool_name.upper()}: {str(e)}")
    
    def demo_ai_features(self):
        """Demonstrate AI-powered features"""
        print("\n🤖 Demonstrating AI Features...")
        
        # AI Payload Generation
        print("\n   🎯 AI Payload Generation:")
        try:
            payload_data = {
                'target_type': 'web',
                'vulnerability': 'xss',
                'context': 'input field'
            }
            response = self.session.post(f"{self.base_url}/api/ai/generate_payload", 
                                       json=payload_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("   ✅ AI payload generated successfully!")
                if 'payload' in data:
                    print(f"      Generated Payload: {data['payload']}")
            else:
                print(f"   ⚠️  AI Payload Generation: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ AI Payload Generation error: {str(e)}")
        
        # Target Analysis
        print("\n   🎯 AI Target Analysis:")
        try:
            analysis_data = {
                'target': 'example.com',
                'scan_type': 'comprehensive'
            }
            response = self.session.post(f"{self.base_url}/api/intelligence/analyze-target", 
                                       json=analysis_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("   ✅ Target analysis completed!")
                if 'analysis' in data:
                    print(f"      Analysis: {str(data['analysis'])[:100]}...")
            else:
                print(f"   ⚠️  Target Analysis: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Target Analysis error: {str(e)}")
    
    def demo_workflows(self):
        """Demonstrate workflow features"""
        print("\n⚡ Demonstrating Workflow Features...")
        
        workflows = [
            ('/api/bugbounty/reconnaissance-workflow', 'Bug Bounty Reconnaissance', 
             {'target': 'example.com', 'scope': 'subdomain'}),
            ('/api/ctf/suggest-tools', 'CTF Tool Suggestion', 
             {'challenge_type': 'web', 'difficulty': 'medium'}),
            ('/api/bugbounty/vulnerability-hunting-workflow', 'Vulnerability Hunting', 
             {'target': 'example.com', 'focus': 'web'})
        ]
        
        for endpoint, name, demo_data in workflows:
            print(f"\n   🎯 {name}:")
            try:
                response = self.session.post(f"{self.base_url}{endpoint}", 
                                           json=demo_data, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ {name} workflow executed!")
                    if 'workflow' in data:
                        print(f"      Workflow: {str(data['workflow'])[:100]}...")
                elif response.status_code == 400:
                    print(f"   ⚠️  {name}: Input validation (expected for demo)")
                else:
                    print(f"   ❌ {name}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {name} error: {str(e)}")
    
    def demo_file_operations(self):
        """Demonstrate file operations"""
        print("\n📁 Demonstrating File Operations...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/files/list", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ File operations accessible!")
                if 'files' in data:
                    print(f"   Found {len(data['files'])} files")
                elif isinstance(data, list):
                    print(f"   Found {len(data)} items")
            else:
                print(f"⚠️  File Operations: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ File Operations error: {str(e)}")
    
    def demo_database_features(self):
        """Demonstrate database connectivity"""
        print("\n🗄️ Demonstrating Database Features...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/database/test-supabase", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                print(f"✅ Database connection: {status}")
                if 'details' in data:
                    print(f"   Details: {data['details']}")
            else:
                print(f"⚠️  Database test: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Database error: {str(e)}")
    
    def show_web_interfaces(self):
        """Show available web interfaces"""
        print("\n🌐 Available Web Interfaces:")
        print(f"   🏠 Main Interface: {self.base_url}/")
        print(f"   💬 Chat Interface: {self.base_url}/chat-interface")
        print(f"   ⚙️  Settings: {self.base_url}/settings")
        print("\n   💡 Tip: Open these URLs in your browser to access the web interface!")
    
    def run_demo(self):
        """Run the complete demonstration"""
        print("\n" + "=" * 70)
        print("🚀 HexStrike AI - Interactive Feature Demonstration")
        print("=" * 70)
        
        # Authenticate
        if not self.authenticate():
            print("\n❌ Cannot proceed without authentication")
            return
        
        # Show web interfaces
        self.show_web_interfaces()
        
        # Discover features
        self.demo_features_discovery()
        
        # Demonstrate security tools
        print("\n🛡️ Security Tools Demonstration:")
        security_tools = [
            ('nmap', {'target': '127.0.0.1', 'options': ['-sn']}),
            ('nuclei', {'target': 'https://example.com', 'templates': ['basic']}),
            ('gobuster', {'target': 'https://example.com', 'wordlist': 'common'})
        ]
        
        for tool, demo_data in security_tools:
            self.demo_security_tool(tool, demo_data)
        
        # Demonstrate AI features
        self.demo_ai_features()
        
        # Demonstrate workflows
        self.demo_workflows()
        
        # Demonstrate file operations
        self.demo_file_operations()
        
        # Demonstrate database features
        self.demo_database_features()
        
        # Final summary
        print("\n" + "=" * 70)
        print("🎉 HexStrike AI Feature Demonstration Complete!")
        print("=" * 70)
        print("\n📋 What you've seen:")
        print("   ✅ Authentication system")
        print("   ✅ 100+ Security tools integration")
        print("   ✅ AI-powered payload generation")
        print("   ✅ Intelligent target analysis")
        print("   ✅ Automated workflows for bug bounty & CTF")
        print("   ✅ File management operations")
        print("   ✅ Database connectivity")
        print("   ✅ Web-based interfaces")
        
        print("\n🌟 Next Steps:")
        print("   1. Open the web interface in your browser")
        print("   2. Explore the chat interface for interactive usage")
        print("   3. Configure settings for your specific needs")
        print("   4. Start using the security tools for your projects")
        
        print(f"\n🔗 Access the application at: {self.base_url}")
        print("\n" + "=" * 70)

def main():
    """Main demonstration execution"""
    demo = HexStrikeDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()