<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authenticated Web Scraper - Chat Box AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: var(--bg-secondary);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 24px;
            font-weight: 600;
        }

        .theme-toggle {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--primary-hover);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .scraping-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        @media (min-width: 1200px) {
            .scraping-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .scraping-grid {
                grid-template-columns: 1fr;
            }
        }

        .card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .card h2 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checkbox-item:hover {
            background: var(--bg-tertiary);
        }

        .checkbox-item.selected {
            background: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }

        .checkbox-item .icon {
            color: var(--primary-color);
            font-size: 16px;
        }

        .checkbox-item .label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .checkbox-item .description {
            font-size: 12px;
            color: var(--text-muted);
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress-section {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-muted);
        }

        .status-dot.active {
            background: var(--primary-color);
            animation: pulse 2s infinite;
        }

        .status-dot.success {
            background: var(--success-color);
        }

        .status-dot.error {
            background: var(--error-color);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .log-container {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 16px;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 4px 0;
        }

        .log-entry.info {
            color: var(--text-secondary);
        }

        .log-entry.success {
            color: var(--success-color);
        }

        .log-entry.warning {
            color: var(--warning-color);
        }

        .log-entry.error {
            color: var(--error-color);
        }

        .results-section {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin-top: 20px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .result-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .result-type {
            font-weight: 600;
            color: var(--primary-color);
        }

        .result-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .result-files {
            max-height: 120px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-icon {
            color: var(--text-muted);
        }

        .file-name {
            flex: 1;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .download-btn {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }

        .download-btn:hover {
            background: rgba(37, 99, 235, 0.1);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--error-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body data-theme="light">
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-spider"></i> Authenticated Web Scraper</h1>
            <button class="theme-toggle" onclick="toggleTheme()">
                <i class="fas fa-sun"></i> Light
            </button>
        </div>

        <div class="main-content">
            <!-- Quick Actions Header -->
            <div class="card" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)); color: white; border: none;">
                <h2 style="color: white; font-size: 24px; margin-bottom: 20px;"><i class="fas fa-rocket"></i> Advanced Data Scraping Suite</h2>
                <div class="action-buttons">
                    <button class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="scrapeAll()">
                        <i class="fas fa-globe"></i> Scrape All Data
                    </button>
                    <button class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="openChatAssistant()">
                        <i class="fas fa-robot"></i> AI Assistant
                    </button>
                    <button class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="loadPreset()">
                        <i class="fas fa-magic"></i> Load Preset
                    </button>
                </div>
            </div>
        </div>
        
        <div class="scraping-grid">
            <!-- Target Configuration -->
            <div class="card">
                <h2><i class="fas fa-bullseye"></i> Target Configuration</h2>
                
                <div class="form-group">
                    <label for="domain">Target URL/Domain</label>
                    <input type="url" id="domain" class="form-input" placeholder="https://example.com" required>
                </div>

                <div class="form-group">
                    <label for="crawl-mode">Scraping Mode</label>
                    <select id="crawl-mode" class="form-input" onchange="updateCrawlMode()">
                        <option value="single">Single Page</option>
                        <option value="domain" selected>Entire Domain</option>
                        <option value="subdomain">Include Subdomains</option>
                        <option value="custom">Custom Pattern</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="depth">Crawl Depth</label>
                    <input type="range" id="depth" min="1" max="10" value="3" oninput="updateDepthValue(this.value)">
                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: var(--text-muted);">
                        <span>1 Level</span>
                        <span id="depth-value">3 Levels</span>
                        <span>10 Levels</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="url-patterns">URL Patterns (Optional)</label>
                    <textarea id="url-patterns" class="form-input" rows="3" placeholder="/blog/*\n/products/*\n/api/v1/*"></textarea>
                </div>
            </div>

            <!-- File Type Filters -->
            <div class="card">
                <h2><i class="fas fa-filter"></i> File Type Filters</h2>
                
                <div class="form-group">
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button class="btn btn-secondary" onclick="selectAllFileTypes()" style="flex: 1;">
                            <i class="fas fa-check-double"></i> Select All
                        </button>
                        <button class="btn btn-secondary" onclick="clearAllFileTypes()" style="flex: 1;">
                            <i class="fas fa-times"></i> Clear All
                        </button>
                    </div>
                </div>
                
                <div class="checkbox-group">
                    <div class="checkbox-item" onclick="toggleCheckbox('html')">
                        <input type="checkbox" id="html" checked>
                        <i class="fas fa-code icon"></i>
                        <div>
                            <div class="label">Web Pages</div>
                            <div class="description">HTML, PHP, ASP</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('documents')">
                        <input type="checkbox" id="documents">
                        <i class="fas fa-file-alt icon"></i>
                        <div>
                            <div class="label">Documents</div>
                            <div class="description">PDF, DOC, TXT</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('spreadsheets')">
                        <input type="checkbox" id="spreadsheets">
                        <i class="fas fa-file-excel icon"></i>
                        <div>
                            <div class="label">Spreadsheets</div>
                            <div class="description">XLS, CSV, ODS</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('media')">
                        <input type="checkbox" id="media">
                        <i class="fas fa-photo-video icon"></i>
                        <div>
                            <div class="label">Media Files</div>
                            <div class="description">Images, Videos, Audio</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('data')">
                        <input type="checkbox" id="data" checked>
                        <i class="fas fa-database icon"></i>
                        <div>
                            <div class="label">Data Files</div>
                            <div class="description">JSON, XML, API</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('archives')">
                        <input type="checkbox" id="archives">
                        <i class="fas fa-file-archive icon"></i>
                        <div>
                            <div class="label">Archives</div>
                            <div class="description">ZIP, RAR, TAR</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="custom-extensions">Custom Extensions</label>
                    <input type="text" id="custom-extensions" class="form-input" placeholder=".log, .config, .backup">
                </div>
            </div>

            <!-- Data Size & Limits -->
            <div class="card">
                <h2><i class="fas fa-weight-hanging"></i> Data Size & Limits</h2>
                
                <div class="form-group">
                    <label for="max-file-size">Max File Size</label>
                    <select id="max-file-size" class="form-input">
                        <option value="1">1 MB</option>
                        <option value="5">5 MB</option>
                        <option value="10" selected>10 MB</option>
                        <option value="50">50 MB</option>
                        <option value="100">100 MB</option>
                        <option value="-1">No Limit</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="max-total-size">Max Total Download</label>
                    <select id="max-total-size" class="form-input">
                        <option value="100">100 MB</option>
                        <option value="500">500 MB</option>
                        <option value="1000" selected>1 GB</option>
                        <option value="5000">5 GB</option>
                        <option value="-1">No Limit</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="max-pages">Max Pages to Scrape</label>
                    <input type="number" id="max-pages" class="form-input" value="1000" min="1" max="100000">
                </div>

                <div class="form-group">
                    <label for="request-delay">Request Delay (ms)</label>
                    <input type="range" id="request-delay" min="0" max="5000" value="1000" oninput="updateDelayValue(this.value)">
                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: var(--text-muted);">
                        <span>0ms (Fast)</span>
                        <span id="delay-value">1000ms</span>
                        <span>5000ms (Slow)</span>
                    </div>
                </div>

                <div class="checkbox-group">
                    <div class="checkbox-item" onclick="toggleCheckbox('respect-robots')">
                        <input type="checkbox" id="respect-robots" checked>
                        <i class="fas fa-robot icon"></i>
                        <div>
                            <div class="label">Respect robots.txt</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('follow-redirects')">
                        <input type="checkbox" id="follow-redirects" checked>
                        <i class="fas fa-route icon"></i>
                        <div>
                            <div class="label">Follow Redirects</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credentials & Authentication -->
            <div class="card">
                <h2><i class="fas fa-shield-alt"></i> Authentication & Credentials</h2>
                
                <div class="form-group">
                    <label for="auth-type">Authentication Type</label>
                    <select id="auth-type" class="form-input" onchange="updateAuthFields()">
                        <option value="none">No Authentication</option>
                        <option value="basic">Basic Auth</option>
                        <option value="form">Form Login</option>
                        <option value="oauth">OAuth 2.0</option>
                        <option value="api-key">API Key</option>
                        <option value="session">Session Cookies</option>
                    </select>
                </div>

                <div id="auth-fields" style="display: none;">
                    <div class="form-group">
                        <label for="username">Username/Email</label>
                        <input type="text" id="username" class="form-input" placeholder="Enter username or email">
                    </div>

                    <div class="form-group">
                        <label for="password">Password/Token</label>
                        <div style="position: relative;">
                            <input type="password" id="password" class="form-input" placeholder="Enter password or token">
                            <button type="button" onclick="togglePasswordVisibility()" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-secondary); cursor: pointer;">
                                <i class="fas fa-eye" id="password-toggle"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group" id="login-url-group" style="display: none;">
                        <label for="login-url">Login URL</label>
                        <input type="url" id="login-url" class="form-input" placeholder="https://example.com/login">
                    </div>

                    <div class="form-group" id="api-key-group" style="display: none;">
                        <label for="api-key-header">API Key Header</label>
                        <input type="text" id="api-key-header" class="form-input" placeholder="X-API-Key, Authorization">
                    </div>
                </div>

                <div class="checkbox-group">
                    <div class="checkbox-item" onclick="toggleCheckbox('save-credentials')">
                        <input type="checkbox" id="save-credentials">
                        <i class="fas fa-save icon"></i>
                        <div>
                            <div class="label">Save Credentials</div>
                            <div class="description">Securely store for reuse</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('use-proxy')">
                        <input type="checkbox" id="use-proxy">
                        <i class="fas fa-mask icon"></i>
                        <div>
                            <div class="label">Use Proxy</div>
                            <div class="description">Route through proxy server</div>
                        </div>
                    </div>
                </div>

                <div class="form-group" id="proxy-settings" style="display: none;">
                    <label for="proxy-url">Proxy URL</label>
                    <input type="text" id="proxy-url" class="form-input" placeholder="http://proxy:port">
                </div>
            </div>

            <!-- Chat & AI Options -->
            <div class="card">
                <h2><i class="fas fa-comments"></i> AI Assistant & Chat Options</h2>
                
                <div class="checkbox-group">
                    <div class="checkbox-item" onclick="toggleCheckbox('ai-analysis')">
                        <input type="checkbox" id="ai-analysis" checked>
                        <i class="fas fa-brain icon"></i>
                        <div>
                            <div class="label">AI Content Analysis</div>
                            <div class="description">Analyze scraped content with AI</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('auto-categorize')">
                        <input type="checkbox" id="auto-categorize">
                        <i class="fas fa-tags icon"></i>
                        <div>
                            <div class="label">Auto Categorization</div>
                            <div class="description">Automatically categorize content</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('sentiment-analysis')">
                        <input type="checkbox" id="sentiment-analysis">
                        <i class="fas fa-smile icon"></i>
                        <div>
                            <div class="label">Sentiment Analysis</div>
                            <div class="description">Analyze content sentiment</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('real-time-chat')">
                        <input type="checkbox" id="real-time-chat" checked>
                        <i class="fas fa-comment-dots icon"></i>
                        <div>
                            <div class="label">Real-time Chat</div>
                            <div class="description">Chat during scraping process</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="ai-prompt">Custom AI Prompt</label>
                    <textarea id="ai-prompt" class="form-input" rows="3" placeholder="Analyze this content for key insights and summarize the main points..."></textarea>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="openChatWindow()">
                        <i class="fas fa-comment"></i> Open Chat
                    </button>
                    <button class="btn btn-secondary" onclick="configureAI()">
                        <i class="fas fa-cog"></i> AI Settings
                    </button>
                </div>
            </div>

            <!-- Advanced Options -->
            <div class="card">
                <h2><i class="fas fa-cogs"></i> Advanced Options</h2>
                
                <div class="form-group">
                    <label for="user-agent">User Agent</label>
                    <select id="user-agent" class="form-input">
                        <option value="chrome">Chrome (Latest)</option>
                        <option value="firefox">Firefox (Latest)</option>
                        <option value="safari">Safari (Latest)</option>
                        <option value="mobile">Mobile Browser</option>
                        <option value="bot">Search Bot</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="output-format">Output Format</label>
                    <select id="output-format" class="form-input">
                        <option value="json">JSON</option>
                        <option value="csv">CSV</option>
                        <option value="xml">XML</option>
                        <option value="html">HTML Report</option>
                        <option value="markdown">Markdown</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <div class="checkbox-item" onclick="toggleCheckbox('extract-emails')">
                        <input type="checkbox" id="extract-emails">
                        <i class="fas fa-envelope icon"></i>
                        <div>
                            <div class="label">Extract Emails</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('extract-phones')">
                        <input type="checkbox" id="extract-phones">
                        <i class="fas fa-phone icon"></i>
                        <div>
                            <div class="label">Extract Phone Numbers</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('extract-links')">
                        <input type="checkbox" id="extract-links" checked>
                        <i class="fas fa-link icon"></i>
                        <div>
                            <div class="label">Extract All Links</div>
                        </div>
                    </div>

                    <div class="checkbox-item" onclick="toggleCheckbox('screenshot')">
                        <input type="checkbox" id="screenshot">
                        <i class="fas fa-camera icon"></i>
                        <div>
                            <div class="label">Take Screenshots</div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="startAdvancedScraping()">
                        <i class="fas fa-rocket"></i> Start Advanced Scraping
                    </button>
                    <button class="btn btn-secondary" onclick="savePreset()">
                        <i class="fas fa-save"></i> Save Preset
                    </button>
                    <button class="btn btn-secondary" onclick="resetToDefaults()">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
            <h2><i class="fas fa-chart-line"></i> Scraping Progress</h2>
            
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Ready to start scraping</span>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div style="display: flex; justify-content: space-between; font-size: 12px; color: var(--text-muted);">
                <span id="progressText">0% Complete</span>
                <span id="timeElapsed">00:00</span>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-entry info">[INFO] Scraper initialized and ready</div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="resultsSection" style="display: none;">
            <h2><i class="fas fa-download"></i> Scraped Results</h2>
            
            <div class="results-grid" id="resultsGrid">
                <!-- Results will be populated here -->
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="downloadAll()">
                    <i class="fas fa-download"></i> Download All
                </button>
                <button class="btn btn-secondary" onclick="exportResults()">
                    <i class="fas fa-file-export"></i> Export Report
                </button>
            </div>
        </div>
    </div>

    <script>
        class WebScraper {
            constructor() {
                this.isRunning = false;
                this.startTime = null;
                this.results = {
                    html: [],
                    pdf: [],
                    word: [],
                    excel: [],
                    json: [],
                    csv: [],
                    rawdata: [],
                    images: []
                };
                this.timer = null;
                this.progress = 0;
            }

            async startScraping() {
                if (this.isRunning) return;

                const domain = document.getElementById('domain').value;
                if (!domain) {
                    this.showNotification('Please enter a target domain', 'error');
                    return;
                }

                const selectedTypes = this.getSelectedDataTypes();
                if (selectedTypes.length === 0) {
                    this.showNotification('Please select at least one data type', 'error');
                    return;
                }

                this.isRunning = true;
                this.startTime = Date.now();
                this.progress = 0;
                
                // Update UI
                document.getElementById('stopBtn').disabled = false;
                document.querySelector('.btn-primary').disabled = true;
                document.getElementById('statusDot').className = 'status-dot active';
                document.getElementById('statusText').textContent = 'Scraping in progress...';
                
                this.addLog('Starting authenticated scraping session', 'info');
                this.addLog(`Target: ${domain}`, 'info');
                this.addLog(`Data types: ${selectedTypes.join(', ')}`, 'info');
                
                // Start timer
                this.startTimer();
                
                // Simulate scraping process
                await this.simulateScraping(domain, selectedTypes);
            }

            async simulateScraping(domain, types) {
                const steps = [
                    'Connecting to target domain...',
                    'Authenticating with provided credentials...',
                    'Analyzing site structure...',
                    'Discovering available content...',
                    'Filtering by selected data types...',
                    'Extracting content...',
                    'Organizing results...',
                    'Finalizing download packages...'
                ];

                for (let i = 0; i < steps.length; i++) {
                    if (!this.isRunning) break;
                    
                    this.addLog(steps[i], 'info');
                    this.progress = ((i + 1) / steps.length) * 100;
                    this.updateProgress();
                    
                    // Simulate finding files
                    if (i >= 4) {
                        this.simulateFoundContent(types);
                    }
                    
                    await this.delay(1000 + Math.random() * 2000);
                }

                if (this.isRunning) {
                    this.completeScraping();
                }
            }

            simulateFoundContent(types) {
                const fileTypes = {
                    html: ['index.html', 'about.html', 'contact.html', 'products.html'],
                    pdf: ['manual.pdf', 'report.pdf', 'brochure.pdf'],
                    word: ['document.docx', 'proposal.doc', 'terms.docx'],
                    excel: ['data.xlsx', 'inventory.xls', 'sales.xlsx'],
                    json: ['api-data.json', 'config.json', 'metadata.json'],
                    csv: ['users.csv', 'transactions.csv', 'products.csv'],
                    rawdata: ['Raw text content', 'Table data', 'Code snippets'],
                    images: ['logo.png', 'banner.jpg', 'icon.svg']
                };

                types.forEach(type => {
                    if (Math.random() > 0.3) { // 70% chance to find content
                        const files = fileTypes[type] || [];
                        const foundFiles = files.slice(0, Math.floor(Math.random() * files.length) + 1);
                        this.results[type].push(...foundFiles);
                        
                        if (foundFiles.length > 0) {
                            this.addLog(`Found ${foundFiles.length} ${type.toUpperCase()} file(s)`, 'success');
                        }
                    }
                });
            }

            completeScraping() {
                this.isRunning = false;
                this.stopTimer();
                
                document.getElementById('stopBtn').disabled = true;
                document.querySelector('.btn-primary').disabled = false;
                document.getElementById('statusDot').className = 'status-dot success';
                document.getElementById('statusText').textContent = 'Scraping completed successfully';
                
                this.addLog('Scraping session completed', 'success');
                this.addLog('Results organized by file type', 'info');
                
                this.showNotification('Scraping completed successfully!', 'success');
                this.displayResults();
            }

            stopScraping() {
                if (!this.isRunning) return;
                
                this.isRunning = false;
                this.stopTimer();
                
                document.getElementById('stopBtn').disabled = true;
                document.querySelector('.btn-primary').disabled = false;
                document.getElementById('statusDot').className = 'status-dot error';
                document.getElementById('statusText').textContent = 'Scraping stopped by user';
                
                this.addLog('Scraping session stopped by user', 'warning');
                this.showNotification('Scraping stopped', 'warning');
            }

            getSelectedDataTypes() {
                const types = ['html', 'pdf', 'word', 'excel', 'json', 'csv', 'rawdata', 'images'];
                return types.filter(type => document.getElementById(type).checked);
            }

            updateProgress() {
                document.getElementById('progressFill').style.width = this.progress + '%';
                document.getElementById('progressText').textContent = Math.round(this.progress) + '% Complete';
            }

            startTimer() {
                this.timer = setInterval(() => {
                    if (this.startTime) {
                        const elapsed = Date.now() - this.startTime;
                        const minutes = Math.floor(elapsed / 60000);
                        const seconds = Math.floor((elapsed % 60000) / 1000);
                        document.getElementById('timeElapsed').textContent = 
                            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    }
                }, 1000);
            }

            stopTimer() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }

            addLog(message, type = 'info') {
                const container = document.getElementById('logContainer');
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] [${type.toUpperCase()}] ${message}`;
                
                container.appendChild(logEntry);
                container.scrollTop = container.scrollHeight;
            }

            displayResults() {
                const resultsSection = document.getElementById('resultsSection');
                const resultsGrid = document.getElementById('resultsGrid');
                
                resultsGrid.innerHTML = '';
                
                Object.keys(this.results).forEach(type => {
                    const files = this.results[type];
                    if (files.length > 0) {
                        const resultCard = this.createResultCard(type, files);
                        resultsGrid.appendChild(resultCard);
                    }
                });
                
                resultsSection.style.display = 'block';
            }

            createResultCard(type, files) {
                const card = document.createElement('div');
                card.className = 'result-card';
                
                const icons = {
                    html: 'fa-code',
                    pdf: 'fa-file-pdf',
                    word: 'fa-file-word',
                    excel: 'fa-file-excel',
                    json: 'fa-file-code',
                    csv: 'fa-file-csv',
                    rawdata: 'fa-database',
                    images: 'fa-image'
                };
                
                card.innerHTML = `
                    <div class="result-header">
                        <div class="result-type">
                            <i class="fas ${icons[type]}"></i> ${type.toUpperCase()}
                        </div>
                        <div class="result-count">${files.length}</div>
                    </div>
                    <div class="result-files">
                        ${files.map(file => `
                            <div class="file-item">
                                <i class="fas fa-file file-icon"></i>
                                <span class="file-name">${file}</span>
                                <button class="download-btn" onclick="downloadFile('${type}', '${file}')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
                
                return card;
            }

            clearResults() {
                this.results = {
                    html: [],
                    pdf: [],
                    word: [],
                    excel: [],
                    json: [],
                    csv: [],
                    rawdata: [],
                    images: []
                };
                
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('logContainer').innerHTML = '<div class="log-entry info">[INFO] Scraper initialized and ready</div>';
                document.getElementById('progressFill').style.width = '0%';
                document.getElementById('progressText').textContent = '0% Complete';
                document.getElementById('timeElapsed').textContent = '00:00';
                document.getElementById('statusDot').className = 'status-dot';
                document.getElementById('statusText').textContent = 'Ready to start scraping';
                
                this.showNotification('Results cleared', 'success');
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer; font-size: 16px;">&times;</button>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Initialize scraper
        const scraper = new WebScraper();

        // Global functions
        function toggleCheckbox(id) {
            const checkbox = document.getElementById(id);
            const item = checkbox.closest('.checkbox-item');
            
            checkbox.checked = !checkbox.checked;
            item.classList.toggle('selected', checkbox.checked);
        }

        function startScraping() {
            scraper.startScraping();
        }

        function stopScraping() {
            scraper.stopScraping();
        }

        function clearResults() {
            scraper.clearResults();
        }

        function downloadFile(type, filename) {
            // Simulate file download
            const content = `Simulated content for ${filename}`;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
            
            scraper.showNotification(`Downloaded ${filename}`, 'success');
        }

        function downloadAll() {
            const allFiles = Object.values(scraper.results).flat();
            if (allFiles.length === 0) {
                scraper.showNotification('No files to download', 'warning');
                return;
            }
            
            // Simulate downloading all files as a ZIP
            const content = `Archive containing ${allFiles.length} files:\n${allFiles.join('\n')}`;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `scraped-content-${new Date().toISOString().split('T')[0]}.zip`;
            a.click();
            URL.revokeObjectURL(url);
            
            scraper.showNotification('All files downloaded as ZIP archive', 'success');
        }

        function exportResults() {
            const report = {
                timestamp: new Date().toISOString(),
                domain: document.getElementById('domain').value,
                results: scraper.results,
                summary: {
                    totalFiles: Object.values(scraper.results).flat().length,
                    fileTypes: Object.keys(scraper.results).filter(type => scraper.results[type].length > 0)
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `scraping-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            scraper.showNotification('Report exported successfully', 'success');
        }

        function toggleTheme() {
            const body = document.body;
            const themeBtn = document.querySelector('.theme-toggle');
            
            if (body.getAttribute('data-theme') === 'dark') {
                body.setAttribute('data-theme', 'light');
                themeBtn.innerHTML = '<i class="fas fa-sun"></i> Light';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeBtn.innerHTML = '<i class="fas fa-moon"></i> Dark';
                localStorage.setItem('theme', 'dark');
            }
        }

        // New enhanced functions for data scraping interface
        function scrapeAll() {
            // Select all file types and start comprehensive scraping
            selectAllFileTypes();
            document.getElementById('max-pages').value = '10000';
            document.getElementById('depth').value = '5';
            updateDepthValue('5');
            scraper.showNotification('Scrape All mode activated - comprehensive scraping will begin', 'info');
            setTimeout(() => startAdvancedScraping(), 1000);
        }

        function openChatAssistant() {
            // Open AI chat assistant in a new window
            const chatWindow = window.open('chat-window.html', 'ChatAssistant', 'width=800,height=600,scrollbars=yes,resizable=yes');
            if (chatWindow) {
                scraper.showNotification('AI Assistant opened in new window', 'success');
            } else {
                scraper.showNotification('Please allow popups to open AI Assistant', 'warning');
            }
        }

        function loadPreset() {
            const presets = {
                'comprehensive': {
                    name: 'Comprehensive Scraping',
                    fileTypes: ['html', 'documents', 'spreadsheets', 'data', 'media'],
                    maxPages: 5000,
                    depth: 4,
                    delay: 1000
                },
                'documents-only': {
                    name: 'Documents Only',
                    fileTypes: ['documents', 'spreadsheets'],
                    maxPages: 1000,
                    depth: 2,
                    delay: 500
                },
                'media-focused': {
                    name: 'Media Focused',
                    fileTypes: ['media', 'html'],
                    maxPages: 2000,
                    depth: 3,
                    delay: 2000
                }
            };
            
            const presetNames = Object.keys(presets);
            const selectedPreset = presetNames[Math.floor(Math.random() * presetNames.length)];
            const preset = presets[selectedPreset];
            
            // Apply preset settings
            clearAllFileTypes();
            preset.fileTypes.forEach(type => {
                const checkbox = document.getElementById(type);
                if (checkbox) {
                    checkbox.checked = true;
                    toggleCheckbox(type);
                }
            });
            
            document.getElementById('max-pages').value = preset.maxPages;
            document.getElementById('depth').value = preset.depth;
            document.getElementById('request-delay').value = preset.delay;
            updateDepthValue(preset.depth);
            updateDelayValue(preset.delay);
            
            scraper.showNotification(`Loaded preset: ${preset.name}`, 'success');
        }

        function updateCrawlMode() {
            const mode = document.getElementById('crawl-mode').value;
            const urlPatternsGroup = document.getElementById('url-patterns').parentElement;
            
            if (mode === 'custom') {
                urlPatternsGroup.style.display = 'block';
                document.getElementById('url-patterns').required = true;
            } else {
                urlPatternsGroup.style.display = 'none';
                document.getElementById('url-patterns').required = false;
            }
        }

        function updateDepthValue(value) {
            document.getElementById('depth-value').textContent = `${value} Level${value > 1 ? 's' : ''}`;
        }

        function updateDelayValue(value) {
            document.getElementById('delay-value').textContent = `${value}ms`;
        }

        function selectAllFileTypes() {
            const fileTypes = ['html', 'documents', 'spreadsheets', 'media', 'data', 'archives'];
            fileTypes.forEach(type => {
                const checkbox = document.getElementById(type);
                if (checkbox && !checkbox.checked) {
                    checkbox.checked = true;
                    checkbox.closest('.checkbox-item').classList.add('selected');
                }
            });
            scraper.showNotification('All file types selected', 'success');
        }

        function clearAllFileTypes() {
            const fileTypes = ['html', 'documents', 'spreadsheets', 'media', 'data', 'archives'];
            fileTypes.forEach(type => {
                const checkbox = document.getElementById(type);
                if (checkbox && checkbox.checked) {
                    checkbox.checked = false;
                    checkbox.closest('.checkbox-item').classList.remove('selected');
                }
            });
            scraper.showNotification('All file types cleared', 'info');
        }

        function updateAuthFields() {
            const authType = document.getElementById('auth-type').value;
            const authFields = document.getElementById('auth-fields');
            const loginUrlGroup = document.getElementById('login-url-group');
            const apiKeyGroup = document.getElementById('api-key-group');
            
            if (authType === 'none') {
                authFields.style.display = 'none';
            } else {
                authFields.style.display = 'block';
                
                // Show/hide specific fields based on auth type
                loginUrlGroup.style.display = authType === 'form' ? 'block' : 'none';
                apiKeyGroup.style.display = authType === 'api-key' ? 'block' : 'none';
                
                // Update field labels based on auth type
                const usernameLabel = document.querySelector('label[for="username"]');
                const passwordLabel = document.querySelector('label[for="password"]');
                
                switch (authType) {
                    case 'basic':
                        usernameLabel.textContent = 'Username';
                        passwordLabel.textContent = 'Password';
                        break;
                    case 'form':
                        usernameLabel.textContent = 'Username/Email';
                        passwordLabel.textContent = 'Password';
                        break;
                    case 'oauth':
                        usernameLabel.textContent = 'Client ID';
                        passwordLabel.textContent = 'Client Secret';
                        break;
                    case 'api-key':
                        usernameLabel.textContent = 'API Key';
                        passwordLabel.textContent = 'API Secret (Optional)';
                        break;
                    case 'session':
                        usernameLabel.textContent = 'Session Token';
                        passwordLabel.textContent = 'Additional Token';
                        break;
                }
            }
        }

        function togglePasswordVisibility() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        function openChatWindow() {
            const chatWindow = window.open('chat-window.html', 'ScrapingChat', 'width=900,height=700,scrollbars=yes,resizable=yes');
            if (chatWindow) {
                scraper.showNotification('Chat window opened', 'success');
            } else {
                scraper.showNotification('Please allow popups to open chat window', 'warning');
            }
        }

        function configureAI() {
            const aiSettings = {
                analysis: document.getElementById('ai-analysis').checked,
                categorization: document.getElementById('auto-categorize').checked,
                sentiment: document.getElementById('sentiment-analysis').checked,
                realTimeChat: document.getElementById('real-time-chat').checked,
                customPrompt: document.getElementById('ai-prompt').value
            };
            
            localStorage.setItem('aiSettings', JSON.stringify(aiSettings));
            scraper.showNotification('AI settings saved', 'success');
        }

        function startAdvancedScraping() {
            // Validate required fields
            const domain = document.getElementById('domain').value;
            if (!domain) {
                scraper.showNotification('Please enter a target URL/Domain', 'error');
                return;
            }
            
            // Get selected file types
            const selectedTypes = ['html', 'documents', 'spreadsheets', 'media', 'data', 'archives']
                .filter(type => document.getElementById(type).checked);
            
            if (selectedTypes.length === 0) {
                scraper.showNotification('Please select at least one file type', 'error');
                return;
            }
            
            // Get configuration
            const config = {
                domain: domain,
                crawlMode: document.getElementById('crawl-mode').value,
                depth: document.getElementById('depth').value,
                maxPages: document.getElementById('max-pages').value,
                maxFileSize: document.getElementById('max-file-size').value,
                maxTotalSize: document.getElementById('max-total-size').value,
                requestDelay: document.getElementById('request-delay').value,
                authType: document.getElementById('auth-type').value,
                fileTypes: selectedTypes,
                respectRobots: document.getElementById('respect-robots').checked,
                followRedirects: document.getElementById('follow-redirects').checked,
                extractEmails: document.getElementById('extract-emails').checked,
                extractPhones: document.getElementById('extract-phones').checked,
                extractLinks: document.getElementById('extract-links').checked,
                takeScreenshots: document.getElementById('screenshot').checked
            };
            
            scraper.showNotification('Starting advanced scraping with custom configuration...', 'info');
            console.log('Scraping configuration:', config);
            
            // Start the enhanced scraping process
            scraper.startScraping();
        }

        function savePreset() {
            const presetName = prompt('Enter a name for this preset:');
            if (!presetName) return;
            
            const preset = {
                name: presetName,
                domain: document.getElementById('domain').value,
                crawlMode: document.getElementById('crawl-mode').value,
                depth: document.getElementById('depth').value,
                maxPages: document.getElementById('max-pages').value,
                fileTypes: ['html', 'documents', 'spreadsheets', 'media', 'data', 'archives']
                    .filter(type => document.getElementById(type).checked),
                authType: document.getElementById('auth-type').value,
                aiSettings: {
                    analysis: document.getElementById('ai-analysis').checked,
                    categorization: document.getElementById('auto-categorize').checked,
                    sentiment: document.getElementById('sentiment-analysis').checked
                }
            };
            
            const savedPresets = JSON.parse(localStorage.getItem('scrapingPresets') || '{}');
            savedPresets[presetName] = preset;
            localStorage.setItem('scrapingPresets', JSON.stringify(savedPresets));
            
            scraper.showNotification(`Preset '${presetName}' saved successfully`, 'success');
        }

        function resetToDefaults() {
            // Reset all form fields to default values
            document.getElementById('domain').value = '';
            document.getElementById('crawl-mode').value = 'domain';
            document.getElementById('depth').value = '3';
            document.getElementById('max-pages').value = '1000';
            document.getElementById('max-file-size').value = '10';
            document.getElementById('max-total-size').value = '1000';
            document.getElementById('request-delay').value = '1000';
            document.getElementById('auth-type').value = 'none';
            document.getElementById('custom-extensions').value = '';
            document.getElementById('ai-prompt').value = '';
            
            // Reset checkboxes
            clearAllFileTypes();
            document.getElementById('html').checked = true;
            document.getElementById('data').checked = true;
            document.getElementById('respect-robots').checked = true;
            document.getElementById('follow-redirects').checked = true;
            document.getElementById('extract-links').checked = true;
            document.getElementById('ai-analysis').checked = true;
            document.getElementById('real-time-chat').checked = true;
            
            // Update UI
            updateDepthValue('3');
            updateDelayValue('1000');
            updateAuthFields();
            
            scraper.showNotification('All settings reset to defaults', 'info');
        }

        // Enhanced checkbox toggle with proxy settings
        document.addEventListener('change', function(e) {
            if (e.target.id === 'use-proxy') {
                const proxySettings = document.getElementById('proxy-settings');
                proxySettings.style.display = e.target.checked ? 'block' : 'none';
            }
        });

        // Load saved theme
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.body.setAttribute('data-theme', savedTheme);
                const themeBtn = document.querySelector('.theme-toggle');
                themeBtn.innerHTML = savedTheme === 'dark' ? 
                    '<i class="fas fa-moon"></i> Dark' : 
                    '<i class="fas fa-sun"></i> Light';
            }
            
            // Load saved AI settings
            const savedAISettings = localStorage.getItem('aiSettings');
            if (savedAISettings) {
                const settings = JSON.parse(savedAISettings);
                document.getElementById('ai-analysis').checked = settings.analysis;
                document.getElementById('auto-categorize').checked = settings.categorization;
                document.getElementById('sentiment-analysis').checked = settings.sentiment;
                document.getElementById('real-time-chat').checked = settings.realTimeChat;
                document.getElementById('ai-prompt').value = settings.customPrompt || '';
            }
        });
    </script>
</body>
</html>