"""Authentication routes for HexStrike AI
Handles login, logout, token refresh, and user management
Secured with input validation and SQL injection protection
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import (
    create_access_token, create_refresh_token, jwt_required,
    get_jwt_identity, get_jwt, verify_jwt_in_request
)
from werkzeug.security import check_password_hash
from datetime import timedelta
import logging
from models import User, AuditLog, db
from security_middleware import audit_action, require_roles
from input_validation import (
    validate_json_input, InputValidator, SQLSafeQuery
)

# Create blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')
logger = logging.getLogger(__name__)

# JWT token blacklist (in production, use Redis)
blacklisted_tokens = set()

@auth_bp.route('/login', methods=['POST'])
@validate_json_input(required_fields=['username', 'password'])
def login():
    """User login endpoint with SQL injection protection"""
    try:
        data = request.validated_json
        
        username = data['username'].strip()
        password = data['password']
        
        # Validate username format
        is_valid, message = InputValidator.validate_username(username)
        if not is_valid:
            audit_action(
                action='login_failed',
                resource_type='auth',
                resource_id=username,
                details={'reason': 'invalid_username_format'}
            )
            return jsonify({'error': 'Invalid username format'}), 400
        
        # Safely find user in database using parameterized query
        user = SQLSafeQuery.safe_user_lookup(username)
        
        if not user or not check_password_hash(user.password_hash, password):
            # Log failed login attempt
            audit_action(
                action='login_failed',
                resource_type='auth',
                resource_id=username,
                details={'reason': 'invalid_credentials'}
            )
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Create JWT tokens
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'username': user.username,
                'role': user.role,
                'permissions': user.permissions
            },
            expires_delta=timedelta(minutes=30)  # 30 minute expiry
        )
        
        refresh_token = create_refresh_token(
            identity=user.id,
            expires_delta=timedelta(days=7)  # 7 day expiry
        )
        
        # Update user last login
        user.update_last_login()
        db.session.commit()
        
        # Log successful login
        audit_action(
            action='login_success',
            resource_type='auth',
            resource_id=str(user.id),
            details={'username': user.username, 'role': user.role}
        )
        
        logger.info(f"User {user.username} logged in successfully")
        
        return jsonify({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'permissions': user.permissions
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """User logout endpoint - blacklist JWT token"""
    try:
        # Get JWT token identifier
        jti = get_jwt()['jti']
        user_id = get_jwt_identity()
        
        # Add token to blacklist
        blacklisted_tokens.add(jti)
        
        # Log logout action
        audit_action(
            action='logout',
            resource_type='auth',
            resource_id=str(user_id),
            details={'token_jti': jti}
        )
        
        logger.info(f"User {user_id} logged out successfully")
        
        return jsonify({'message': 'Successfully logged out'}), 200
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """Refresh access token using refresh token with secure database access"""
    try:
        current_user_id = get_jwt_identity()
        
        # Validate user ID format
        is_valid, user_id = InputValidator.validate_integer(current_user_id, min_val=1)
        if not is_valid:
            return jsonify({'error': 'Invalid user ID'}), 400
        
        # Get user from database using safe query
        user = User.query.filter(
            User.id == user_id,
            User.is_active == True
        ).first()
        
        if not user:
            return jsonify({'error': 'User not found or inactive'}), 404
        
        # Create new access token
        new_access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'username': user.username,
                'role': user.role,
                'permissions': user.permissions
            },
            expires_delta=timedelta(minutes=30)
        )
        
        # Log token refresh
        audit_action(
            action='token_refresh',
            resource_type='auth',
            resource_id=str(user.id),
            details={'username': user.username}
        )
        
        return jsonify({'access_token': new_access_token}), 200
        
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/verify', methods=['GET'])
@jwt_required()
def verify_token():
    """Verify JWT token validity"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        
        # Get user from database
        # Get user from database using safe query
        user = User.query.filter(
            User.id == current_user_id,
            User.is_active == True
        ).first()
        if not user:
            return jsonify({'error': 'User not found or inactive'}), 404
        
        return jsonify({
            'valid': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'permissions': user.permissions
            },
            'token_claims': {
                'exp': claims.get('exp'),
                'iat': claims.get('iat'),
                'jti': claims.get('jti')
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Token verification error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get current user profile"""
    try:
        current_user_id = get_jwt_identity()
        
        # Get user from database using safe query
        user = User.query.filter(
            User.id == current_user_id,
            User.is_active == True
        ).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'permissions': user.permissions,
                'created_at': user.created_at.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Profile retrieval error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """Change user password"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('current_password') or not data.get('new_password'):
            return jsonify({'error': 'Current and new password required'}), 400
        
        # Get user from database using safe query
        user = User.query.filter(
            User.id == current_user_id,
            User.is_active == True
        ).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Verify current password
        if not check_password_hash(user.password_hash, data['current_password']):
            audit_action(
                action='password_change_failed',
                resource_type='auth',
                resource_id=str(user.id),
                details={'reason': 'invalid_current_password'}
            )
            return jsonify({'error': 'Current password is incorrect'}), 401
        
        # Update password
        user.set_password(data['new_password'])
        db.session.commit()
        
        # Log password change
        audit_action(
            action='password_changed',
            resource_type='auth',
            resource_id=str(user.id),
            details={'username': user.username}
        )
        
        logger.info(f"Password changed for user {user.username}")
        
        return jsonify({'message': 'Password changed successfully'}), 200
        
    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# Token blacklist checker
def is_token_blacklisted(jwt_header, jwt_payload):
    """Check if JWT token is blacklisted"""
    jti = jwt_payload['jti']
    return jti in blacklisted_tokens

# Register blacklist checker with JWT manager
def register_jwt_callbacks(jwt_manager):
    """Register JWT callbacks with the JWT manager"""
    
    @jwt_manager.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload):
        return is_token_blacklisted(jwt_header, jwt_payload)
    
    @jwt_manager.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({'error': 'Token has expired'}), 401
    
    @jwt_manager.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({'error': 'Invalid token'}), 401
    
    @jwt_manager.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({'error': 'Authorization token required'}), 401
    
    @jwt_manager.revoked_token_loader
    def revoked_token_callback(jwt_header, jwt_payload):
        return jsonify({'error': 'Token has been revoked'}), 401