# HexStrike AI - Security Tools Installation Script
# This script installs all missing security tools for the HexStrike AI platform

Write-Host "=== HexStrike AI Security Tools Installation ==="
Write-Host "Installing 126 missing security tools..."

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Check for admin privileges
if (-not (Test-Administrator)) {
    Write-Error "This script requires administrator privileges. Please run as administrator."
    exit 1
}

# Install package managers if not present
Write-Host "\n=== Installing Package Managers ==="

# Install Chocolatey
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Chocolatey..."
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
}

# Install Scoop
if (-not (Get-Command scoop -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Scoop..."
    Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
    iwr -useb get.scoop.sh | iex
    scoop bucket add extras
    scoop bucket add main
}

# Install Python and pip if not present
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Python..."
    choco install python -y
    refreshenv
}

# Install Node.js and npm if not present
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Node.js..."
    choco install nodejs -y
    refreshenv
}

# Install Go if not present
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Go..."
    choco install golang -y
    refreshenv
}

# Install Rust if not present
if (-not (Get-Command cargo -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Rust..."
    choco install rust -y
    refreshenv
}

Write-Host "\n=== Installing Security Tools ==="

# Network Security Tools
Write-Host "Installing Network Security Tools..."
choco install nmap -y
choco install wireshark -y
choco install tcpdump -y
scoop install rustscan
pip install wafw00f
pip install theharvester

# Web Application Security
Write-Host "Installing Web Application Security Tools..."
pip install sqlmap
pip install nikto
pip install wpscan
choco install burp-suite-free-edition -y
npm install -g nuclei
go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest
go install github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
go install github.com/tomnomnom/waybackurls@latest
go install github.com/lc/gau@latest
go install github.com/tomnomnom/qsreplace@latest
go install github.com/s0md3v/uro@latest
go install github.com/devanshbatham/paramspider@latest

# Penetration Testing Frameworks
Write-Host "Installing Penetration Testing Frameworks..."
choco install metasploit -y
pip install impacket
pip install crackmapexec
pip install responder
pip install bloodhound

# Vulnerability Scanners
Write-Host "Installing Vulnerability Scanners..."
go install github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest
pip install prowler
choco install trivy -y
go install github.com/aquasecurity/trivy@latest

# Forensics Tools
Write-Host "Installing Forensics Tools..."
choco install volatility -y
pip install volatility3
choco install autopsy -y
choco install sleuthkit -y
choco install testdisk -y
choco install photorec -y
choco install steghide -y
choco install binwalk -y
choco install strings -y
choco install xxd -y

# Reverse Engineering
Write-Host "Installing Reverse Engineering Tools..."
choco install ghidra -y
choco install ida-free -y
pip install radare2
choco install x64dbg -y
choco install ollydbg -y
pip install angr
pip install pwntools
pip install ropgadget
pip install ropper

# OSINT Tools
Write-Host "Installing OSINT Tools..."
pip install sherlock-project
pip install social-analyzer
pip install spiderfoot
pip install shodan
go install github.com/sherlock-project/sherlock@latest

# Password Cracking
Write-Host "Installing Password Cracking Tools..."
choco install hashcat -y
choco install john-the-ripper -y
choco install hydra -y
pip install patator
choco install ophcrack -y
choco install medusa -y

# Wireless Security
Write-Host "Installing Wireless Security Tools..."
choco install aircrack-ng -y
choco install kismet -y
choco install wifite -y

# Steganography
Write-Host "Installing Steganography Tools..."
pip install stegcracker
pip install zsteg
choco install steghide -y
pip install outguess

# Binary Analysis
Write-Host "Installing Binary Analysis Tools..."
choco install binutils -y
choco install objdump -y
pip install checksec
pip install one-gadget

# Cloud Security
Write-Host "Installing Cloud Security Tools..."
pip install scout-suite
pip install cloudsplaining
pip install terrascan

# Additional Tools
Write-Host "Installing Additional Tools..."
pip install dirsearch
pip install gobuster
go install github.com/OJ/gobuster/v3@latest
pip install ffuf
go install github.com/ffuf/ffuf@latest
pip install wfuzz
pip install dirb
pip install enum4linux
pip install smbmap
pip install nbtscan
pip install rpcclient
pip install searchsploit
pip install msfconsole
pip install msfvenom
pip install zaproxy
pip install postman
pip install scalpel
pip install vol
pip install tshark
pip install x8
pip install xsser
pip install pwninit

# Refresh environment variables
refreshenv

Write-Host "\n=== Installation Complete ==="
Write-Host "All security tools have been installed successfully!"
Write-Host "Please restart your terminal or run 'refreshenv' to update PATH variables."
Write-Host "\nTo verify installation, restart the HexStrike AI server and check /health endpoint."

# Create verification script
$verificationScript = @'
# Verification script to check installed tools
Write-Host "Verifying installed security tools..."

$tools = @(
    "nmap", "sqlmap", "nikto", "nuclei", "subfinder", "waybackurls",
    "hydra", "hashcat", "john", "metasploit", "wireshark", "burpsuite",
    "gobuster", "ffuf", "dirsearch", "sherlock", "theharvester"
)

foreach ($tool in $tools) {
    if (Get-Command $tool -ErrorAction SilentlyContinue) {
        Write-Host "✓ $tool - Installed" -ForegroundColor Green
    } else {
        Write-Host "✗ $tool - Not found" -ForegroundColor Red
    }
}
'@

$verificationScript | Out-File -FilePath "verify_tools.ps1" -Encoding UTF8

Write-Host "\nVerification script created: verify_tools.ps1"
Write-Host "Run this script to check which tools are properly installed."