-- HexStrike AI Database Initialization Script
-- This script sets up the initial database schema and security settings

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create application schema
CREATE SCHEMA IF NOT EXISTS hexstrike;

-- Set default search path
ALTER DATABASE hexstrike_ai SET search_path TO hexstrike, public;

-- Create roles for different access levels
DO $$
BEGIN
    -- Read-only role for reporting
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'hexstrike_readonly') THEN
        CREATE ROLE hexstrike_readonly;
    END IF;
    
    -- Application role with limited privileges
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'hexstrike_app') THEN
        CREATE ROLE hexstrike_app;
    END IF;
END
$$;

-- Grant schema usage
GRANT USAGE ON SCHEMA hexstrike TO hexstrike_readonly;
GRANT USAGE ON SCHEMA hexstrike TO hexstrike_app;
GRANT CREATE ON SCHEMA hexstrike TO hexstrike_app;

-- Grant the application role to the main user
GRANT hexstrike_app TO hexstrike;

-- Create audit log table for security events
CREATE TABLE IF NOT EXISTS hexstrike.audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    user_id UUID,
    ip_address INET,
    user_agent TEXT,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    severity VARCHAR(20) DEFAULT 'INFO'
);

-- Create index for audit log queries
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON hexstrike.audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_log_event_type ON hexstrike.audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON hexstrike.audit_log(user_id);

-- Create session management table
CREATE TABLE IF NOT EXISTS hexstrike.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for session management
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON hexstrike.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON hexstrike.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON hexstrike.user_sessions(expires_at);

-- Create rate limiting table
CREATE TABLE IF NOT EXISTS hexstrike.rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier VARCHAR(255) NOT NULL, -- IP address or user ID
    endpoint VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(identifier, endpoint, window_start)
);

-- Create index for rate limiting
CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier_endpoint ON hexstrike.rate_limits(identifier, endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window_start ON hexstrike.rate_limits(window_start);

-- Create function to clean up expired sessions
CREATE OR REPLACE FUNCTION hexstrike.cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM hexstrike.user_sessions 
    WHERE expires_at < NOW() OR last_activity < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    INSERT INTO hexstrike.audit_log (event_type, event_data, severity)
    VALUES ('session_cleanup', jsonb_build_object('deleted_sessions', deleted_count), 'INFO');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old rate limit records
CREATE OR REPLACE FUNCTION hexstrike.cleanup_old_rate_limits()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM hexstrike.rate_limits 
    WHERE window_start < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to log security events
CREATE OR REPLACE FUNCTION hexstrike.log_security_event(
    p_event_type VARCHAR(50),
    p_user_id UUID DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_event_data JSONB DEFAULT NULL,
    p_severity VARCHAR(20) DEFAULT 'INFO'
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO hexstrike.audit_log (
        event_type, user_id, ip_address, user_agent, event_data, severity
    ) VALUES (
        p_event_type, p_user_id, p_ip_address, p_user_agent, p_event_data, p_severity
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions to application role
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA hexstrike TO hexstrike_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA hexstrike TO hexstrike_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA hexstrike TO hexstrike_app;

-- Grant read-only permissions
GRANT SELECT ON ALL TABLES IN SCHEMA hexstrike TO hexstrike_readonly;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA hexstrike GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO hexstrike_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA hexstrike GRANT USAGE, SELECT ON SEQUENCES TO hexstrike_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA hexstrike GRANT EXECUTE ON FUNCTIONS TO hexstrike_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA hexstrike GRANT SELECT ON TABLES TO hexstrike_readonly;

-- Create initial security event
SELECT hexstrike.log_security_event(
    'database_initialized',
    NULL,
    NULL,
    'PostgreSQL Init Script',
    jsonb_build_object(
        'version', '1.0',
        'timestamp', NOW(),
        'schema_created', true
    ),
    'INFO'
);

-- Display initialization summary
DO $$
BEGIN
    RAISE NOTICE 'HexStrike AI Database Initialization Complete';
    RAISE NOTICE 'Schema: hexstrike';
    RAISE NOTICE 'Roles: hexstrike_app, hexstrike_readonly';
    RAISE NOTICE 'Security features: audit_log, session management, rate limiting';
END
$$;