# Local Development Requirements (simplified)
# Core Flask dependencies
flask==3.0.0
flask-cors==4.0.0
flask-jwt-extended==4.6.0
flask-limiter==3.5.0
flask-sqlalchemy==3.1.1
flask-migrate==4.0.5
requests==2.31.0
psutil==5.9.6
fastmcp==0.2.0
cryptography==41.0.7
gunicorn==21.2.0

# Database (SQLite instead of PostgreSQL)
sqlalchemy==2.0.23
alembic==1.12.1

# Redis (optional for local dev)
redis==5.0.1
rq==1.15.1

# Validation
pydantic>=2.5.3
marshmallow==3.20.1

# Monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Web scraping
beautifulsoup4==4.12.2
aiohttp==3.9.1

# Development tools
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio==0.21.1
bandit==1.7.5
ruff==0.1.6
black==23.11.0
isort==5.12.0

# Werkzeug for password hashing
werkzeug==3.0.1

# Python-dotenv for environment variables
python-dotenv==1.0.0