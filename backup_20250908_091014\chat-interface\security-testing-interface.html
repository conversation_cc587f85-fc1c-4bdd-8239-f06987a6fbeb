<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Testing Suite - Chat Box AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #dc2626;
            --primary-hover: #b91c1c;
            --secondary-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #2563eb;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --bg-dark: #1e293b;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --bg-dark: #0f172a;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--danger-color));
            color: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .warning-banner {
            background: var(--warning-color);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
        }

        .card h2 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }

        .secure-input {
            position: relative;
        }

        .secure-input .toggle-password {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            padding: 4px;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .checkbox-item:hover {
            background: var(--bg-tertiary);
        }

        .checkbox-item.selected {
            background: rgba(220, 38, 38, 0.1);
            border-color: var(--primary-color);
        }

        .checkbox-item.dangerous {
            border-left: 4px solid var(--danger-color);
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }

        .checkbox-item .icon {
            color: var(--primary-color);
            font-size: 16px;
        }

        .checkbox-item .label {
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
        }

        .checkbox-item .risk-badge {
            background: var(--danger-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
        }

        .operation-modes {
            grid-column: span 2;
        }

        .mode-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .mode-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .mode-item:hover {
            background: var(--bg-tertiary);
        }

        .mode-item.selected {
            background: rgba(220, 38, 38, 0.1);
            border-color: var(--primary-color);
        }

        .mode-item.high-risk {
            border-left: 4px solid var(--danger-color);
        }

        .mode-icon {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .mode-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .mode-description {
            font-size: 12px;
            color: var(--text-muted);
        }

        .action-section {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .execution-panel {
            background: var(--bg-dark);
            color: white;
            border-radius: 12px;
            padding: 24px;
            margin-top: 20px;
        }

        .terminal {
            background: #000;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 16px;
        }

        .terminal-line {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .terminal-line.success {
            color: #10b981;
        }

        .terminal-line.error {
            color: #ef4444;
        }

        .terminal-line.warning {
            color: #f59e0b;
        }

        .terminal-line.info {
            color: #3b82f6;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .status-card {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .status-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .status-label {
            font-size: 12px;
            color: var(--text-muted);
            margin-top: 4px;
        }

        .results-section {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin-top: 20px;
            display: none;
        }

        .results-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .results-tab {
            padding: 8px 16px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .results-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .results-content {
            min-height: 200px;
        }

        .vulnerability-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid var(--danger-color);
        }

        .vulnerability-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .vulnerability-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .severity-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .severity-critical {
            background: #dc2626;
        }

        .severity-high {
            background: #ea580c;
        }

        .severity-medium {
            background: #d97706;
        }

        .severity-low {
            background: #65a30d;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            max-width: 400px;
        }

        .notification.success {
            background: var(--secondary-color);
        }

        .notification.error {
            background: var(--danger-color);
        }

        /* Validation Error Styles */
        .validation-errors {
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            color: #dc3545;
        }
        
        .error-message {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }
        
        .field-error {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }
        
        input.error, select.error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
        }
        
        input.error:focus, select.error:focus {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3) !important;
        }

        .credential-testing-section {
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            margin-bottom: 24px;
            border-left: 5px solid var(--primary-color);
            position: relative;
            overflow: hidden;
        }
        
        .credential-testing-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }
        
        .credential-testing-section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 24px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .credential-testing-section h2 i {
            font-size: 1.25rem;
            opacity: 0.8;
        }
        
        .credential-testing-section .card {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 24px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .credential-testing-section .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }
        
        .credential-testing-section .card h2 {
            font-size: 1.125rem;
            margin-bottom: 20px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 12px;
        }
        
        .credential-testing-section .form-group {
            margin-bottom: 20px;
        }
        
        .credential-testing-section .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.9rem;
        }
        
        .credential-testing-section .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .credential-testing-section .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
        }
        
        .credential-testing-section .secure-input {
            position: relative;
        }
        
        .credential-testing-section .toggle-password {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .credential-testing-section .toggle-password:hover {
            color: var(--primary-color);
            background: rgba(var(--primary-rgb), 0.1);
        }

        .credential-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        @media (max-width: 1200px) {
            .credential-grid {
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .credential-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }
        
        .credential-testing-section .checkbox-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 16px;
        }
        
        .credential-testing-section .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
            position: relative;
        }
        
        .credential-testing-section .checkbox-item:hover {
            border-color: var(--primary-color);
            background: rgba(var(--primary-rgb), 0.05);
            transform: translateY(-1px);
        }
        
        .credential-testing-section .checkbox-item.dangerous {
            border-color: var(--danger-color);
        }
        
        .credential-testing-section .checkbox-item.dangerous:hover {
            border-color: var(--danger-color);
            background: rgba(220, 53, 69, 0.05);
        }
        
        .credential-testing-section .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }
        
        .credential-testing-section .checkbox-item .icon {
            font-size: 1rem;
            color: var(--text-secondary);
        }
        
        .credential-testing-section .checkbox-item .label {
            flex: 1;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .credential-testing-section .risk-badge {
            background: var(--danger-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .credential-testing-section .action-section {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 2px solid var(--border-color);
        }
        
        .credential-testing-section .action-buttons {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .credential-testing-section .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
            justify-content: center;
        }
        
        .credential-testing-section .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .credential-testing-section .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
        }
        
        .credential-testing-section .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .credential-testing-section .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }
        
        .credential-testing-section .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-primary);
            border: 2px solid var(--border-color);
        }
        
        .credential-testing-section .btn-secondary:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .notification.error {
            background: var(--danger-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .theme-toggle {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 1024px) {
            .main-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .operation-modes {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .operation-modes {
                grid-column: span 1;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .mode-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body data-theme="light">
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> Security Testing Suite</h1>
            <button class="theme-toggle" onclick="toggleTheme()">
                <i class="fas fa-sun"></i> Light
            </button>
        </div>

        <div class="warning-banner">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>WARNING:</strong> This tool is for authorized security testing only. Unauthorized use is illegal and unethical.
        </div>

        <div class="main-grid">
            <!-- Credential Injection -->
            <div class="card">
                <h2><i class="fas fa-key"></i> Target & Credentials</h2>
                
                <div class="form-group">
                    <label for="targetDomain">Target Domain/Website</label>
                    <input type="url" id="targetDomain" class="form-input" placeholder="https://target-website.com" required>
                </div>

                <div class="form-group">
                    <label for="username">Username (Optional)</label>
                    <input type="text" id="username" class="form-input" placeholder="Enter username">
                </div>

                <div class="form-group">
                    <label for="password">Password (Optional)</label>
                    <div class="secure-input">
                        <input type="password" id="password" class="form-input" placeholder="Enter password">
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="sessionTimeout">Session Timeout (minutes)</label>
                    <input type="number" id="sessionTimeout" class="form-input" value="30" min="5" max="120">
                </div>
            </div>

            <!-- Target File/Data Types -->
            <div class="card">
                <h2><i class="fas fa-bullseye"></i> Target Data Types</h2>
                
                <div class="checkbox-grid">
                    <div class="checkbox-item" onclick="toggleDataType('html')">
                        <input type="checkbox" id="html" checked>
                        <i class="fas fa-code icon"></i>
                        <span class="label">HTML</span>
                    </div>

                    <div class="checkbox-item" onclick="toggleDataType('pdf')">
                        <input type="checkbox" id="pdf">
                        <i class="fas fa-file-pdf icon"></i>
                        <span class="label">PDF</span>
                    </div>

                    <div class="checkbox-item" onclick="toggleDataType('word')">
                        <input type="checkbox" id="word">
                        <i class="fas fa-file-word icon"></i>
                        <span class="label">Word</span>
                    </div>

                    <div class="checkbox-item" onclick="toggleDataType('excel')">
                        <input type="checkbox" id="excel">
                        <i class="fas fa-file-excel icon"></i>
                        <span class="label">Excel</span>
                    </div>

                    <div class="checkbox-item" onclick="toggleDataType('json')">
                        <input type="checkbox" id="json">
                        <i class="fas fa-file-code icon"></i>
                        <span class="label">JSON</span>
                    </div>

                    <div class="checkbox-item" onclick="toggleDataType('csv')">
                        <input type="checkbox" id="csv">
                        <i class="fas fa-file-csv icon"></i>
                        <span class="label">CSV</span>
                    </div>

                    <div class="checkbox-item" onclick="toggleDataType('rawdata')">
                        <input type="checkbox" id="rawdata" checked>
                        <i class="fas fa-database icon"></i>
                        <span class="label">Raw Data</span>
                    </div>
                </div>
            </div>

            <!-- Operation Modes -->
            <div class="card operation-modes">
                <h2><i class="fas fa-cogs"></i> Operation Modes</h2>
                
                <div class="mode-grid">
                    <div class="mode-item" onclick="toggleMode('scraping')">
                        <div class="mode-icon"><i class="fas fa-spider"></i></div>
                        <div class="mode-title">Web Scraping</div>
                        <div class="mode-description">Extract data from web pages</div>
                    </div>

                    <div class="mode-item" onclick="toggleMode('crawling')">
                        <div class="mode-icon"><i class="fas fa-sitemap"></i></div>
                        <div class="mode-title">Web Crawling</div>
                        <div class="mode-description">Discover and map site structure</div>
                    </div>

                    <div class="mode-item high-risk" onclick="toggleMode('pentest')">
                        <div class="mode-icon"><i class="fas fa-user-secret"></i></div>
                        <div class="mode-title">Penetration Testing</div>
                        <div class="mode-description">Security vulnerability assessment</div>
                        <div class="risk-badge">HIGH RISK</div>
                    </div>

                    <div class="mode-item high-risk" onclick="toggleMode('dirbrute')">
                        <div class="mode-icon"><i class="fas fa-folder-open"></i></div>
                        <div class="mode-title">Directory Brute Force</div>
                        <div class="mode-description">Discover hidden directories</div>
                        <div class="risk-badge">HIGH RISK</div>
                    </div>

                    <div class="mode-item high-risk" onclick="toggleMode('forminjection')">
                        <div class="mode-icon"><i class="fas fa-syringe"></i></div>
                        <div class="mode-title">Form Injection</div>
                        <div class="mode-description">Test for injection vulnerabilities</div>
                        <div class="risk-badge">CRITICAL</div>
                    </div>

                    <div class="mode-item high-risk" onclick="toggleMode('cookiestealing')">
                        <div class="mode-icon"><i class="fas fa-cookie-bite"></i></div>
                        <div class="mode-title">Cookie Analysis</div>
                        <div class="mode-description">Analyze session management</div>
                        <div class="risk-badge">HIGH RISK</div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-secondary" onclick="selectAllModes()">
                        <i class="fas fa-check-double"></i> Select All
                    </button>
                    <button class="btn btn-secondary" onclick="clearAllModes()">
                        <i class="fas fa-times"></i> Clear All
                    </button>
                </div>
            </div>
        </div>

        <!-- Credential Testing Section -->
        <div class="credential-testing-section">
            <h2 style="color: var(--primary-color); margin-bottom: 20px; display: flex; align-items: center; gap: 12px;">
                <i class="fas fa-user-lock"></i> Website Credential Testing
            </h2>
            
            <div class="credential-grid">
                <!-- Login Form Testing -->
                <div class="card">
                    <h2><i class="fas fa-sign-in-alt"></i> Login Form Attack</h2>
                    
                    <div class="form-group">
                        <label for="credential-target-url">Login Page URL</label>
                        <input type="url" id="credential-target-url" class="form-input" placeholder="https://target-site.com/login" required>
                    </div>

                    <div class="form-group">
                        <label for="credential-username">Test Username</label>
                        <input type="text" id="credential-username" class="form-input" placeholder="admin, user, <EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="credential-password">Test Password</label>
                        <div class="secure-input">
                            <input type="password" id="credential-password" class="form-input" placeholder="password, 123456, admin">
                            <button type="button" class="toggle-password" onclick="toggleCredentialPassword('credential-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="usernameField">Username Field Name</label>
                        <input type="text" id="usernameField" class="form-input" placeholder="username, email, user" value="username">
                    </div>

                    <div class="form-group">
                        <label for="passwordField">Password Field Name</label>
                        <input type="text" id="passwordField" class="form-input" placeholder="password, pass, pwd" value="password">
                    </div>
                </div>

                <!-- Credential Attack Methods -->
                <div class="card">
                    <h2><i class="fas fa-shield-alt"></i> Attack Methods</h2>
                    
                    <div class="checkbox-grid">
                        <div class="checkbox-item dangerous" onclick="toggleCredentialMethod('bruteforce')">
                            <input type="checkbox" id="bruteforce">
                            <i class="fas fa-hammer icon"></i>
                            <span class="label">Brute Force</span>
                            <span class="risk-badge">HIGH</span>
                        </div>

                        <div class="checkbox-item dangerous" onclick="toggleCredentialMethod('dictionary')">
                            <input type="checkbox" id="dictionary" checked>
                            <i class="fas fa-book icon"></i>
                            <span class="label">Dictionary Attack</span>
                            <span class="risk-badge">MED</span>
                        </div>

                        <div class="checkbox-item" onclick="toggleCredentialMethod('defaultcreds')">
                            <input type="checkbox" id="defaultcreds" checked>
                            <i class="fas fa-key icon"></i>
                            <span class="label">Default Credentials</span>
                        </div>

                        <div class="checkbox-item dangerous" onclick="toggleCredentialMethod('sqlinjection')">
                            <input type="checkbox" id="sqlinjection">
                            <i class="fas fa-database icon"></i>
                            <span class="label">SQL Injection</span>
                            <span class="risk-badge">CRIT</span>
                        </div>

                        <div class="checkbox-item" onclick="toggleCredentialMethod('sessiontest')">
                            <input type="checkbox" id="sessiontest" checked>
                            <i class="fas fa-clock icon"></i>
                            <span class="label">Session Testing</span>
                        </div>

                        <div class="checkbox-item" onclick="toggleCredentialMethod('captchabypass')">
                            <input type="checkbox" id="captchabypass">
                            <i class="fas fa-robot icon"></i>
                            <span class="label">CAPTCHA Analysis</span>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options -->
                <div class="card">
                    <h2><i class="fas fa-cog"></i> Advanced Settings</h2>
                    
                    <div class="form-group">
                        <label for="requestDelay">Request Delay (ms)</label>
                        <input type="number" id="requestDelay" class="form-input" value="1000" min="100" max="10000">
                    </div>

                    <div class="form-group">
                        <label for="maxAttempts">Max Attempts</label>
                        <input type="number" id="maxAttempts" class="form-input" value="100" min="1" max="1000">
                    </div>

                    <div class="form-group">
                        <label for="userAgent">User Agent</label>
                        <select id="userAgent" class="form-input">
                            <option value="chrome">Chrome (Latest)</option>
                            <option value="firefox">Firefox (Latest)</option>
                            <option value="safari">Safari (Latest)</option>
                            <option value="custom">Custom</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="proxySettings">Proxy (Optional)</label>
                        <input type="text" id="proxySettings" class="form-input" placeholder="http://proxy:port">
                    </div>

                    <div class="checkbox-grid">
                        <div class="checkbox-item" onclick="toggleAdvancedOption('followRedirects')">
                            <input type="checkbox" id="followRedirects" checked>
                            <i class="fas fa-route icon"></i>
                            <span class="label">Follow Redirects</span>
                        </div>

                        <div class="checkbox-item" onclick="toggleAdvancedOption('saveCookies')">
                            <input type="checkbox" id="saveCookies" checked>
                            <i class="fas fa-cookie icon"></i>
                            <span class="label">Save Cookies</span>
                        </div>

                        <div class="checkbox-item" onclick="toggleAdvancedOption('verifySSL')">
                            <input type="checkbox" id="verifySSL" checked>
                            <i class="fas fa-lock icon"></i>
                            <span class="label">Verify SSL</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credential Testing Actions -->
            <div class="action-section">
                <h2><i class="fas fa-play-circle"></i> Credential Testing Control</h2>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="startCredentialTest()" id="startCredentialBtn">
                        <i class="fas fa-user-secret"></i> Start Credential Test
                    </button>
                    <button class="btn btn-danger" onclick="stopCredentialTest()" disabled id="stopCredentialBtn">
                        <i class="fas fa-stop"></i> Stop Test
                    </button>
                    <button class="btn btn-secondary" onclick="clearCredentialResults()">
                        <i class="fas fa-trash"></i> Clear Results
                    </button>
                    <button class="btn btn-secondary" onclick="exportCredentialResults()">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Action Section -->
        <div class="action-section">
            <h2><i class="fas fa-play-circle"></i> Execution Control</h2>
            
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="startOperation()" id="startBtn">
                    <i class="fas fa-rocket"></i> Start Security Test
                </button>
                <button class="btn btn-danger" onclick="stopOperation()" disabled id="stopBtn">
                    <i class="fas fa-stop"></i> Emergency Stop
                </button>
                <button class="btn btn-secondary" onclick="clearSession()">
                    <i class="fas fa-trash"></i> Clear Session
                </button>
                <button class="btn btn-secondary" onclick="exportResults()">
                    <i class="fas fa-download"></i> Export Results
                </button>
            </div>
        </div>

        <!-- Execution Panel -->
        <div class="execution-panel" id="executionPanel" style="display: none;">
            <h2><i class="fas fa-terminal"></i> Live Execution Monitor</h2>
            
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number" id="testsRun">0</div>
                    <div class="status-label">Tests Executed</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="vulnerabilities">0</div>
                    <div class="status-label">Vulnerabilities Found</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="dataExtracted">0</div>
                    <div class="status-label">Data Points Extracted</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="timeElapsed">00:00</div>
                    <div class="status-label">Time Elapsed</div>
                </div>
            </div>

            <div class="terminal" id="terminal">
                <div class="terminal-line info">
                    <span>[INFO]</span> Security Testing Suite initialized and ready
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="resultsSection">
            <h2><i class="fas fa-chart-bar"></i> Security Assessment Results</h2>
            
            <div class="results-tabs">
                <button class="results-tab active" onclick="switchResultsTab('vulnerabilities')">Vulnerabilities</button>
                <button class="results-tab" onclick="switchResultsTab('data')">Extracted Data</button>
                <button class="results-tab" onclick="switchResultsTab('summary')">Summary Report</button>
            </div>

            <div class="results-content" id="resultsContent">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>

    <script>
        class SecurityTestingSuite {
            constructor() {
                this.isRunning = false;
                this.startTime = null;
                this.timer = null;
                this.selectedModes = [];
                this.selectedDataTypes = [];
                this.credentials = null;
                this.results = {
                    vulnerabilities: [],
                    extractedData: [],
                    summary: {}
                };
                this.stats = {
                    testsRun: 0,
                    vulnerabilities: 0,
                    dataExtracted: 0
                };
            }

            async startOperation() {
                if (this.isRunning) return;

                // Validate inputs
                const domain = document.getElementById('targetDomain').value;
                if (!domain) {
                    this.showNotification('Please enter a target domain', 'error');
                    return;
                }

                this.selectedModes = this.getSelectedModes();
                if (this.selectedModes.length === 0) {
                    this.showNotification('Please select at least one operation mode', 'error');
                    return;
                }

                this.selectedDataTypes = this.getSelectedDataTypes();
                if (this.selectedDataTypes.length === 0) {
                    this.showNotification('Please select at least one data type', 'error');
                    return;
                }

                // Security confirmation for high-risk operations
                const highRiskModes = ['pentest', 'dirbrute', 'forminjection', 'cookiestealing'];
                const hasHighRisk = this.selectedModes.some(mode => highRiskModes.includes(mode));
                
                if (hasHighRisk) {
                    const confirmed = confirm(
                        'WARNING: You have selected high-risk security testing operations. ' +
                        'Ensure you have proper authorization to test this target. ' +
                        'Unauthorized testing is illegal. Continue?'
                    );
                    if (!confirmed) return;
                }

                this.isRunning = true;
                this.startTime = Date.now();
                this.resetStats();
                
                // Store credentials securely (session only)
                this.credentials = {
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    domain: domain
                };

                // Update UI
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('executionPanel').style.display = 'block';
                document.getElementById('resultsSection').style.display = 'none';
                
                this.addTerminalLine('Starting security testing operation...', 'info');
                this.addTerminalLine('Target: ' + domain, 'info');
                this.addTerminalLine('Selected modes: ' + this.selectedModes.join(', '), 'info');
                this.addTerminalLine('Data types: ' + this.selectedDataTypes.join(', '), 'info');
                
                this.startTimer();
                
                // Execute operations
                await this.executeOperations();
            }

            async executeOperations() {
                for (const mode of this.selectedModes) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Executing ' + mode + ' operation...', 'info');
                    await this.executeMode(mode);
                    await this.delay(1000);
                }

                if (this.isRunning) {
                    this.completeOperation();
                }
            }

            async executeMode(mode) {
                const operations = {
                    scraping: () => this.executeScraping(),
                    crawling: () => this.executeCrawling(),
                    pentest: () => this.executePenetrationTest(),
                    dirbrute: () => this.executeDirectoryBruteForce(),
                    forminjection: () => this.executeFormInjection(),
                    cookiestealing: () => this.executeCookieAnalysis()
                };

                if (operations[mode]) {
                    await operations[mode]();
                }
            }

            async executeScraping() {
                this.addTerminalLine('Initializing web scraping module...', 'info');
                await this.delay(500);
                
                for (const dataType of this.selectedDataTypes) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Scraping ' + dataType + ' content...', 'info');
                    await this.delay(800);
                    
                    // Simulate finding data
                    const found = Math.floor(Math.random() * 10) + 1;
                    this.stats.dataExtracted += found;
                    this.stats.testsRun++;
                    
                    this.results.extractedData.push({
                        type: dataType,
                        count: found,
                        timestamp: new Date().toISOString()
                    });
                    
                    this.addTerminalLine('Found ' + found + ' ' + dataType + ' items', 'success');
                    this.updateStats();
                }
            }

            async executeCrawling() {
                this.addTerminalLine('Starting web crawling operation...', 'info');
                await this.delay(500);
                
                const pages = ['/', '/admin', '/login', '/api', '/docs', '/config'];
                
                for (const page of pages) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Crawling ' + page + '...', 'info');
                    await this.delay(600);
                    
                    this.stats.testsRun++;
                    
                    if (Math.random() > 0.7) {
                        this.addTerminalLine('Discovered: ' + page + ' (Status: 200)', 'success');
                    } else {
                        this.addTerminalLine(page + ' - Access denied (Status: 403)', 'warning');
                    }
                    
                    this.updateStats();
                }
            }

            async executePenetrationTest() {
                this.addTerminalLine('Initiating penetration testing suite...', 'info');
                await this.delay(500);
                
                const tests = [
                    { name: 'SQL Injection', severity: 'critical' },
                    { name: 'XSS Vulnerability', severity: 'high' },
                    { name: 'CSRF Token Missing', severity: 'medium' },
                    { name: 'Weak Password Policy', severity: 'low' },
                    { name: 'Unencrypted Data Transmission', severity: 'high' }
                ];
                
                for (const test of tests) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Testing for ' + test.name + '...', 'info');
                    await this.delay(1200);
                    
                    this.stats.testsRun++;
                    
                    if (Math.random() > 0.6) {
                        this.stats.vulnerabilities++;
                        this.results.vulnerabilities.push({
                            name: test.name,
                            severity: test.severity,
                            description: test.name + ' vulnerability detected in target application',
                            timestamp: new Date().toISOString()
                        });
                        
                        this.addTerminalLine('VULNERABILITY FOUND: ' + test.name + ' (' + test.severity + ')', 'error');
                    } else {
                        this.addTerminalLine(test.name + ' - No vulnerabilities detected', 'success');
                    }
                    
                    this.updateStats();
                }
            }

            async executeDirectoryBruteForce() {
                this.addTerminalLine('Starting directory brute force attack...', 'warning');
                await this.delay(500);
                
                const directories = ['admin', 'backup', 'config', 'test', 'dev', 'api', 'uploads', 'logs'];
                
                for (const dir of directories) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Brute forcing /' + dir + '/...', 'info');
                    await this.delay(400);
                    
                    this.stats.testsRun++;
                    
                    if (Math.random() > 0.8) {
                        this.addTerminalLine('FOUND: /' + dir + '/ (200 OK)', 'error');
                        this.stats.vulnerabilities++;
                        
                        this.results.vulnerabilities.push({
                            name: 'Exposed Directory',
                            severity: 'medium',
                            description: 'Directory /' + dir + '/ is accessible without authentication',
                            timestamp: new Date().toISOString()
                        });
                    } else {
                        this.addTerminalLine('/' + dir + '/ - Not found (404)', 'info');
                    }
                    
                    this.updateStats();
                }
            }

            async executeFormInjection() {
                this.addTerminalLine('Testing form injection vulnerabilities...', 'warning');
                await this.delay(500);
                
                const injectionTests = [
                    "' OR '1'='1",
                    '<script>alert("XSS")</script>',
                    '../../etc/passwd',
                    '49',
                    "admin'--"
                ];
                
                for (const injection of injectionTests) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Testing injection: ' + injection.substring(0, 20) + '...', 'info');
                    await this.delay(800);
                    
                    this.stats.testsRun++;
                    
                    if (Math.random() > 0.7) {
                        this.addTerminalLine('INJECTION SUCCESSFUL: ' + injection.substring(0, 20) + '...', 'error');
                        this.stats.vulnerabilities++;
                        
                        this.results.vulnerabilities.push({
                            name: 'Form Injection Vulnerability',
                            severity: 'critical',
                            description: 'Form accepts malicious input: ' + injection.substring(0, 30) + '...',
                            timestamp: new Date().toISOString()
                        });
                    } else {
                        this.addTerminalLine('Injection blocked by security measures', 'success');
                    }
                    
                    this.updateStats();
                }
            }

            async executeCookieAnalysis() {
                this.addTerminalLine('Analyzing session management and cookies...', 'info');
                await this.delay(500);
                
                const cookieTests = [
                    'HttpOnly flag check',
                    'Secure flag verification',
                    'SameSite attribute test',
                    'Session fixation test',
                    'Cookie encryption analysis'
                ];
                
                for (const test of cookieTests) {
                    if (!this.isRunning) break;
                    
                    this.addTerminalLine('Executing ' + test + '...', 'info');
                    await this.delay(700);
                    
                    this.stats.testsRun++;
                    
                    if (Math.random() > 0.6) {
                        this.addTerminalLine('SECURITY ISSUE: ' + test + ' failed', 'error');
                        this.stats.vulnerabilities++;
                        
                        this.results.vulnerabilities.push({
                            name: 'Cookie Security Issue',
                            severity: 'medium',
                            description: test + ' revealed security weakness',
                            timestamp: new Date().toISOString()
                        });
                    } else {
                        this.addTerminalLine(test + ' - Passed', 'success');
                    }
                    
                    this.updateStats();
                }
            }

            completeOperation() {
                this.isRunning = false;
                this.stopTimer();
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                
                this.addTerminalLine('Security testing operation completed', 'success');
                this.addTerminalLine('Total tests executed: ' + this.stats.testsRun, 'info');
                this.addTerminalLine('Vulnerabilities found: ' + this.stats.vulnerabilities, this.stats.vulnerabilities > 0 ? 'error' : 'success');
                this.addTerminalLine('Data points extracted: ' + this.stats.dataExtracted, 'info');
                
                this.showNotification('Security testing completed successfully!', 'success');
                this.displayResults();
                
                // Clear credentials from memory
                this.credentials = null;
            }

            stopOperation() {
                if (!this.isRunning) return;
                
                this.isRunning = false;
                this.stopTimer();
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                
                this.addTerminalLine('Operation stopped by user', 'warning');
                this.showNotification('Security testing stopped', 'warning');
                
                // Clear credentials from memory
                this.credentials = null;
            }

            getSelectedModes() {
                const modes = ['scraping', 'crawling', 'pentest', 'dirbrute', 'forminjection', 'cookiestealing'];
                return modes.filter(mode => {
                    const element = document.querySelector("[onclick=\"toggleMode('" + mode + "')\"]");
                    return element && element.classList.contains('selected');
                });
            }

            getSelectedDataTypes() {
                const types = ['html', 'pdf', 'word', 'excel', 'json', 'csv', 'rawdata'];
                return types.filter(type => document.getElementById(type).checked);
            }

            resetStats() {
                this.stats = {
                    testsRun: 0,
                    vulnerabilities: 0,
                    dataExtracted: 0
                };
                this.updateStats();
            }

            updateStats() {
                document.getElementById('testsRun').textContent = this.stats.testsRun;
                document.getElementById('vulnerabilities').textContent = this.stats.vulnerabilities;
                document.getElementById('dataExtracted').textContent = this.stats.dataExtracted;
            }

            startTimer() {
                this.timer = setInterval(() => {
                    if (this.startTime) {
                        const elapsed = Date.now() - this.startTime;
                        const minutes = Math.floor(elapsed / 60000);
                        const seconds = Math.floor((elapsed % 60000) / 1000);
                        document.getElementById('timeElapsed').textContent = 
                            minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
                    }
                }, 1000);
            }

            stopTimer() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }

            addTerminalLine(message, type = 'info') {
                const terminal = document.getElementById('terminal');
                const line = document.createElement('div');
                line.className = 'terminal-line ' + type;
                
                const timeSpan = document.createElement('span');
                timeSpan.textContent = '[' + new Date().toLocaleTimeString() + ']';
                
                const typeSpan = document.createElement('span');
                typeSpan.textContent = '[' + type.toUpperCase() + ']';
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                
                line.appendChild(timeSpan);
                line.appendChild(typeSpan);
                line.appendChild(messageSpan);
                
                terminal.appendChild(line);
                terminal.scrollTop = terminal.scrollHeight;
            }

            displayResults() {
                document.getElementById('resultsSection').style.display = 'block';
                this.switchResultsTab('vulnerabilities');
            }

            switchResultsTab(tab) {
                // Update tab buttons
                document.querySelectorAll('.results-tab').forEach(btn => {
                    btn.classList.toggle('active', btn.textContent.toLowerCase().includes(tab));
                });
                
                const content = document.getElementById('resultsContent');
                
                if (tab === 'vulnerabilities') {
                    this.renderVulnerabilities(content);
                } else if (tab === 'data') {
                    this.renderExtractedData(content);
                } else if (tab === 'summary') {
                    this.renderSummary(content);
                }
            }

            renderVulnerabilities(content) {
                content.innerHTML = '';
                
                if (this.results.vulnerabilities.length === 0) {
                    const noVulnMsg = document.createElement('p');
                    noVulnMsg.style.textAlign = 'center';
                    noVulnMsg.style.color = 'var(--text-muted)';
                    noVulnMsg.style.padding = '40px';
                    noVulnMsg.textContent = 'No vulnerabilities detected';
                    content.appendChild(noVulnMsg);
                    return;
                }
                
                this.results.vulnerabilities.forEach(vuln => {
                    const vulnItem = document.createElement('div');
                    vulnItem.className = 'vulnerability-item';
                    
                    const vulnHeader = document.createElement('div');
                    vulnHeader.className = 'vulnerability-header';
                    
                    const vulnTitle = document.createElement('div');
                    vulnTitle.className = 'vulnerability-title';
                    vulnTitle.textContent = vuln.name;
                    
                    const severityBadge = document.createElement('div');
                    severityBadge.className = 'severity-badge severity-' + vuln.severity;
                    severityBadge.textContent = vuln.severity.toUpperCase();
                    
                    vulnHeader.appendChild(vulnTitle);
                    vulnHeader.appendChild(severityBadge);
                    
                    const vulnDesc = document.createElement('div');
                    vulnDesc.style.color = 'var(--text-secondary)';
                    vulnDesc.style.fontSize = '14px';
                    vulnDesc.textContent = vuln.description;
                    
                    const vulnTime = document.createElement('div');
                    vulnTime.style.color = 'var(--text-muted)';
                    vulnTime.style.fontSize = '12px';
                    vulnTime.style.marginTop = '8px';
                    vulnTime.textContent = 'Detected: ' + new Date(vuln.timestamp).toLocaleString();
                    
                    vulnItem.appendChild(vulnHeader);
                    vulnItem.appendChild(vulnDesc);
                    vulnItem.appendChild(vulnTime);
                    
                    content.appendChild(vulnItem);
                });
            }

            renderExtractedData(content) {
                content.innerHTML = '';
                
                if (this.results.extractedData.length === 0) {
                    const noDataMsg = document.createElement('p');
                    noDataMsg.style.textAlign = 'center';
                    noDataMsg.style.color = 'var(--text-muted)';
                    noDataMsg.style.padding = '40px';
                    noDataMsg.textContent = 'No data extracted';
                    content.appendChild(noDataMsg);
                    return;
                }
                
                this.results.extractedData.forEach(data => {
                    const dataItem = document.createElement('div');
                    dataItem.style.background = 'var(--bg-primary)';
                    dataItem.style.border = '1px solid var(--border-color)';
                    dataItem.style.borderRadius = '8px';
                    dataItem.style.padding = '16px';
                    dataItem.style.marginBottom = '12px';
                    
                    const dataHeader = document.createElement('div');
                    dataHeader.style.display = 'flex';
                    dataHeader.style.justifyContent = 'space-between';
                    dataHeader.style.alignItems = 'center';
                    
                    const dataInfo = document.createElement('div');
                    
                    const dataType = document.createElement('strong');
                    dataType.textContent = data.type.toUpperCase();
                    
                    const dataCount = document.createElement('div');
                    dataCount.style.color = 'var(--text-secondary)';
                    dataCount.style.fontSize = '14px';
                    dataCount.textContent = data.count + ' items extracted';
                    
                    dataInfo.appendChild(dataType);
                    dataInfo.appendChild(dataCount);
                    
                    const dataTime = document.createElement('div');
                    dataTime.style.color = 'var(--text-muted)';
                    dataTime.style.fontSize = '12px';
                    dataTime.textContent = new Date(data.timestamp).toLocaleString();
                    
                    dataHeader.appendChild(dataInfo);
                    dataHeader.appendChild(dataTime);
                    
                    dataItem.appendChild(dataHeader);
                    content.appendChild(dataItem);
                });
            }

            renderSummary(content) {
                const totalVulns = this.results.vulnerabilities.length;
                const criticalVulns = this.results.vulnerabilities.filter(v => v.severity === 'critical').length;
                const highVulns = this.results.vulnerabilities.filter(v => v.severity === 'high').length;
                const totalData = this.results.extractedData.reduce((sum, data) => sum + data.count, 0);
                
                const vulnColor = totalVulns > 0 ? 'var(--danger-color)' : 'var(--secondary-color)';
                
                content.innerHTML = '';
                
                // Create status grid
                const statusGrid = document.createElement('div');
                statusGrid.style.display = 'grid';
                statusGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(200px, 1fr))';
                statusGrid.style.gap = '16px';
                
                // Total vulnerabilities card
                const totalCard = document.createElement('div');
                totalCard.className = 'status-card';
                const totalNumber = document.createElement('div');
                totalNumber.className = 'status-number';
                totalNumber.style.color = vulnColor;
                totalNumber.textContent = totalVulns;
                const totalLabel = document.createElement('div');
                totalLabel.className = 'status-label';
                totalLabel.textContent = 'Total Vulnerabilities';
                totalCard.appendChild(totalNumber);
                totalCard.appendChild(totalLabel);
                
                // Critical issues card
                const criticalCard = document.createElement('div');
                criticalCard.className = 'status-card';
                const criticalNumber = document.createElement('div');
                criticalNumber.className = 'status-number';
                criticalNumber.style.color = 'var(--danger-color)';
                criticalNumber.textContent = criticalVulns;
                const criticalLabel = document.createElement('div');
                criticalLabel.className = 'status-label';
                criticalLabel.textContent = 'Critical Issues';
                criticalCard.appendChild(criticalNumber);
                criticalCard.appendChild(criticalLabel);
                
                // High risk card
                const highCard = document.createElement('div');
                highCard.className = 'status-card';
                const highNumber = document.createElement('div');
                highNumber.className = 'status-number';
                highNumber.style.color = 'var(--warning-color)';
                highNumber.textContent = highVulns;
                const highLabel = document.createElement('div');
                highLabel.className = 'status-label';
                highLabel.textContent = 'High Risk Issues';
                highCard.appendChild(highNumber);
                highCard.appendChild(highLabel);
                
                // Data points card
                const dataCard = document.createElement('div');
                dataCard.className = 'status-card';
                const dataNumber = document.createElement('div');
                dataNumber.className = 'status-number';
                dataNumber.style.color = 'var(--info-color)';
                dataNumber.textContent = totalData;
                const dataLabel = document.createElement('div');
                dataLabel.className = 'status-label';
                dataLabel.textContent = 'Data Points';
                dataCard.appendChild(dataNumber);
                dataCard.appendChild(dataLabel);
                
                statusGrid.appendChild(totalCard);
                statusGrid.appendChild(criticalCard);
                statusGrid.appendChild(highCard);
                statusGrid.appendChild(dataCard);
                
                // Create summary section
                const summaryDiv = document.createElement('div');
                summaryDiv.style.marginTop = '24px';
                summaryDiv.style.padding = '16px';
                summaryDiv.style.background = 'var(--bg-primary)';
                summaryDiv.style.borderRadius = '8px';
                
                const summaryTitle = document.createElement('h3');
                summaryTitle.style.marginBottom = '12px';
                summaryTitle.textContent = 'Security Assessment Summary';
                
                const summaryText = document.createElement('p');
                summaryText.style.color = 'var(--text-secondary)';
                summaryText.style.lineHeight = '1.6';
                
                const summaryMessage = totalVulns > 0 ? 
                    'ATTENTION REQUIRED: ' + totalVulns + ' security issue(s) were identified, including ' + criticalVulns + ' critical and ' + highVulns + ' high-risk vulnerabilities that should be addressed immediately.' :
                    'GOOD NEWS: No significant security vulnerabilities were detected during this assessment.';
                
                summaryText.textContent = 'The security testing operation has been completed. ' + summaryMessage + ' Additionally, ' + totalData + ' data points were successfully extracted from the target.';
                
                summaryDiv.appendChild(summaryTitle);
                summaryDiv.appendChild(summaryText);
                
                content.appendChild(statusGrid);
                content.appendChild(summaryDiv);
            }

            clearSession() {
                this.results = {
                    vulnerabilities: [],
                    extractedData: [],
                    summary: {}
                };
                this.resetStats();
                this.credentials = null;
                
                document.getElementById('executionPanel').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'none';
                
                const terminal = document.getElementById('terminal');
                terminal.innerHTML = '';
                const initLine = document.createElement('div');
                initLine.className = 'terminal-line info';
                const infoSpan = document.createElement('span');
                infoSpan.textContent = '[INFO]';
                const messageSpan = document.createElement('span');
                messageSpan.textContent = ' Security Testing Suite initialized and ready';
                initLine.appendChild(infoSpan);
                initLine.appendChild(messageSpan);
                terminal.appendChild(initLine);
                
                document.getElementById('timeElapsed').textContent = '00:00';
                
                this.showNotification('Session cleared successfully', 'success');
            }

            exportResults() {
                const report = {
                    timestamp: new Date().toISOString(),
                    target: document.getElementById('targetDomain').value,
                    operations: this.selectedModes,
                    dataTypes: this.selectedDataTypes,
                    statistics: this.stats,
                    vulnerabilities: this.results.vulnerabilities,
                    extractedData: this.results.extractedData,
                    summary: {
                        totalVulnerabilities: this.results.vulnerabilities.length,
                        criticalIssues: this.results.vulnerabilities.filter(v => v.severity === 'critical').length,
                        highRiskIssues: this.results.vulnerabilities.filter(v => v.severity === 'high').length,
                        totalDataPoints: this.results.extractedData.reduce((sum, data) => sum + data.count, 0)
                    }
                };
                
                const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'security-assessment-' + new Date().toISOString().split('T')[0] + '.json';
                a.click();
                URL.revokeObjectURL(url);
                
                this.showNotification('Security assessment report exported', 'success');
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = 'notification ' + type;
                
                const content = document.createElement('div');
                content.style.cssText = 'display: flex; justify-content: space-between; align-items: center;';
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                
                const closeBtn = document.createElement('button');
                closeBtn.innerHTML = '&times;';
                closeBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;';
                closeBtn.onclick = () => notification.remove();
                
                content.appendChild(messageSpan);
                content.appendChild(closeBtn);
                notification.appendChild(content);
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Initialize the security testing suite
        const securitySuite = new SecurityTestingSuite();

        // Credential Testing Functions
        let credentialTestRunning = false;
        let credentialTestTimer = null;
        let credentialResults = {
            attempts: 0,
            successful: 0,
            failed: 0,
            vulnerabilities: []
        };

        function validateCredentialForm() {
            const targetUrl = document.getElementById('credential-target-url').value.trim();
            const username = document.getElementById('credential-username').value.trim();
            const password = document.getElementById('credential-password').value;
            const usernameField = document.getElementById('credential-username-field').value.trim();
            const passwordField = document.getElementById('credential-password-field').value.trim();
            const methods = Array.from(document.querySelectorAll('input[name="attack-method"]:checked')).map(cb => cb.value);
            
            // Clear previous errors
            clearValidationErrors();
            
            let isValid = true;
            const errors = [];
            
            // Validate target URL
            if (!targetUrl) {
                showFieldError('credential-target-url', 'Target URL is required');
                errors.push('Target URL is required');
                isValid = false;
            } else {
                try {
                    const url = new URL(targetUrl);
                    if (!['http:', 'https:'].includes(url.protocol)) {
                        showFieldError('credential-target-url', 'URL must use HTTP or HTTPS protocol');
                        errors.push('Invalid URL protocol');
                        isValid = false;
                    }
                } catch (e) {
                    showFieldError('credential-target-url', 'Please enter a valid URL');
                    errors.push('Invalid URL format');
                    isValid = false;
                }
            }
            
            // Validate field names
            if (!usernameField) {
                showFieldError('credential-username-field', 'Username field name is required');
                errors.push('Username field name is required');
                isValid = false;
            }
            
            if (!passwordField) {
                showFieldError('credential-password-field', 'Password field name is required');
                errors.push('Password field name is required');
                isValid = false;
            }
            
            // Validate attack methods
            if (methods.length === 0) {
                showValidationError('Please select at least one attack method');
                errors.push('No attack method selected');
                isValid = false;
            }
            
            // Validate username for dictionary attack
            if (methods.includes('dictionary') && !username) {
                showFieldError('credential-username', 'Username is required for dictionary attack');
                errors.push('Username required for dictionary attack');
                isValid = false;
            }
            
            // Validate request delay
            const requestDelay = parseInt(document.getElementById('request-delay').value);
            if (requestDelay < 100 || requestDelay > 10000) {
                showFieldError('request-delay', 'Request delay must be between 100ms and 10000ms');
                errors.push('Invalid request delay');
                isValid = false;
            }
            
            // Validate max attempts
            const maxAttempts = parseInt(document.getElementById('max-attempts').value);
            if (maxAttempts < 1 || maxAttempts > 1000) {
                showFieldError('max-attempts', 'Max attempts must be between 1 and 1000');
                errors.push('Invalid max attempts');
                isValid = false;
            }
            
            return { isValid, errors };
        }
        
        function showFieldError(fieldId, message) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.add('error');
                
                // Create or update error message
                let errorElement = field.parentNode.querySelector('.field-error');
                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'field-error';
                    field.parentNode.appendChild(errorElement);
                }
                errorElement.textContent = message;
            }
        }
        
        function showValidationError(message) {
            const errorContainer = document.querySelector('.credential-testing-section .validation-errors');
            if (!errorContainer) {
                const container = document.createElement('div');
                container.className = 'validation-errors';
                container.innerHTML = `<div class="error-message"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
                document.querySelector('.credential-testing-section').insertBefore(container, document.querySelector('.credential-grid'));
            } else {
                errorContainer.innerHTML = `<div class="error-message"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
            }
        }
        
        function clearValidationErrors() {
            // Clear field errors
            document.querySelectorAll('.error').forEach(field => {
                field.classList.remove('error');
            });
            
            document.querySelectorAll('.field-error').forEach(error => {
                error.remove();
            });
            
            // Clear validation errors
            const errorContainer = document.querySelector('.credential-testing-section .validation-errors');
            if (errorContainer) {
                errorContainer.remove();
            }
        }

        function toggleCredentialPassword(fieldId) {
            const field = document.getElementById(fieldId);
            const button = field.nextElementSibling;
            const icon = button.querySelector('i');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        function toggleCredentialMethod(method) {
            const checkbox = document.getElementById(method);
            const item = checkbox.closest('.checkbox-item');
            
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }

        function toggleAdvancedOption(option) {
            const checkbox = document.getElementById(option);
            const item = checkbox.closest('.checkbox-item');
            
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }

        async function startCredentialTest() {
            if (credentialTestRunning) return;
            
            // Validate form before starting test
            const validation = validateCredentialForm();
            if (!validation.isValid) {
                showNotification('Please fix the validation errors before starting the test', 'error');
                return;
            }
            
            const targetUrl = document.getElementById('credential-target-url').value;
            const username = document.getElementById('credential-username').value;
            const password = document.getElementById('credential-password').value;
            
            if (!targetUrl) {
                showNotification('Please enter a target URL', 'error');
                return;
            }
            
            credentialTestRunning = true;
            document.getElementById('startCredentialBtn').disabled = true;
            document.getElementById('stopCredentialBtn').disabled = false;
            
            // Reset results
            credentialResults = {
                attempts: 0,
                successful: 0,
                failed: 0,
                vulnerabilities: []
            };
            
            showNotification('Starting credential testing...', 'info');
            
            // Get selected methods
            const selectedMethods = [];
            ['bruteforce', 'dictionary', 'defaultcreds', 'sqlinjection', 'sessiontest', 'captchabypass'].forEach(method => {
                if (document.getElementById(method).checked) {
                    selectedMethods.push(method);
                }
            });
            
            // Prepare test data
            const testData = {
                target_url: targetUrl,
                username: username,
                password: password,
                username_field: document.getElementById('usernameField').value,
                password_field: document.getElementById('passwordField').value,
                methods: selectedMethods,
                request_delay: parseInt(document.getElementById('requestDelay').value),
                max_attempts: parseInt(document.getElementById('maxAttempts').value),
                user_agent: document.getElementById('userAgent').value,
                proxy: document.getElementById('proxySettings').value,
                follow_redirects: document.getElementById('followRedirects').checked,
                save_cookies: document.getElementById('saveCookies').checked,
                verify_ssl: document.getElementById('verifySSL').checked
            };
            
            try {
                const response = await fetch('/api/credential-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    handleCredentialTestResult(result);
                } else {
                    throw new Error('Failed to start credential test');
                }
            } catch (error) {
                console.error('Credential test error:', error);
                showNotification('Failed to start credential test: ' + error.message, 'error');
                stopCredentialTest();
            }
        }

        function stopCredentialTest() {
            credentialTestRunning = false;
            document.getElementById('startCredentialBtn').disabled = false;
            document.getElementById('stopCredentialBtn').disabled = true;
            
            if (credentialTestTimer) {
                clearInterval(credentialTestTimer);
                credentialTestTimer = null;
            }
            
            showNotification('Credential test stopped', 'warning');
        }

        function handleCredentialTestResult(result) {
            if (result.status === 'success') {
                credentialResults.successful++;
                showNotification('Successful login found!', 'success');
                
                // Add vulnerability if credentials work
                credentialResults.vulnerabilities.push({
                    type: 'weak_credentials',
                    severity: 'high',
                    description: `Successful login with credentials: ${result.username}:${result.password}`,
                    timestamp: new Date().toISOString()
                });
            } else if (result.status === 'failed') {
                credentialResults.failed++;
            } else if (result.status === 'vulnerability') {
                credentialResults.vulnerabilities.push({
                    type: result.vulnerability_type,
                    severity: result.severity,
                    description: result.description,
                    timestamp: new Date().toISOString()
                });
            }
            
            credentialResults.attempts++;
            
            // Update UI with results
            updateCredentialTestUI();
            
            // Check if test is complete
            if (result.status === 'complete' || result.status === 'error') {
                stopCredentialTest();
                if (result.status === 'error') {
                    showNotification('Credential test completed with errors', 'warning');
                } else {
                    showNotification('Credential test completed successfully', 'success');
                }
            }
        }

        function updateCredentialTestUI() {
            // This would update a results display if we had one
            // For now, just log the results
            console.log('Credential Test Results:', credentialResults);
        }

        function clearCredentialResults() {
            credentialResults = {
                attempts: 0,
                successful: 0,
                failed: 0,
                vulnerabilities: []
            };
            
            // Clear form fields
            document.getElementById('loginTargetUrl').value = '';
            document.getElementById('testUsername').value = '';
            document.getElementById('testPassword').value = '';
            
            showNotification('Credential test results cleared', 'info');
        }

        function exportCredentialResults() {
            const exportData = {
                timestamp: new Date().toISOString(),
                target: document.getElementById('loginTargetUrl').value,
                results: credentialResults,
                summary: {
                    total_attempts: credentialResults.attempts,
                    successful_logins: credentialResults.successful,
                    failed_attempts: credentialResults.failed,
                    vulnerabilities_found: credentialResults.vulnerabilities.length
                }
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `credential-test-results-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showNotification('Results exported successfully', 'success');
         }

         // Global functions
        function toggleDataType(id) {
            const checkbox = document.getElementById(id);
            const item = checkbox.closest('.checkbox-item');
            
            checkbox.checked = !checkbox.checked;
            item.classList.toggle('selected', checkbox.checked);
        }

        function toggleMode(mode) {
            const item = document.querySelector("[onclick=\"toggleMode('" + mode + "')\"]");
            item.classList.toggle('selected');
        }

        function selectAllModes() {
            document.querySelectorAll('.mode-item').forEach(item => {
                item.classList.add('selected');
            });
        }

        function clearAllModes() {
            document.querySelectorAll('.mode-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }

        function startOperation() {
            securitySuite.startOperation();
        }

        function stopOperation() {
            securitySuite.stopOperation();
        }

        function clearSession() {
            securitySuite.clearSession();
        }

        function exportResults() {
            securitySuite.exportResults();
        }

        function switchResultsTab(tab) {
            securitySuite.switchResultsTab(tab);
        }

        function toggleTheme() {
            const body = document.body;
            const themeBtn = document.querySelector('.theme-toggle');
            
            if (body.getAttribute('data-theme') === 'dark') {
                body.setAttribute('data-theme', 'light');
                themeBtn.innerHTML = '<i class="fas fa-sun"></i> Light';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeBtn.innerHTML = '<i class="fas fa-moon"></i> Dark';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.body.setAttribute('data-theme', savedTheme);
                const themeBtn = document.querySelector('.theme-toggle');
                themeBtn.innerHTML = savedTheme === 'dark' ? 
                    '<i class="fas fa-moon"></i> Dark' : 
                    '<i class="fas fa-sun"></i> Light';
            }
        });
    </script>
</body>
</html>