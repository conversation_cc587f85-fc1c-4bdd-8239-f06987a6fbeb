version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hexstrike-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-hexstrike_ai}
      POSTGRES_USER: ${POSTGRES_USER:-hexstrike}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_me}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - hexstrike-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-hexstrike} -d ${POSTGRES_DB:-hexstrike_ai}"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/run/postgresql

  # Redis Cache and Queue
  redis:
    image: redis:7-alpine
    container_name: hexstrike-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_change_me}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - hexstrike-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp

  # Main Chat Box Server
  chatbox-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hexstrike-chatbox
    environment:
      - FLASK_ENV=production
      - CHAT_BOX_PORT=3000
      - DATABASE_URL=postgresql://${POSTGRES_USER:-hexstrike}:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/${POSTGRES_DB:-hexstrike_ai}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-jwt_secret_change_me}
      - SECRET_KEY=${SECRET_KEY:-flask_secret_change_me}
      - PYTHONIOENCODING=utf-8
    ports:
      - "3000:3000"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - hexstrike-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/temp

  # Background Worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    container_name: hexstrike-worker
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-hexstrike}:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/${POSTGRES_DB:-hexstrike_ai}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379/0
      - WORKER_CONCURRENCY=2
      - WORKER_TIMEOUT=300
      - PYTHONIOENCODING=utf-8
    volumes:
      - ./logs:/app/logs
      - ./scans:/app/scans
    networks:
      - hexstrike-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    tmpfs:
      - /tmp
      - /app/temp

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: hexstrike-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - hexstrike-network
    depends_on:
      - chatbox-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /var/cache/nginx
      - /var/run

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  hexstrike-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16