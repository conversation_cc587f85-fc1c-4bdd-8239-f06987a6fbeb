<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Box AI - Advanced Interface</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --border-color: #3c3c3c;
            --text-primary: #d4d4d4;
            --text-secondary: #cccccc;
            --text-muted: #888;
            --accent-blue: #0078d4;
            --accent-green: #4caf50;
            --accent-orange: #ff9800;
            --accent-red: #f44336;
            --hover-bg: #4a4a4a;
        }

        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --border-color: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #495057;
            --text-muted: #6c757d;
            --hover-bg: #e9ecef;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }

        /* Login Screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .login-form {
            background: var(--bg-secondary);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            width: 400px;
            max-width: 90vw;
        }

        .login-title {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: var(--accent-blue);
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            margin-top: 1rem;
        }

        .login-btn:hover {
            background: #106ebe;
        }

        /* Main Layout */
        .app-container {
            display: flex;
            height: 100vh;
            flex-direction: column;
        }

        .app-header {
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            padding: 8px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-secondary);
        }

        .header-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--accent-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        /* Resizable Split View */
        .split-container {
            display: flex;
            width: 100%;
            height: 100%;
        }

        .chat-section {
            width: 50%;
            min-width: 300px;
            display: flex;
            flex-direction: column;
            background: var(--bg-secondary);
        }

        .resize-handle {
            width: 4px;
            background: var(--border-color);
            cursor: col-resize;
            position: relative;
        }

        .resize-handle:hover {
            background: var(--accent-blue);
        }

        .process-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-primary);
        }

        /* Tab System */
        .tab-container {
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            overflow-x: auto;
        }

        .tab {
            padding: 12px 16px;
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            font-size: 13px;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab.active {
            color: var(--text-primary);
            border-bottom-color: var(--accent-blue);
        }

        .tab:hover {
            color: var(--text-primary);
            background: var(--hover-bg);
        }

        .tab-close {
            margin-left: 8px;
            opacity: 0.6;
            font-size: 12px;
        }

        .tab-close:hover {
            opacity: 1;
            color: var(--accent-red);
        }

        .add-tab {
            padding: 12px;
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            font-size: 16px;
        }

        .add-tab:hover {
            color: var(--text-primary);
            background: var(--hover-bg);
        }

        /* Chat Interface */
        .chat-header {
            background: var(--bg-tertiary);
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-green);
            animation: pulse 2s infinite;
        }

        .status-dot.paused {
            background: var(--accent-orange);
            animation: none;
        }

        .status-dot.stopped {
            background: var(--accent-red);
            animation: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .role-selector {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.4;
            animation: slideIn 0.3s ease-out;
            position: relative;
        }

        .message.user {
            align-self: flex-end;
            background: var(--accent-blue);
            color: white;
        }

        .message.ai {
            align-self: flex-start;
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .message.system {
            align-self: center;
            background: var(--bg-tertiary);
            color: var(--text-muted);
            font-style: italic;
            max-width: 90%;
        }

        .message-actions {
            position: absolute;
            top: -8px;
            right: 8px;
            display: none;
            gap: 4px;
        }

        .message:hover .message-actions {
            display: flex;
        }

        .message-action {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-muted);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .message-action:hover {
            color: var(--text-primary);
            background: var(--hover-bg);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-input {
            background: var(--bg-tertiary);
            border-top: 1px solid var(--border-color);
            padding: 16px;
        }

        .input-container {
            display: flex;
            gap: 8px;
            align-items: flex-end;
        }

        .chat-textarea {
            flex: 1;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            color: var(--text-primary);
            font-size: 14px;
            resize: none;
            min-height: 40px;
            max-height: 120px;
        }

        .chat-textarea:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        .input-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .action-btn {
            background: var(--accent-blue);
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #106ebe;
        }

        .action-btn:disabled {
            background: var(--text-muted);
            cursor: not-allowed;
        }

        .action-btn.secondary {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .action-btn.secondary:hover {
            background: var(--hover-bg);
        }

        /* Process Panel */
        .process-tabs {
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            overflow-x: auto;
        }

        .process-content {
            flex: 1;
            overflow: hidden;
        }

        .tab-panel {
            height: 100%;
            display: none;
            flex-direction: column;
        }

        .tab-panel.active {
            display: flex;
        }

        /* Controls Panel */
        .controls-section {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .control-group {
            margin-bottom: 16px;
        }

        .control-label {
            display: block;
            margin-bottom: 8px;
            font-size: 13px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .control-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: var(--bg-secondary);
            color: var(--text-primary);
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .control-btn:hover {
            background: var(--hover-bg);
        }

        .control-btn.pause {
            border-color: var(--accent-orange);
            color: var(--accent-orange);
        }

        .control-btn.stop {
            border-color: var(--accent-red);
            color: var(--accent-red);
        }

        .control-btn.success {
            border-color: var(--accent-green);
            color: var(--accent-green);
        }

        /* Data Input Forms */
        .form-section {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-section h3 {
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .input-group {
            margin-bottom: 12px;
        }

        .input-label {
            display: block;
            margin-bottom: 4px;
            font-size: 12px;
            color: var(--text-muted);
        }

        .input-field {
            width: 100%;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 13px;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        .file-upload {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .file-upload:hover {
            border-color: var(--accent-blue);
            background: var(--bg-secondary);
        }

        .file-upload.dragover {
            border-color: var(--accent-green);
            background: rgba(76, 175, 80, 0.1);
        }

        /* Logs Panel */
        .logs-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 4px 8px;
            border-radius: 3px;
            white-space: pre-wrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .log-timestamp {
            color: var(--text-muted);
            font-size: 11px;
        }

        .log-entry.info {
            color: #4fc3f7;
        }

        .log-entry.warning {
            color: #ffb74d;
            background: rgba(255, 183, 77, 0.1);
        }

        .log-entry.error {
            color: #f48fb1;
            background: rgba(244, 143, 177, 0.1);
        }

        .log-entry.success {
            color: #81c784;
        }

        /* History Panel */
        .history-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .history-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .history-item:hover {
            background: var(--hover-bg);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .history-title {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .history-time {
            font-size: 12px;
            color: var(--text-muted);
        }

        .history-preview {
            font-size: 13px;
            color: var(--text-muted);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Queue Panel */
        .queue-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .queue-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .queue-item.running {
            border-color: var(--accent-blue);
        }

        .queue-item.completed {
            border-color: var(--accent-green);
            opacity: 0.7;
        }

        .queue-item.failed {
            border-color: var(--accent-red);
        }

        .queue-content {
            flex: 1;
        }

        .queue-title {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .queue-status {
            font-size: 12px;
            color: var(--text-muted);
        }

        .queue-actions {
            display: flex;
            gap: 4px;
        }

        .queue-action {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-muted);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .queue-action:hover {
            color: var(--text-primary);
            background: var(--hover-bg);
        }

        /* Metrics Dashboard */
        .metrics-container {
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .metric-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
        }

        .metric-title {
            font-size: 12px;
            color: var(--text-muted);
            margin-bottom: 8px;
            text-transform: uppercase;
        }

        .metric-value {
            font-size: 24px;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .metric-change {
            font-size: 12px;
            margin-top: 4px;
        }

        .metric-change.positive {
            color: var(--accent-green);
        }

        .metric-change.negative {
            color: var(--accent-red);
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            max-width: 300px;
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
        }

        .notification.success {
            border-color: var(--accent-green);
        }

        .notification.error {
            border-color: var(--accent-red);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Loading States */
        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--accent-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .split-container {
                flex-direction: column;
            }
            
            .chat-section {
                width: 100%;
                height: 50vh;
            }
            
            .process-section {
                height: 50vh;
            }
            
            .resize-handle {
                display: none;
            }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .flex {
            display: flex;
        }

        .flex-1 {
            flex: 1;
        }

        .gap-2 {
            gap: 8px;
        }

        .gap-4 {
            gap: 16px;
        }

        .text-center {
            text-align: center;
        }

        .text-sm {
            font-size: 12px;
        }

        .text-xs {
            font-size: 11px;
        }

        .font-mono {
            font-family: 'Consolas', 'Monaco', monospace;
        }

        .opacity-50 {
            opacity: 0.5;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .select-none {
            user-select: none;
        }
    </style>
</head>
<body data-theme="dark">
    <!-- Login Screen -->
    <div class="login-screen" id="loginScreen">
        <div class="login-form">
            <h2 class="login-title">Chat Box AI</h2>
            <div class="form-group">
                <label class="form-label">Username</label>
                <input type="text" class="form-input" id="loginUsername" placeholder="Enter username">
            </div>
            <div class="form-group">
                <label class="form-label">Password</label>
                <input type="password" class="form-input" id="loginPassword" placeholder="Enter password">
            </div>
            <button class="login-btn" id="loginBtn">Sign In</button>
            <div class="text-center" style="margin-top: 1rem; font-size: 12px; color: var(--text-muted);">
                Demo: admin / password
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container hidden" id="appContainer">
        <!-- Header -->
        <div class="app-header">
            <div class="app-title">
                <i class="fas fa-robot"></i> Chat Box AI - Advanced Interface
            </div>
            <div class="header-controls">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i> Dark
                </button>
                <div class="user-menu">
                    <div class="user-avatar" id="userAvatar">A</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="split-container">
                <!-- Chat Section -->
                <div class="chat-section" id="chatSection">
                    <!-- Chat Tabs -->
                    <div class="tab-container">
                        <button class="tab active" data-tab="chat-1">
                            <i class="fas fa-comments"></i>
                            <span>Chat 1</span>
                            <i class="fas fa-times tab-close"></i>
                        </button>
                        <button class="add-tab" id="addChatTab">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>

                    <!-- Chat Content -->
                    <div class="chat-tab-content">
                        <div class="chat-header">
                            <div class="chat-status">
                                <div class="status-dot" id="statusDot"></div>
                                <span id="statusText">Ready</span>
                            </div>
                            <select class="role-selector" id="roleSelector">
                                <option value="helper">Helper</option>
                                <option value="coder">Coder</option>
                                <option value="explainer">Explainer</option>
                                <option value="analyst">Security Analyst</option>
                            </select>
                        </div>
                        
                        <div class="chat-messages" id="chatMessages">
                            <div class="message system">Welcome to Chat Box AI! Select your AI role and start chatting.</div>
                        </div>
                        
                        <div class="chat-input">
                            <div class="input-container">
                                <textarea 
                                    class="chat-textarea" 
                                    id="messageInput" 
                                    placeholder="Type your message here..."
                                    rows="1"
                                ></textarea>
                                <div class="input-actions">
                                    <button class="action-btn" id="sendBtn">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    <button class="action-btn secondary" id="uploadBtn">
                                        <i class="fas fa-paperclip"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resize Handle -->
                <div class="resize-handle" id="resizeHandle"></div>

                <!-- Process Section -->
                <div class="process-section" id="processSection">
                    <!-- Process Tabs -->
                    <div class="process-tabs">
                        <button class="tab active" data-panel="controls">
                            <i class="fas fa-sliders-h"></i> Controls
                        </button>
                        <button class="tab" data-panel="data">
                            <i class="fas fa-database"></i> Data Input
                        </button>
                        <button class="tab" data-panel="logs">
                            <i class="fas fa-terminal"></i> Logs
                        </button>
                        <button class="tab" data-panel="history">
                            <i class="fas fa-history"></i> History
                        </button>
                        <button class="tab" data-panel="queue">
                            <i class="fas fa-list"></i> Queue
                        </button>
                        <button class="tab" data-panel="metrics">
                            <i class="fas fa-chart-bar"></i> Metrics
                        </button>
                    </div>
                    
                    <div class="process-content">
                        <!-- Controls Panel -->
                        <div class="tab-panel active" id="controlsPanel">
                            <div class="controls-section">
                                <div class="control-group">
                                    <label class="control-label">Process Controls</label>
                                    <div class="control-buttons">
                                        <button class="control-btn pause" id="pauseBtn">
                                            <i class="fas fa-pause"></i> Pause
                                        </button>
                                        <button class="control-btn stop" id="stopBtn">
                                            <i class="fas fa-stop"></i> Stop
                                        </button>
                                        <button class="control-btn success hidden" id="resumeBtn">
                                            <i class="fas fa-play"></i> Resume
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="control-group">
                                    <label class="control-label">Session Controls</label>
                                    <div class="control-buttons">
                                        <button class="control-btn success" id="newRequestBtn">
                                            <i class="fas fa-plus"></i> New Request
                                        </button>
                                        <button class="control-btn" id="changeRequestBtn">
                                            <i class="fas fa-edit"></i> Change Request
                                        </button>
                                        <button class="control-btn" id="retryBtn">
                                            <i class="fas fa-redo"></i> Retry
                                        </button>
                                    </div>
                                </div>

                                <div class="control-group">
                                    <label class="control-label">Export Options</label>
                                    <div class="control-buttons">
                                        <button class="control-btn" id="exportTxtBtn">
                                            <i class="fas fa-file-alt"></i> Export TXT
                                        </button>
                                        <button class="control-btn" id="exportJsonBtn">
                                            <i class="fas fa-file-code"></i> Export JSON
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Data Input Panel -->
                        <div class="tab-panel" id="dataPanel">
                            <div class="form-section">
                                <h3><i class="fas fa-user"></i> Authentication</h3>
                                <div class="input-group">
                                    <label class="input-label">Username</label>
                                    <input type="text" class="input-field" id="authUsername" placeholder="Optional username">
                                </div>
                                <div class="input-group">
                                    <label class="input-label">Password</label>
                                    <input type="password" class="input-field" id="authPassword" placeholder="Optional password">
                                </div>
                            </div>

                            <div class="form-section">
                                <h3><i class="fas fa-bullseye"></i> Target Configuration</h3>
                                <div class="input-group">
                                    <label class="input-label">Target URL/IP</label>
                                    <input type="text" class="input-field" id="targetInput" placeholder="https://example.com">
                                </div>
                                <div class="input-group">
                                    <label class="input-label">Port Range</label>
                                    <input type="text" class="input-field" id="portInput" placeholder="80,443,8080-8090">
                                </div>
                            </div>

                            <div class="form-section">
                                <h3><i class="fas fa-cog"></i> API Configuration</h3>
                                <div class="input-group">
                                    <label class="input-label">API Endpoint</label>
                                    <input type="text" class="input-field" id="apiEndpoint" placeholder="https://api.example.com">
                                </div>
                                <div class="input-group">
                                    <label class="input-label">API Key</label>
                                    <input type="password" class="input-field" id="apiKey" placeholder="Your API key">
                                </div>
                            </div>

                            <div class="form-section">
                                <h3><i class="fas fa-upload"></i> File Upload</h3>
                                <div class="file-upload" id="fileUpload">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 24px; margin-bottom: 8px;"></i>
                                    <div>Drop files here or click to browse</div>
                                    <div class="text-xs" style="margin-top: 4px;">Supports CSV, PDF, TXT, JSON</div>
                                    <input type="file" id="fileInput" multiple accept=".csv,.pdf,.txt,.json" style="display: none;">
                                </div>
                                <div id="uploadedFiles" class="text-sm" style="margin-top: 8px;"></div>
                            </div>

                            <div class="form-section">
                                <div class="control-buttons">
                                    <button class="control-btn success" id="saveConfigBtn">
                                        <i class="fas fa-save"></i> Save Configuration
                                    </button>
                                    <button class="control-btn" id="loadPresetBtn">
                                        <i class="fas fa-folder-open"></i> Load Preset
                                    </button>
                                    <button class="control-btn" id="clearConfigBtn">
                                        <i class="fas fa-trash"></i> Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Logs Panel -->
                        <div class="tab-panel" id="logsPanel">
                            <div class="logs-container" id="logsContainer">
                                <div class="log-entry info">
                                    <span class="log-timestamp">12:00:00</span>
                                    <span>[INFO] Chat Box AI initialized</span>
                                </div>
                                <div class="log-entry info">
                                    <span class="log-timestamp">12:00:01</span>
                                    <span>[INFO] System ready for requests</span>
                                </div>
                            </div>
                        </div>

                        <!-- History Panel -->
                        <div class="tab-panel" id="historyPanel">
                            <div class="history-container" id="historyContainer">
                                <!-- History items will be populated here -->
                            </div>
                        </div>

                        <!-- Queue Panel -->
                        <div class="tab-panel" id="queuePanel">
                            <div class="queue-container" id="queueContainer">
                                <!-- Queue items will be populated here -->
                            </div>
                        </div>

                        <!-- Metrics Panel -->
                        <div class="tab-panel" id="metricsPanel">
                            <div class="metrics-container">
                                <div class="metric-card">
                                    <div class="metric-title">Total Requests</div>
                                    <div class="metric-value" id="totalRequests">0</div>
                                    <div class="metric-change positive">+0 today</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-title">Success Rate</div>
                                    <div class="metric-value" id="successRate">100%</div>
                                    <div class="metric-change positive">+0% this week</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-title">Avg Response Time</div>
                                    <div class="metric-value" id="avgResponseTime">0ms</div>
                                    <div class="metric-change positive">-0ms vs last week</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-title">Active Sessions</div>
                                    <div class="metric-value" id="activeSessions">1</div>
                                    <div class="metric-change">Current</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ChatBoxAI {
            constructor() {
                this.currentUser = null;
                this.isProcessing = false;
                this.isPaused = false;
                this.currentTab = 'chat-1';
                this.chatTabs = new Map();
                this.requestHistory = [];
                this.requestQueue = [];
                this.metrics = {
                    totalRequests: 0,
                    successfulRequests: 0,
                    avgResponseTime: 0,
                    activeSessions: 1
                };
                this.theme = 'dark';
                this.autoLogoutTimer = null;
                
                this.initializeApp();
            }

            initializeApp() {
                this.bindEvents();
                this.loadSavedData();
                this.initializeResizer();
                this.startAutoLogoutTimer();
                
                // Initialize first chat tab
                this.chatTabs.set('chat-1', {
                    id: 'chat-1',
                    title: 'Chat 1',
                    messages: [],
                    role: 'helper'
                });
            }

            bindEvents() {
                // Login events
                document.getElementById('loginBtn').addEventListener('click', () => this.handleLogin());
                document.getElementById('loginPassword').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.handleLogin();
                });

                // Theme toggle
                document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

                // Chat events
                document.getElementById('sendBtn').addEventListener('click', () => this.sendMessage());
                document.getElementById('messageInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                document.getElementById('messageInput').addEventListener('input', () => this.autoResizeTextarea());
                document.getElementById('uploadBtn').addEventListener('click', () => this.handleFileUpload());
                document.getElementById('addChatTab').addEventListener('click', () => this.addChatTab());

                // Control events
                document.getElementById('pauseBtn').addEventListener('click', () => this.pauseProcess());
                document.getElementById('stopBtn').addEventListener('click', () => this.stopProcess());
                document.getElementById('resumeBtn').addEventListener('click', () => this.resumeProcess());
                document.getElementById('newRequestBtn').addEventListener('click', () => this.newRequest());
                document.getElementById('changeRequestBtn').addEventListener('click', () => this.changeRequest());
                document.getElementById('retryBtn').addEventListener('click', () => this.retryRequest());

                // Export events
                document.getElementById('exportTxtBtn').addEventListener('click', () => this.exportLogs('txt'));
                document.getElementById('exportJsonBtn').addEventListener('click', () => this.exportLogs('json'));

                // Data input events
                document.getElementById('saveConfigBtn').addEventListener('click', () => this.saveConfiguration());
                document.getElementById('loadPresetBtn').addEventListener('click', () => this.loadPreset());
                document.getElementById('clearConfigBtn').addEventListener('click', () => this.clearConfiguration());

                // File upload events
                const fileUpload = document.getElementById('fileUpload');
                const fileInput = document.getElementById('fileInput');
                
                fileUpload.addEventListener('click', () => fileInput.click());
                fileUpload.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    fileUpload.classList.add('dragover');
                });
                fileUpload.addEventListener('dragleave', () => {
                    fileUpload.classList.remove('dragover');
                });
                fileUpload.addEventListener('drop', (e) => {
                    e.preventDefault();
                    fileUpload.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });
                fileInput.addEventListener('change', (e) => {
                    this.handleFiles(e.target.files);
                });

                // Tab switching
                document.querySelectorAll('.process-tabs .tab').forEach(tab => {
                    tab.addEventListener('click', () => this.switchProcessTab(tab.dataset.panel));
                });

                // Activity tracking for auto-logout
                ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
                    document.addEventListener(event, () => this.resetAutoLogoutTimer(), true);
                });
            }

            handleLogin() {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;
                
                // Demo authentication
                if (username === 'admin' && password === 'password') {
                    this.currentUser = { username, loginTime: new Date() };
                    document.getElementById('loginScreen').classList.add('hidden');
                    document.getElementById('appContainer').classList.remove('hidden');
                    document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
                    this.addLog('User logged in successfully', 'success');
                    this.showNotification('Welcome to Chat Box AI!', 'success');
                } else {
                    this.showNotification('Invalid credentials. Try admin/password', 'error');
                }
            }

            toggleTheme() {
                this.theme = this.theme === 'dark' ? 'light' : 'dark';
                document.body.setAttribute('data-theme', this.theme);
                const themeBtn = document.getElementById('themeToggle');
                themeBtn.innerHTML = this.theme === 'dark' ? 
                    '<i class="fas fa-moon"></i> Dark' : 
                    '<i class="fas fa-sun"></i> Light';
                localStorage.setItem('theme', this.theme);
            }

            addChatTab() {
                const tabId = `chat-${Date.now()}`;
                const tabTitle = `Chat ${this.chatTabs.size + 1}`;
                
                this.chatTabs.set(tabId, {
                    id: tabId,
                    title: tabTitle,
                    messages: [],
                    role: 'helper'
                });
                
                this.renderChatTabs();
                this.switchChatTab(tabId);
            }

            renderChatTabs() {
                const container = document.querySelector('.tab-container');
                const addBtn = container.querySelector('.add-tab');
                
                // Clear existing tabs
                container.querySelectorAll('.tab:not(.add-tab)').forEach(tab => tab.remove());
                
                // Add tabs
                this.chatTabs.forEach((chat, id) => {
                    const tab = document.createElement('button');
                    tab.className = `tab ${id === this.currentTab ? 'active' : ''}`;
                    tab.dataset.tab = id;
                    tab.innerHTML = `
                        <i class="fas fa-comments"></i>
                        <span>${chat.title}</span>
                        <i class="fas fa-times tab-close"></i>
                    `;
                    
                    tab.addEventListener('click', (e) => {
                        if (e.target.classList.contains('tab-close')) {
                            this.closeChatTab(id);
                        } else {
                            this.switchChatTab(id);
                        }
                    });
                    
                    // Add null check to ensure addBtn exists and is a child of container
                    if (addBtn && addBtn.parentNode === container) {
                        container.insertBefore(tab, addBtn);
                    } else {
                        container.appendChild(tab);
                    }
                });
            }

            switchChatTab(tabId) {
                this.currentTab = tabId;
                this.renderChatTabs();
                this.renderChatMessages();
            }

            closeChatTab(tabId) {
                if (this.chatTabs.size <= 1) return; // Keep at least one tab
                
                this.chatTabs.delete(tabId);
                
                if (this.currentTab === tabId) {
                    this.currentTab = this.chatTabs.keys().next().value;
                }
                
                this.renderChatTabs();
                this.renderChatMessages();
            }

            renderChatMessages() {
                const container = document.getElementById('chatMessages');
                const currentChat = this.chatTabs.get(this.currentTab);
                
                container.innerHTML = '';
                
                if (currentChat.messages.length === 0) {
                    container.innerHTML = '<div class="message system">Welcome to Chat Box AI! How can I assist you today?</div>';
                } else {
                    currentChat.messages.forEach(msg => {
                        this.addMessageToDOM(msg.content, msg.type, false);
                    });
                }
            }

            autoResizeTextarea() {
                const textarea = document.getElementById('messageInput');
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }

            sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message || this.isProcessing) return;

                this.addMessage(message, 'user');
                input.value = '';
                this.autoResizeTextarea();
                
                this.processRequest(message);
            }

            addMessage(content, type, saveToHistory = true) {
                this.addMessageToDOM(content, type);
                
                if (saveToHistory) {
                    const currentChat = this.chatTabs.get(this.currentTab);
                    currentChat.messages.push({ content, type, timestamp: new Date() });
                }
            }

            addMessageToDOM(content, type, animate = true) {
                const container = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.innerHTML = `
                    ${content}
                    <div class="message-actions">
                        <button class="message-action" onclick="navigator.clipboard.writeText('${content.replace(/'/g, "\\'")}')">Copy</button>
                        <button class="message-action" onclick="this.closest('.message').remove()">Delete</button>
                    </div>
                `;
                
                if (!animate) {
                    messageDiv.style.animation = 'none';
                }
                
                container.appendChild(messageDiv);
                container.scrollTop = container.scrollHeight;
            }

            async processRequest(message) {
                this.isProcessing = true;
                this.updateStatus('Processing...', '');
                document.getElementById('sendBtn').disabled = true;
                
                const startTime = Date.now();
                this.addLog(`Processing request: ${message}`, 'info');
                
                // Add to queue
                const requestId = Date.now().toString();
                this.requestQueue.push({
                    id: requestId,
                    message,
                    status: 'running',
                    startTime
                });
                this.renderQueue();
                
                try {
                    await this.simulateAIResponse(message);
                    
                    // Update metrics
                    const responseTime = Date.now() - startTime;
                    this.metrics.totalRequests++;
                    this.metrics.successfulRequests++;
                    this.metrics.avgResponseTime = 
                        (this.metrics.avgResponseTime + responseTime) / 2;
                    
                    // Update queue
                    const queueItem = this.requestQueue.find(item => item.id === requestId);
                    if (queueItem) {
                        queueItem.status = 'completed';
                        queueItem.responseTime = responseTime;
                    }
                    
                    // Add to history
                    this.requestHistory.unshift({
                        id: requestId,
                        message,
                        timestamp: new Date(startTime),
                        responseTime,
                        status: 'completed'
                    });
                    
                    this.showNotification('Request completed successfully!', 'success');
                    
                } catch (error) {
                    this.addLog(`Error: ${error.message}`, 'error');
                    this.showNotification(`Error: ${error.message}`, 'error');
                    
                    // Update queue
                    const queueItem = this.requestQueue.find(item => item.id === requestId);
                    if (queueItem) {
                        queueItem.status = 'failed';
                        queueItem.error = error.message;
                    }
                } finally {
                    this.isProcessing = false;
                    this.updateStatus('Ready', '');
                    document.getElementById('sendBtn').disabled = false;
                    this.renderQueue();
                    this.renderHistory();
                    this.updateMetrics();
                }
            }

            async simulateAIResponse(userMessage) {
                const currentChat = this.chatTabs.get(this.currentTab);
                const role = document.getElementById('roleSelector').value;
                
                const responses = {
                    helper: [
                        "I understand your request. Let me help you with that...",
                        "Analyzing your requirements...",
                        "Gathering relevant information...",
                        "Processing your request...",
                        "Here's what I found based on your request."
                    ],
                    coder: [
                        "Analyzing the code structure...",
                        "Identifying potential improvements...",
                        "Generating optimized solution...",
                        "Testing the implementation...",
                        "Code analysis complete. Here are my recommendations."
                    ],
                    explainer: [
                        "Let me break this down for you...",
                        "Understanding the core concepts...",
                        "Organizing the explanation...",
                        "Adding relevant examples...",
                        "Here's a comprehensive explanation of your topic."
                    ],
                    analyst: [
                        "Initiating security assessment...",
                        "Scanning for vulnerabilities...",
                        "Analyzing threat vectors...",
                        "Generating security report...",
                        "Security analysis complete. Here are the findings."
                    ]
                };

                const roleResponses = responses[role] || responses.helper;

                for (let i = 0; i < roleResponses.length; i++) {
                    if (this.isPaused) {
                        await this.waitForResume();
                    }
                    
                    if (!this.isProcessing) break; // Stopped
                    
                    await this.delay(1000 + Math.random() * 2000);
                    this.addMessage(roleResponses[i], 'ai');
                    this.addLog(`Step ${i + 1}: ${roleResponses[i]}`, 'success');
                }
            }

            async waitForResume() {
                return new Promise(resolve => {
                    const checkResume = () => {
                        if (!this.isPaused) {
                            resolve();
                        } else {
                            setTimeout(checkResume, 100);
                        }
                    };
                    checkResume();
                });
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            pauseProcess() {
                if (!this.isProcessing) return;
                
                this.isPaused = true;
                this.updateStatus('Paused', 'paused');
                document.getElementById('pauseBtn').classList.add('hidden');
                document.getElementById('resumeBtn').classList.remove('hidden');
                this.addLog('Process paused by user', 'warning');
            }

            resumeProcess() {
                this.isPaused = false;
                this.updateStatus('Processing...', '');
                document.getElementById('pauseBtn').classList.remove('hidden');
                document.getElementById('resumeBtn').classList.add('hidden');
                this.addLog('Process resumed', 'info');
            }

            stopProcess() {
                this.isProcessing = false;
                this.isPaused = false;
                this.updateStatus('Stopped', 'stopped');
                document.getElementById('sendBtn').disabled = false;
                document.getElementById('pauseBtn').classList.remove('hidden');
                document.getElementById('resumeBtn').classList.add('hidden');
                this.addLog('Process stopped by user', 'error');
                
                setTimeout(() => {
                    this.updateStatus('Ready', '');
                }, 2000);
            }

            newRequest() {
                this.stopProcess();
                const currentChat = this.chatTabs.get(this.currentTab);
                currentChat.messages = [];
                this.renderChatMessages();
                this.addLog('New request session started', 'info');
            }

            changeRequest() {
                this.stopProcess();
                this.addMessage('Please provide your updated request.', 'system');
                this.addLog('Request change initiated', 'info');
            }

            retryRequest() {
                const currentChat = this.chatTabs.get(this.currentTab);
                const lastUserMessage = currentChat.messages
                    .slice()
                    .reverse()
                    .find(msg => msg.type === 'user');
                
                if (lastUserMessage) {
                    this.processRequest(lastUserMessage.content);
                    this.addLog('Retrying last request', 'info');
                }
            }

            updateStatus(status, dotClass = '') {
                document.getElementById('statusText').textContent = status;
                document.getElementById('statusDot').className = `status-dot ${dotClass}`;
            }

            addLog(message, type = 'info') {
                 const container = document.getElementById('logsContainer');
                 const logDiv = document.createElement('div');
                 logDiv.className = `log-entry ${type}`;
                 logDiv.innerHTML = `
                     <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
                     <span>[${type.toUpperCase()}] ${message}</span>
                 `;
                 
                 container.appendChild(logDiv);
                 container.scrollTop = container.scrollHeight;
             }

             switchProcessTab(panelName) {
                 // Update tab buttons
                 document.querySelectorAll('.process-tabs .tab').forEach(tab => {
                     tab.classList.toggle('active', tab.dataset.panel === panelName);
                 });
                 
                 // Update tab panels
                 document.querySelectorAll('.tab-panel').forEach(panel => {
                     panel.classList.toggle('active', panel.id === panelName + 'Panel');
                 });
             }

             handleFileUpload() {
                 document.getElementById('fileInput').click();
             }

             handleFiles(files) {
                 const uploadedFiles = document.getElementById('uploadedFiles');
                 uploadedFiles.innerHTML = '';
                 
                 Array.from(files).forEach(file => {
                     const fileDiv = document.createElement('div');
                     fileDiv.innerHTML = `
                         <i class="fas fa-file"></i> ${file.name} (${(file.size / 1024).toFixed(1)} KB)
                     `;
                     uploadedFiles.appendChild(fileDiv);
                 });
                 
                 this.addLog(`${files.length} file(s) uploaded`, 'success');
             }

             saveConfiguration() {
                 const config = {
                     username: document.getElementById('authUsername').value,
                     target: document.getElementById('targetInput').value,
                     ports: document.getElementById('portInput').value,
                     apiEndpoint: document.getElementById('apiEndpoint').value,
                     timestamp: new Date().toISOString()
                 };
                 
                 localStorage.setItem('chatBoxConfig', JSON.stringify(config));
                 this.addLog('Configuration saved successfully', 'success');
                 this.showNotification('Configuration saved!', 'success');
             }

             loadPreset() {
                 const saved = localStorage.getItem('chatBoxConfig');
                 if (saved) {
                     try {
                         const config = JSON.parse(saved);
                         document.getElementById('authUsername').value = config.username || '';
                         document.getElementById('targetInput').value = config.target || '';
                         document.getElementById('portInput').value = config.ports || '';
                         document.getElementById('apiEndpoint').value = config.apiEndpoint || '';
                         this.addLog('Configuration loaded', 'success');
                         this.showNotification('Configuration loaded!', 'success');
                     } catch (e) {
                         this.addLog('Error loading configuration', 'error');
                         this.showNotification('Error loading configuration', 'error');
                     }
                 } else {
                     this.showNotification('No saved configuration found', 'error');
                 }
             }

             clearConfiguration() {
                 document.getElementById('authUsername').value = '';
                 document.getElementById('authPassword').value = '';
                 document.getElementById('targetInput').value = '';
                 document.getElementById('portInput').value = '';
                 document.getElementById('apiEndpoint').value = '';
                 document.getElementById('apiKey').value = '';
                 document.getElementById('uploadedFiles').innerHTML = '';
                 
                 this.addLog('Configuration cleared', 'info');
                 this.showNotification('Configuration cleared', 'success');
             }

             exportLogs(format) {
                 const logs = Array.from(document.querySelectorAll('.log-entry')).map(entry => {
                     const timestamp = entry.querySelector('.log-timestamp').textContent;
                     const message = entry.textContent.replace(timestamp, '').trim();
                     return { timestamp, message };
                 });
                 
                 let content, filename, mimeType;
                 
                 if (format === 'txt') {
                     content = logs.map(log => `${log.timestamp} ${log.message}`).join('\n');
                     filename = `chat-logs-${new Date().toISOString().split('T')[0]}.txt`;
                     mimeType = 'text/plain';
                 } else {
                     content = JSON.stringify(logs, null, 2);
                     filename = `chat-logs-${new Date().toISOString().split('T')[0]}.json`;
                     mimeType = 'application/json';
                 }
                 
                 const blob = new Blob([content], { type: mimeType });
                 const url = URL.createObjectURL(blob);
                 const a = document.createElement('a');
                 a.href = url;
                 a.download = filename;
                 a.click();
                 URL.revokeObjectURL(url);
                 
                 this.addLog(`Logs exported as ${format.toUpperCase()}`, 'success');
                 this.showNotification(`Logs exported as ${filename}`, 'success');
             }

             renderQueue() {
                 const container = document.getElementById('queueContainer');
                 container.innerHTML = '';
                 
                 this.requestQueue.forEach(item => {
                     const queueDiv = document.createElement('div');
                     queueDiv.className = `queue-item ${item.status}`;
                     queueDiv.innerHTML = `
                         <div class="queue-content">
                             <div class="queue-title">${item.message.substring(0, 50)}...</div>
                             <div class="queue-status">${item.status} ${item.responseTime ? `(${item.responseTime}ms)` : ''}</div>
                         </div>
                         <div class="queue-actions">
                             <button class="queue-action" onclick="app.removeFromQueue('${item.id}')">Remove</button>
                         </div>
                     `;
                     container.appendChild(queueDiv);
                 });
             }

             renderHistory() {
                 const container = document.getElementById('historyContainer');
                 container.innerHTML = '';
                 
                 this.requestHistory.forEach(item => {
                     const historyDiv = document.createElement('div');
                     historyDiv.className = 'history-item';
                     historyDiv.innerHTML = `
                         <div class="history-header">
                             <div class="history-title">Request #${item.id.slice(-4)}</div>
                             <div class="history-time">${item.timestamp.toLocaleTimeString()}</div>
                         </div>
                         <div class="history-preview">${item.message}</div>
                     `;
                     historyDiv.addEventListener('click', () => {
                         this.processRequest(item.message);
                     });
                     container.appendChild(historyDiv);
                 });
             }

             updateMetrics() {
                 document.getElementById('totalRequests').textContent = this.metrics.totalRequests;
                 document.getElementById('successRate').textContent = 
                     this.metrics.totalRequests > 0 ? 
                     Math.round((this.metrics.successfulRequests / this.metrics.totalRequests) * 100) + '%' : '100%';
                 document.getElementById('avgResponseTime').textContent = Math.round(this.metrics.avgResponseTime) + 'ms';
                 document.getElementById('activeSessions').textContent = this.metrics.activeSessions;
             }

             removeFromQueue(id) {
                 this.requestQueue = this.requestQueue.filter(item => item.id !== id);
                 this.renderQueue();
                 this.addLog(`Request ${id.slice(-4)} removed from queue`, 'info');
             }

             showNotification(message, type = 'info') {
                 const notification = document.createElement('div');
                 notification.className = `notification ${type}`;
                 notification.innerHTML = `
                     <div style="display: flex; justify-content: space-between; align-items: center;">
                         <span>${message}</span>
                         <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 16px;">&times;</button>
                     </div>
                 `;
                 
                 document.body.appendChild(notification);
                 
                 setTimeout(() => {
                     if (notification.parentElement) {
                         notification.remove();
                     }
                 }, 5000);
             }

             initializeResizer() {
                 const resizeHandle = document.getElementById('resizeHandle');
                 const chatSection = document.getElementById('chatSection');
                 const processSection = document.getElementById('processSection');
                 let isResizing = false;
                 
                 resizeHandle.addEventListener('mousedown', (e) => {
                     isResizing = true;
                     document.addEventListener('mousemove', handleResize);
                     document.addEventListener('mouseup', stopResize);
                 });
                 
                 function handleResize(e) {
                     if (!isResizing) return;
                     
                     const containerWidth = document.querySelector('.split-container').offsetWidth;
                     const newChatWidth = (e.clientX / containerWidth) * 100;
                     
                     if (newChatWidth >= 20 && newChatWidth <= 80) {
                         chatSection.style.width = newChatWidth + '%';
                     }
                 }
                 
                 function stopResize() {
                     isResizing = false;
                     document.removeEventListener('mousemove', handleResize);
                     document.removeEventListener('mouseup', stopResize);
                 }
             }

             startAutoLogoutTimer() {
                 this.resetAutoLogoutTimer();
             }

             resetAutoLogoutTimer() {
                 if (this.autoLogoutTimer) {
                     clearTimeout(this.autoLogoutTimer);
                 }
                 
                 // Auto logout after 30 minutes of inactivity
                 this.autoLogoutTimer = setTimeout(() => {
                     this.logout();
                 }, 30 * 60 * 1000);
             }

             logout() {
                 this.currentUser = null;
                 document.getElementById('appContainer').classList.add('hidden');
                 document.getElementById('loginScreen').classList.remove('hidden');
                 document.getElementById('loginUsername').value = '';
                 document.getElementById('loginPassword').value = '';
                 this.showNotification('Session expired. Please log in again.', 'error');
             }

             loadSavedData() {
                 // Load theme preference
                 const savedTheme = localStorage.getItem('theme');
                 if (savedTheme) {
                     this.theme = savedTheme;
                     document.body.setAttribute('data-theme', this.theme);
                     const themeBtn = document.getElementById('themeToggle');
                     themeBtn.innerHTML = this.theme === 'dark' ? 
                         '<i class="fas fa-moon"></i> Dark' : 
                         '<i class="fas fa-sun"></i> Light';
                 }
             }
         }

         // Initialize the application
         let app;
         document.addEventListener('DOMContentLoaded', () => {
             app = new ChatBoxAI();
         });
     </script>
 </body>
 </html>