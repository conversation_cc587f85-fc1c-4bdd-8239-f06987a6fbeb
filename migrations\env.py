#!/usr/bin/env python3
"""
Alembic Environment Configuration for HexStrike AI
"""

from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# Import your models
from models import db
from app_factory import create_app

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = db.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_url():
    """Get database URL from environment or config"""
    # Try environment variable first (for different environments)
    url = os.getenv('DATABASE_URL')
    if url:
        return url
    
    # Fall back to config file
    return config.get_main_option("sqlalchemy.url")


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Create Flask app to get proper configuration
    app = create_app()
    
    with app.app_context():
        # Override the sqlalchemy.url with the one from Flask config
        config.set_main_option('sqlalchemy.url', app.config['SQLALCHEMY_DATABASE_URI'])
        
        connectable = engine_from_config(
            config.get_section(config.config_ini_section, {}),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )

        with connectable.connect() as connection:
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                compare_type=True,
                compare_server_default=True,
                # Include schemas if using multiple schemas
                include_schemas=True,
                # Render item for autogenerate
                render_item=render_item,
                # Process revision directives
                process_revision_directives=process_revision_directives,
            )

            with context.begin_transaction():
                context.run_migrations()


def render_item(type_, obj, autogen_context):
    """Apply custom rendering for certain schema items"""
    # Import at function level to avoid circular imports
    from alembic.autogenerate import render
    
    if type_ == 'type' and hasattr(obj, 'python_type'):
        # Handle UUID type rendering
        if obj.python_type.__name__ == 'UUID':
            return "sa.dialects.postgresql.UUID(as_uuid=True)"
        # Handle JSONB type rendering
        elif obj.python_type.__name__ == 'dict':
            return "sa.dialects.postgresql.JSONB()"
    
    # Default rendering
    return render._render_type(type_, obj, autogen_context)


def process_revision_directives(context, revision, directives):
    """Process revision directives for custom behavior"""
    # Add custom processing if needed
    # For example, you could modify the migration script here
    pass


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()