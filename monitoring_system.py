#!/usr/bin/env python3
"""
HexStrike AI - Production Monitoring System
Real-time monitoring, alerting, and health checks
"""

import os
import time
import json
import psutil
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from threading import Thread, Event
import logging

@dataclass
class HealthMetrics:
    """System health metrics"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    active_connections: int
    response_time_ms: float
    error_rate: float
    uptime_seconds: float
    status: str  # healthy, warning, critical

@dataclass
class Alert:
    """Alert definition"""
    id: str
    severity: str  # info, warning, critical
    title: str
    description: str
    timestamp: str
    metrics: Dict[str, Any]
    resolved: bool = False

class SystemMonitor:
    """System resource monitoring"""
    
    def __init__(self):
        self.start_time = time.time()
        self.logger = logging.getLogger('monitoring')
    
    def get_cpu_usage(self) -> float:
        """Get CPU usage percentage"""
        return psutil.cpu_percent(interval=1)
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage information"""
        memory = psutil.virtual_memory()
        return {
            'percent': memory.percent,
            'available_gb': memory.available / (1024**3),
            'used_gb': memory.used / (1024**3),
            'total_gb': memory.total / (1024**3)
        }
    
    def get_disk_usage(self, path: str = '/') -> Dict[str, float]:
        """Get disk usage information"""
        if os.name == 'nt':  # Windows
            path = 'C:\\'
        
        disk = psutil.disk_usage(path)
        return {
            'percent': (disk.used / disk.total) * 100,
            'free_gb': disk.free / (1024**3),
            'used_gb': disk.used / (1024**3),
            'total_gb': disk.total / (1024**3)
        }
    
    def get_network_connections(self) -> int:
        """Get number of active network connections"""
        try:
            connections = psutil.net_connections(kind='inet')
            return len([conn for conn in connections if conn.status == 'ESTABLISHED'])
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            return 0
    
    def get_uptime(self) -> float:
        """Get system uptime in seconds"""
        return time.time() - self.start_time

class ApplicationMonitor:
    """Application-specific monitoring"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.logger = logging.getLogger('monitoring')
        self.error_count = 0
        self.request_count = 0
        self.response_times = []
    
    def check_health_endpoint(self) -> Dict[str, Any]:
        """Check application health endpoint"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=10)
            response_time = (time.time() - start_time) * 1000
            
            self.request_count += 1
            self.response_times.append(response_time)
            
            # Keep only last 100 response times
            if len(self.response_times) > 100:
                self.response_times = self.response_times[-100:]
            
            if response.status_code == 200:
                return {
                    'status': 'healthy',
                    'response_time_ms': response_time,
                    'status_code': response.status_code
                }
            else:
                self.error_count += 1
                return {
                    'status': 'unhealthy',
                    'response_time_ms': response_time,
                    'status_code': response.status_code,
                    'error': f"HTTP {response.status_code}"
                }
        
        except Exception as e:
            self.error_count += 1
            return {
                'status': 'unhealthy',
                'response_time_ms': 0,
                'error': str(e)
            }
    
    def get_error_rate(self) -> float:
        """Calculate error rate percentage"""
        if self.request_count == 0:
            return 0.0
        return (self.error_count / self.request_count) * 100
    
    def get_avg_response_time(self) -> float:
        """Get average response time"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

class AlertManager:
    """Alert management and notification system"""
    
    def __init__(self):
        self.alerts: List[Alert] = []
        self.thresholds = {
            'cpu_critical': 90.0,
            'cpu_warning': 75.0,
            'memory_critical': 90.0,
            'memory_warning': 75.0,
            'disk_critical': 90.0,
            'disk_warning': 80.0,
            'response_time_critical': 5000.0,  # 5 seconds
            'response_time_warning': 2000.0,   # 2 seconds
            'error_rate_critical': 10.0,       # 10%
            'error_rate_warning': 5.0          # 5%
        }
        self.logger = logging.getLogger('monitoring')
    
    def check_thresholds(self, metrics: HealthMetrics) -> List[Alert]:
        """Check metrics against thresholds and generate alerts"""
        new_alerts = []
        
        # CPU alerts
        if metrics.cpu_percent >= self.thresholds['cpu_critical']:
            alert = Alert(
                id=f"cpu_critical_{int(time.time())}",
                severity="critical",
                title="Critical CPU Usage",
                description=f"CPU usage is {metrics.cpu_percent:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'cpu_percent': metrics.cpu_percent}
            )
            new_alerts.append(alert)
        elif metrics.cpu_percent >= self.thresholds['cpu_warning']:
            alert = Alert(
                id=f"cpu_warning_{int(time.time())}",
                severity="warning",
                title="High CPU Usage",
                description=f"CPU usage is {metrics.cpu_percent:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'cpu_percent': metrics.cpu_percent}
            )
            new_alerts.append(alert)
        
        # Memory alerts
        if metrics.memory_percent >= self.thresholds['memory_critical']:
            alert = Alert(
                id=f"memory_critical_{int(time.time())}",
                severity="critical",
                title="Critical Memory Usage",
                description=f"Memory usage is {metrics.memory_percent:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'memory_percent': metrics.memory_percent}
            )
            new_alerts.append(alert)
        elif metrics.memory_percent >= self.thresholds['memory_warning']:
            alert = Alert(
                id=f"memory_warning_{int(time.time())}",
                severity="warning",
                title="High Memory Usage",
                description=f"Memory usage is {metrics.memory_percent:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'memory_percent': metrics.memory_percent}
            )
            new_alerts.append(alert)
        
        # Disk alerts
        if metrics.disk_percent >= self.thresholds['disk_critical']:
            alert = Alert(
                id=f"disk_critical_{int(time.time())}",
                severity="critical",
                title="Critical Disk Usage",
                description=f"Disk usage is {metrics.disk_percent:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'disk_percent': metrics.disk_percent}
            )
            new_alerts.append(alert)
        elif metrics.disk_percent >= self.thresholds['disk_warning']:
            alert = Alert(
                id=f"disk_warning_{int(time.time())}",
                severity="warning",
                title="High Disk Usage",
                description=f"Disk usage is {metrics.disk_percent:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'disk_percent': metrics.disk_percent}
            )
            new_alerts.append(alert)
        
        # Response time alerts
        if metrics.response_time_ms >= self.thresholds['response_time_critical']:
            alert = Alert(
                id=f"response_time_critical_{int(time.time())}",
                severity="critical",
                title="Critical Response Time",
                description=f"Response time is {metrics.response_time_ms:.1f}ms",
                timestamp=metrics.timestamp,
                metrics={'response_time_ms': metrics.response_time_ms}
            )
            new_alerts.append(alert)
        elif metrics.response_time_ms >= self.thresholds['response_time_warning']:
            alert = Alert(
                id=f"response_time_warning_{int(time.time())}",
                severity="warning",
                title="High Response Time",
                description=f"Response time is {metrics.response_time_ms:.1f}ms",
                timestamp=metrics.timestamp,
                metrics={'response_time_ms': metrics.response_time_ms}
            )
            new_alerts.append(alert)
        
        # Error rate alerts
        if metrics.error_rate >= self.thresholds['error_rate_critical']:
            alert = Alert(
                id=f"error_rate_critical_{int(time.time())}",
                severity="critical",
                title="Critical Error Rate",
                description=f"Error rate is {metrics.error_rate:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'error_rate': metrics.error_rate}
            )
            new_alerts.append(alert)
        elif metrics.error_rate >= self.thresholds['error_rate_warning']:
            alert = Alert(
                id=f"error_rate_warning_{int(time.time())}",
                severity="warning",
                title="High Error Rate",
                description=f"Error rate is {metrics.error_rate:.1f}%",
                timestamp=metrics.timestamp,
                metrics={'error_rate': metrics.error_rate}
            )
            new_alerts.append(alert)
        
        # Add new alerts
        self.alerts.extend(new_alerts)
        
        # Log alerts
        for alert in new_alerts:
            if alert.severity == 'critical':
                self.logger.critical(f"ALERT: {alert.title} - {alert.description}")
            elif alert.severity == 'warning':
                self.logger.warning(f"ALERT: {alert.title} - {alert.description}")
            else:
                self.logger.info(f"ALERT: {alert.title} - {alert.description}")
        
        return new_alerts
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active (unresolved) alerts"""
        return [alert for alert in self.alerts if not alert.resolved]
    
    def resolve_alert(self, alert_id: str):
        """Mark an alert as resolved"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                self.logger.info(f"Alert resolved: {alert.title}")
                break

class MonitoringDashboard:
    """Monitoring dashboard and reporting"""
    
    def __init__(self, system_monitor: SystemMonitor, app_monitor: ApplicationMonitor, 
                 alert_manager: AlertManager):
        self.system_monitor = system_monitor
        self.app_monitor = app_monitor
        self.alert_manager = alert_manager
        self.metrics_history: List[HealthMetrics] = []
    
    def collect_metrics(self) -> HealthMetrics:
        """Collect current system and application metrics"""
        
        # System metrics
        cpu_percent = self.system_monitor.get_cpu_usage()
        memory_info = self.system_monitor.get_memory_usage()
        disk_info = self.system_monitor.get_disk_usage()
        connections = self.system_monitor.get_network_connections()
        uptime = self.system_monitor.get_uptime()
        
        # Application metrics
        health_check = self.app_monitor.check_health_endpoint()
        error_rate = self.app_monitor.get_error_rate()
        avg_response_time = self.app_monitor.get_avg_response_time()
        
        # Determine overall status
        status = 'healthy'
        if (cpu_percent > 90 or memory_info['percent'] > 90 or 
            disk_info['percent'] > 90 or error_rate > 10):
            status = 'critical'
        elif (cpu_percent > 75 or memory_info['percent'] > 75 or 
              disk_info['percent'] > 80 or error_rate > 5):
            status = 'warning'
        
        metrics = HealthMetrics(
            timestamp=datetime.utcnow().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory_info['percent'],
            disk_percent=disk_info['percent'],
            active_connections=connections,
            response_time_ms=avg_response_time,
            error_rate=error_rate,
            uptime_seconds=uptime,
            status=status
        )
        
        # Store metrics history (keep last 1000 entries)
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
        
        return metrics
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate monitoring report"""
        current_metrics = self.collect_metrics()
        active_alerts = self.alert_manager.get_active_alerts()
        
        # Calculate trends (last 10 metrics)
        recent_metrics = self.metrics_history[-10:] if len(self.metrics_history) >= 10 else self.metrics_history
        
        trends = {}
        if len(recent_metrics) > 1:
            trends = {
                'cpu_trend': recent_metrics[-1].cpu_percent - recent_metrics[0].cpu_percent,
                'memory_trend': recent_metrics[-1].memory_percent - recent_metrics[0].memory_percent,
                'response_time_trend': recent_metrics[-1].response_time_ms - recent_metrics[0].response_time_ms
            }
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'current_metrics': asdict(current_metrics),
            'active_alerts': [asdict(alert) for alert in active_alerts],
            'trends': trends,
            'summary': {
                'total_alerts': len(self.alert_manager.alerts),
                'active_alerts': len(active_alerts),
                'uptime_hours': current_metrics.uptime_seconds / 3600,
                'overall_status': current_metrics.status
            }
        }
    
    def save_report(self, filename: str = None):
        """Save monitoring report to file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"monitoring_report_{timestamp}.json"
        
        report = self.generate_report()
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return filename

class MonitoringService:
    """Main monitoring service"""
    
    def __init__(self, app_url: str, check_interval: int = 60):
        self.app_url = app_url
        self.check_interval = check_interval
        self.running = False
        self.stop_event = Event()
        
        # Initialize components
        self.system_monitor = SystemMonitor()
        self.app_monitor = ApplicationMonitor(app_url)
        self.alert_manager = AlertManager()
        self.dashboard = MonitoringDashboard(
            self.system_monitor, self.app_monitor, self.alert_manager
        )
        
        self.logger = logging.getLogger('monitoring')
    
    def start(self):
        """Start monitoring service"""
        self.running = True
        self.logger.info(f"Starting monitoring service for {self.app_url}")
        
        monitor_thread = Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        return monitor_thread
    
    def stop(self):
        """Stop monitoring service"""
        self.running = False
        self.stop_event.set()
        self.logger.info("Monitoring service stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running and not self.stop_event.is_set():
            try:
                # Collect metrics
                metrics = self.dashboard.collect_metrics()
                
                # Check for alerts
                new_alerts = self.alert_manager.check_thresholds(metrics)
                
                # Log current status
                self.logger.info(
                    f"System Status: {metrics.status} | "
                    f"CPU: {metrics.cpu_percent:.1f}% | "
                    f"Memory: {metrics.memory_percent:.1f}% | "
                    f"Response: {metrics.response_time_ms:.1f}ms | "
                    f"Errors: {metrics.error_rate:.1f}%"
                )
                
                # Wait for next check
                self.stop_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                self.stop_event.wait(10)  # Wait 10 seconds before retrying
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return self.dashboard.generate_report()

def main():
    """Main function for testing monitoring system"""
    import logging
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Start monitoring service
    app_url = os.getenv('APP_URL', 'http://localhost:8888')
    monitoring = MonitoringService(app_url, check_interval=30)
    
    try:
        print(f"Starting monitoring for {app_url}...")
        monitor_thread = monitoring.start()
        
        # Run for a few minutes for testing
        time.sleep(180)  # 3 minutes
        
        # Generate and save report
        report_file = monitoring.dashboard.save_report()
        print(f"Monitoring report saved to: {report_file}")
        
    except KeyboardInterrupt:
        print("\nStopping monitoring service...")
    finally:
        monitoring.stop()

if __name__ == '__main__':
    main()