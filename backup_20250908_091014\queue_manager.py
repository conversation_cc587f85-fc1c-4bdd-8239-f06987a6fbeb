#!/usr/bin/env python3
"""
HexStrike AI - Queue Manager
Utilities for managing Redis queues and job submission
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

import redis
from rq import Queue, Job as RQJob
from flask import current_app

from models import db, Job, User
from security_middleware import audit_log
from worker import execute_job, cleanup_old_jobs

# ============================================================================
# QUEUE CONFIGURATION
# ============================================================================

class QueueConfig:
    """Configuration for queue management"""
    
    # Queue priorities
    QUEUE_PRIORITIES = {
        'critical': 'high',
        'high': 'high',
        'medium': 'default',
        'low': 'low'
    }
    
    # Job timeouts (seconds)
    JOB_TIMEOUTS = {
        'security_scan': 300,      # 5 minutes
        'vulnerability_scan': 600,  # 10 minutes
        'malware_analysis': 180,   # 3 minutes
        'custom_script': 120       # 2 minutes
    }
    
    # Maximum concurrent jobs per user
    MAX_CONCURRENT_JOBS = {
        'admin': 10,
        'analyst': 5,
        'guest': 2
    }
    
    # Rate limits (jobs per hour)
    RATE_LIMITS = {
        'admin': 100,
        'analyst': 50,
        'guest': 10
    }

# ============================================================================
# QUEUE MANAGER CLASS
# ============================================================================

class QueueManager:
    """Manages job queues and execution"""
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or current_app.config.get('REDIS_URL', 'redis://localhost:6379/0')
        self.redis_conn = redis.from_url(self.redis_url)
        
        # Initialize queues
        self.high_queue = Queue('high', connection=self.redis_conn)
        self.default_queue = Queue('default', connection=self.redis_conn)
        self.low_queue = Queue('low', connection=self.redis_conn)
        
        self.queues = {
            'high': self.high_queue,
            'default': self.default_queue,
            'low': self.low_queue
        }
    
    def submit_job(self, user_id: str, job_type: str, tool_name: str, 
                   parameters: Dict[str, Any], priority: str = 'medium') -> str:
        """Submit a new job to the queue"""
        # Validate user
        user = User.query.get(user_id)
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        # Check rate limits
        if not self._check_rate_limit(user):
            raise ValueError(f"Rate limit exceeded for user {user.username}")
        
        # Check concurrent job limits
        if not self._check_concurrent_limit(user):
            raise ValueError(f"Concurrent job limit exceeded for user {user.username}")
        
        # Validate job parameters
        self._validate_job_parameters(job_type, tool_name, parameters)
        
        # Create job record
        job = Job(
            user_id=user_id,
            job_type=job_type,
            tool_name=tool_name,
            parameters=parameters,
            priority=priority,
            timeout_seconds=QueueConfig.JOB_TIMEOUTS.get(job_type, 120)
        )
        
        db.session.add(job)
        db.session.commit()
        
        # Submit to appropriate queue
        queue_name = QueueConfig.QUEUE_PRIORITIES.get(priority, 'default')
        queue = self.queues[queue_name]
        
        rq_job = queue.enqueue(
            execute_job,
            str(job.id),
            job_timeout=job.timeout_seconds,
            result_ttl=3600,  # Keep results for 1 hour
            failure_ttl=86400  # Keep failures for 24 hours
        )
        
        # Update job with RQ job ID
        job.worker_id = rq_job.id
        job.queue_name = queue_name
        db.session.commit()
        
        # Log job submission
        audit_log(
            action='job_submitted',
            user_id=user_id,
            resource_type='job',
            resource_id=str(job.id),
            outcome='success',
            details={
                'job_type': job_type,
                'tool_name': tool_name,
                'priority': priority,
                'queue': queue_name
            }
        )
        
        return str(job.id)
    
    def cancel_job(self, job_id: str, user_id: str) -> bool:
        """Cancel a pending or running job"""
        job = Job.query.get(job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")
        
        # Check permissions
        user = User.query.get(user_id)
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        if str(job.user_id) != user_id and user.role != 'admin':
            raise ValueError("Insufficient permissions to cancel job")
        
        # Can only cancel pending or running jobs
        if job.status not in ['pending', 'running']:
            raise ValueError(f"Cannot cancel job with status: {job.status}")
        
        # Cancel RQ job if it exists
        if job.worker_id:
            try:
                rq_job = RQJob.fetch(job.worker_id, connection=self.redis_conn)
                rq_job.cancel()
            except Exception as e:
                current_app.logger.warning(f"Failed to cancel RQ job {job.worker_id}: {e}")
        
        # Update job status
        job.status = 'cancelled'
        job.completed_at = datetime.utcnow()
        job.error_message = f"Cancelled by user {user.username}"
        db.session.commit()
        
        # Log cancellation
        audit_log(
            action='job_cancelled',
            user_id=user_id,
            resource_type='job',
            resource_id=str(job.id),
            outcome='success',
            details={'cancelled_by': user.username}
        )
        
        return True
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get detailed status of a job"""
        job = Job.query.get(job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")
        
        status = job.to_dict()
        
        # Add RQ job information if available
        if job.worker_id:
            try:
                rq_job = RQJob.fetch(job.worker_id, connection=self.redis_conn)
                status['rq_status'] = rq_job.get_status()
                status['rq_position'] = rq_job.get_position()
                status['rq_started_at'] = rq_job.started_at.isoformat() if rq_job.started_at else None
                status['rq_ended_at'] = rq_job.ended_at.isoformat() if rq_job.ended_at else None
            except Exception as e:
                current_app.logger.warning(f"Failed to fetch RQ job {job.worker_id}: {e}")
                status['rq_status'] = 'unknown'
        
        return status
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues"""
        stats = {}
        
        for name, queue in self.queues.items():
            stats[name] = {
                'length': len(queue),
                'started_jobs': queue.started_job_registry.count,
                'finished_jobs': queue.finished_job_registry.count,
                'failed_jobs': queue.failed_job_registry.count,
                'deferred_jobs': queue.deferred_job_registry.count
            }
        
        # Add database statistics
        stats['database'] = {
            'total_jobs': Job.query.count(),
            'pending_jobs': Job.query.filter_by(status='pending').count(),
            'running_jobs': Job.query.filter_by(status='running').count(),
            'completed_jobs': Job.query.filter_by(status='completed').count(),
            'failed_jobs': Job.query.filter_by(status='failed').count()
        }
        
        return stats
    
    def get_user_jobs(self, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """Get jobs for a specific user"""
        jobs = Job.query.filter_by(user_id=user_id)\
                       .order_by(Job.created_at.desc())\
                       .limit(limit)\
                       .offset(offset)\
                       .all()
        
        return [job.to_dict() for job in jobs]
    
    def cleanup_completed_jobs(self, days: int = 7) -> int:
        """Clean up completed jobs older than specified days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        deleted_count = Job.query.filter(
            Job.completed_at < cutoff_date,
            Job.status.in_(['completed', 'failed', 'timeout', 'cancelled'])
        ).delete()
        
        db.session.commit()
        
        # Also clean up RQ registries
        for queue in self.queues.values():
            queue.finished_job_registry.cleanup()
            queue.failed_job_registry.cleanup()
        
        return deleted_count
    
    def _check_rate_limit(self, user: User) -> bool:
        """Check if user has exceeded rate limit"""
        limit = QueueConfig.RATE_LIMITS.get(user.role, 10)
        
        # Count jobs in the last hour
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        recent_jobs = Job.query.filter(
            Job.user_id == user.id,
            Job.created_at >= one_hour_ago
        ).count()
        
        return recent_jobs < limit
    
    def _check_concurrent_limit(self, user: User) -> bool:
        """Check if user has exceeded concurrent job limit"""
        limit = QueueConfig.MAX_CONCURRENT_JOBS.get(user.role, 2)
        
        # Count running jobs
        running_jobs = Job.query.filter(
            Job.user_id == user.id,
            Job.status.in_(['pending', 'running'])
        ).count()
        
        return running_jobs < limit
    
    def _validate_job_parameters(self, job_type: str, tool_name: str, parameters: Dict[str, Any]) -> None:
        """Validate job parameters"""
        # Check job type
        valid_job_types = ['security_scan', 'vulnerability_scan', 'malware_analysis', 'custom_script']
        if job_type not in valid_job_types:
            raise ValueError(f"Invalid job type: {job_type}")
        
        # Check tool name based on job type
        valid_tools = {
            'security_scan': ['nmap', 'nikto', 'whatweb', 'sslyze'],
            'vulnerability_scan': ['sqlmap', 'dirb', 'gobuster'],
            'malware_analysis': ['yara', 'strings', 'file'],
            'custom_script': ['python3']
        }
        
        if tool_name not in valid_tools.get(job_type, []):
            raise ValueError(f"Invalid tool '{tool_name}' for job type '{job_type}'")
        
        # Validate required parameters
        if job_type in ['security_scan', 'vulnerability_scan']:
            if 'target' not in parameters:
                raise ValueError("Target parameter is required")
            
            target = parameters['target']
            if not target or len(target) > 500:
                raise ValueError("Invalid target parameter")
        
        elif job_type == 'malware_analysis':
            if 'file_path' not in parameters:
                raise ValueError("File path parameter is required")
        
        # Validate parameter sizes
        for key, value in parameters.items():
            if isinstance(value, str) and len(value) > 1000:
                raise ValueError(f"Parameter '{key}' is too long")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_queue_manager() -> QueueManager:
    """Get a queue manager instance"""
    return QueueManager()

def submit_security_scan(user_id: str, tool_name: str, target: str, 
                        options: Dict[str, Any] = None, priority: str = 'medium') -> str:
    """Convenience function to submit a security scan job"""
    qm = get_queue_manager()
    
    parameters = {'target': target}
    if options:
        parameters.update(options)
    
    return qm.submit_job(
        user_id=user_id,
        job_type='security_scan',
        tool_name=tool_name,
        parameters=parameters,
        priority=priority
    )

def submit_vulnerability_scan(user_id: str, tool_name: str, target: str,
                             options: Dict[str, Any] = None, priority: str = 'medium') -> str:
    """Convenience function to submit a vulnerability scan job"""
    qm = get_queue_manager()
    
    parameters = {'target': target}
    if options:
        parameters.update(options)
    
    return qm.submit_job(
        user_id=user_id,
        job_type='vulnerability_scan',
        tool_name=tool_name,
        parameters=parameters,
        priority=priority
    )

def submit_malware_analysis(user_id: str, tool_name: str, file_path: str,
                           options: Dict[str, Any] = None, priority: str = 'medium') -> str:
    """Convenience function to submit a malware analysis job"""
    qm = get_queue_manager()
    
    parameters = {'file_path': file_path}
    if options:
        parameters.update(options)
    
    return qm.submit_job(
        user_id=user_id,
        job_type='malware_analysis',
        tool_name=tool_name,
        parameters=parameters,
        priority=priority
    )

def schedule_cleanup_job():
    """Schedule periodic cleanup of old jobs"""
    qm = get_queue_manager()
    
    # Schedule cleanup to run every day
    qm.default_queue.enqueue(
        cleanup_old_jobs,
        job_timeout=300,
        result_ttl=0  # Don't keep results
    )