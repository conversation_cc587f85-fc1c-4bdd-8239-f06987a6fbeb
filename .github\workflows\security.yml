name: Security Monitoring

on:
  schedule:
    # Run daily at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:
  push:
    paths:
      - 'requirements*.txt'
      - 'package*.json'
      - 'Dockerfile*'
      - '.github/workflows/security.yml'

env:
  PYTHON_VERSION: '3.11'

jobs:
  # Dependency Vulnerability Scanning
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install safety pip-audit
          pip install -r requirements.txt
          pip install -r requirements-worker.txt

      - name: Run Safety check
        run: |
          safety check --json --output safety-report.json || true
          safety check --short-report
        continue-on-error: true

      - name: Run pip-audit
        run: |
          pip-audit --format=json --output=pip-audit-report.json || true
          pip-audit --format=cyclonedx-json --output=sbom.json || true
        continue-on-error: true

      - name: Upload vulnerability reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: dependency-vulnerability-reports
          path: |
            safety-report.json
            pip-audit-report.json
            sbom.json

  # Container Security Scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build main application image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: hexstrike-ai:security-scan
          outputs: type=docker,dest=/tmp/main-image.tar

      - name: Build worker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.worker
          push: false
          tags: hexstrike-worker:security-scan
          outputs: type=docker,dest=/tmp/worker-image.tar

      - name: Run Trivy scan on main image
        uses: aquasecurity/trivy-action@master
        with:
          input: /tmp/main-image.tar
          format: 'json'
          output: 'trivy-main-report.json'

      - name: Run Trivy scan on worker image
        uses: aquasecurity/trivy-action@master
        with:
          input: /tmp/worker-image.tar
          format: 'json'
          output: 'trivy-worker-report.json'

      - name: Run Grype scan on main image
        uses: anchore/scan-action@v3
        with:
          image: hexstrike-ai:security-scan
          format: json
          output-file: grype-main-report.json
        continue-on-error: true

      - name: Upload container scan reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: container-security-reports
          path: |
            trivy-main-report.json
            trivy-worker-report.json
            grype-main-report.json

  # SAST (Static Application Security Testing)
  sast-scan:
    name: Static Application Security Testing
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install SAST tools
        run: |
          python -m pip install --upgrade pip
          pip install bandit semgrep
          pip install -r requirements.txt

      - name: Run Bandit security linter
        run: |
          bandit -r . -f json -o bandit-security-report.json || true
          bandit -r . -f txt
        continue-on-error: true

      - name: Run Semgrep SAST
        run: |
          semgrep --config=auto --json --output=semgrep-security-report.json . || true
          semgrep --config=p/security-audit --json --output=semgrep-audit-report.json . || true
        continue-on-error: true

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: python
          queries: security-and-quality

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

      - name: Upload SAST reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: sast-security-reports
          path: |
            bandit-security-report.json
            semgrep-security-report.json
            semgrep-audit-report.json

  # Secret Scanning
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install TruffleHog
        run: |
          curl -sSfL https://raw.githubusercontent.com/trufflesecurity/trufflehog/main/scripts/install.sh | sh -s -- -b /usr/local/bin

      - name: Run TruffleHog secret scan
        run: |
          trufflehog git file://. --json --output=trufflehog-report.json || true
        continue-on-error: true

      - name: Install detect-secrets
        run: |
          pip install detect-secrets

      - name: Run detect-secrets
        run: |
          detect-secrets scan --all-files --baseline .secrets.baseline || true
          detect-secrets audit .secrets.baseline || true
        continue-on-error: true

      - name: Upload secret scan reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: secret-scan-reports
          path: |
            trufflehog-report.json
            .secrets.baseline

  # License Compliance
  license-scan:
    name: License Compliance
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install license scanning tools
        run: |
          python -m pip install --upgrade pip
          pip install pip-licenses licensecheck
          pip install -r requirements.txt
          pip install -r requirements-worker.txt

      - name: Generate license report
        run: |
          pip-licenses --format=json --output-file=licenses-report.json
          pip-licenses --format=csv --output-file=licenses-report.csv
          licensecheck --zero || true

      - name: Upload license reports
        uses: actions/upload-artifact@v3
        with:
          name: license-reports
          path: |
            licenses-report.json
            licenses-report.csv

  # Security Summary Report
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [dependency-scan, container-scan, sast-scan, secret-scan, license-scan]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Install jq for JSON processing
        run: sudo apt-get install jq

      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "Generated on: $(date)" >> security-summary.md
          echo "Commit: ${{ github.sha }}" >> security-summary.md
          echo "" >> security-summary.md
          
          # Process dependency vulnerabilities
          if [ -f "dependency-vulnerability-reports/safety-report.json" ]; then
            VULN_COUNT=$(jq '.vulnerabilities | length' dependency-vulnerability-reports/safety-report.json 2>/dev/null || echo "0")
            echo "## Dependency Vulnerabilities: $VULN_COUNT" >> security-summary.md
          fi
          
          # Process container vulnerabilities
          if [ -f "container-security-reports/trivy-main-report.json" ]; then
            CRITICAL=$(jq '.Results[]?.Vulnerabilities[]? | select(.Severity=="CRITICAL") | .VulnerabilityID' container-security-reports/trivy-main-report.json 2>/dev/null | wc -l || echo "0")
            HIGH=$(jq '.Results[]?.Vulnerabilities[]? | select(.Severity=="HIGH") | .VulnerabilityID' container-security-reports/trivy-main-report.json 2>/dev/null | wc -l || echo "0")
            echo "## Container Vulnerabilities (Main): Critical: $CRITICAL, High: $HIGH" >> security-summary.md
          fi
          
          # Process SAST findings
          if [ -f "sast-security-reports/bandit-security-report.json" ]; then
            SAST_ISSUES=$(jq '.results | length' sast-security-reports/bandit-security-report.json 2>/dev/null || echo "0")
            echo "## SAST Issues: $SAST_ISSUES" >> security-summary.md
          fi
          
          # Process secret scan
          if [ -f "secret-scan-reports/trufflehog-report.json" ]; then
            SECRET_COUNT=$(jq '. | length' secret-scan-reports/trufflehog-report.json 2>/dev/null || echo "0")
            echo "## Potential Secrets Found: $SECRET_COUNT" >> security-summary.md
          fi
          
          echo "" >> security-summary.md
          echo "## Recommendations" >> security-summary.md
          echo "- Review and address critical and high severity vulnerabilities" >> security-summary.md
          echo "- Update dependencies with known vulnerabilities" >> security-summary.md
          echo "- Investigate and remediate any exposed secrets" >> security-summary.md
          echo "- Ensure all licenses are compliant with project requirements" >> security-summary.md

      - name: Upload security summary
        uses: actions/upload-artifact@v3
        with:
          name: security-summary
          path: security-summary.md

      - name: Comment on PR (if applicable)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('security-summary.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🔒 Security Scan Results\n\n${summary}`
            });

      - name: Create security issue (if vulnerabilities found)
        if: github.ref == 'refs/heads/main'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            
            // Check if there are critical vulnerabilities
            let hasCritical = false;
            
            try {
              const dependencyReport = JSON.parse(fs.readFileSync('dependency-vulnerability-reports/safety-report.json', 'utf8'));
              if (dependencyReport.vulnerabilities && dependencyReport.vulnerabilities.length > 0) {
                hasCritical = true;
              }
            } catch (e) {}
            
            if (hasCritical) {
              const summary = fs.readFileSync('security-summary.md', 'utf8');
              
              github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `🚨 Security Vulnerabilities Detected - ${new Date().toISOString().split('T')[0]}`,
                body: `## Security Alert\n\nCritical security vulnerabilities have been detected in the codebase.\n\n${summary}\n\n**Action Required:** Please review and address these vulnerabilities immediately.`,
                labels: ['security', 'critical', 'vulnerability']
              });
            }