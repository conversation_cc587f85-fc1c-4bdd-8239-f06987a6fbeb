#!/usr/bin/env python3
"""
Deploy database schema to Supabase
This script applies the migration SQL to the Supabase database
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_supabase_client() -> Client:
    """Create Supabase client"""
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env file")
    
    return create_client(url, key)

def read_migration_file() -> str:
    """Read the latest migration SQL file"""
    versions_dir = os.path.join('migrations', 'versions')
    
    if not os.path.exists(versions_dir):
        raise FileNotFoundError("No migrations/versions directory found")
    
    # Find the latest SQL file
    sql_files = [f for f in os.listdir(versions_dir) if f.endswith('.sql')]
    
    if not sql_files:
        raise FileNotFoundError("No SQL migration files found")
    
    # Get the latest file (sorted by name which includes timestamp)
    latest_file = sorted(sql_files)[-1]
    filepath = os.path.join(versions_dir, latest_file)
    
    print(f"Reading migration file: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def apply_migration(supabase: Client, sql: str) -> bool:
    """Apply migration SQL to Supabase"""
    try:
        print("Applying database migration...")
        
        # Split SQL into individual statements
        statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements, 1):
            if statement.strip():
                print(f"Executing statement {i}/{len(statements)}...")
                try:
                    # Use the REST API to execute SQL
                    result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                    print(f"Statement {i} executed successfully")
                except Exception as e:
                    print(f"Warning: Statement {i} failed: {e}")
                    # Continue with other statements
                    continue
        
        print("Migration applied successfully!")
        return True
        
    except Exception as e:
        print(f"Error applying migration: {e}")
        return False

def verify_tables(supabase: Client) -> bool:
    """Verify that tables were created successfully"""
    try:
        print("Verifying database tables...")
        
        # Check if main tables exist by trying to query them
        tables_to_check = ['users', 'jobs', 'reports', 'audit_logs']
        
        for table in tables_to_check:
            try:
                result = supabase.table(table).select('*').limit(1).execute()
                print(f"✓ Table '{table}' exists and is accessible")
            except Exception as e:
                print(f"✗ Table '{table}' check failed: {e}")
                return False
        
        print("All tables verified successfully!")
        return True
        
    except Exception as e:
        print(f"Error verifying tables: {e}")
        return False

def main():
    """Main deployment function"""
    try:
        print("HexStrike AI Database Deployment")
        print("=" * 40)
        
        # Create Supabase client
        print("Connecting to Supabase...")
        supabase = get_supabase_client()
        print("✓ Connected to Supabase")
        
        # Read migration file
        sql = read_migration_file()
        print("✓ Migration file loaded")
        
        # Apply migration
        if apply_migration(supabase, sql):
            print("✓ Migration applied")
        else:
            print("✗ Migration failed")
            sys.exit(1)
        
        # Verify tables
        if verify_tables(supabase):
            print("✓ Database verification passed")
        else:
            print("✗ Database verification failed")
            sys.exit(1)
        
        print("\n" + "=" * 40)
        print("Database deployment completed successfully!")
        print("Your HexStrike AI database is ready for use.")
        
    except Exception as e:
        print(f"Deployment failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()