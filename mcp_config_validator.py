#!/usr/bin/env python3
"""
MCP Configuration Validator

Validates and enforces security policies for Chat Box AI MCP Server configuration.
Ensures compliance with security standards and proper tool access controls.
"""

import json
import logging
import os
import sys
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import ipaddress
import re

class SecurityLevel(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ToolRiskLevel(Enum):
    SAFE = "safe"
    MODERATE = "moderate"
    HIGH_RISK = "high_risk"
    BLOCKED = "blocked"

@dataclass
class ValidationResult:
    """Result of configuration validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    recommendations: List[str]
    security_score: int  # 0-100

class MCPConfigValidator:
    """Validates MCP configuration for security compliance"""
    
    def __init__(self, config_path: str, security_config_path: Optional[str] = None):
        self.config_path = config_path
        self.security_config_path = security_config_path or "mcp-security-config.json"
        self.logger = self._setup_logging()
        
        # Load configurations
        self.mcp_config = self._load_json(config_path)
        self.security_config = self._load_json(self.security_config_path)
        
        # Security baselines
        self.max_timeout = 300  # 5 minutes
        self.min_retry_delay = 1000  # 1 second
        self.max_retries = 3
        self.max_memory_mb = 1024
        self.max_cpu_percent = 75
        
        # Known dangerous tools
        self.dangerous_tools = {
            "metasploit_exploit", "empire_agent", "cobalt_strike", "mimikatz",
            "bloodhound", "crackmapexec", "pacu_exploitation", "kube_hunter_scan"
        }
        
        # Required security fields
        self.required_security_fields = {
            "timeout", "retryPolicy", "resourceLimits", "security", 
            "rateLimits", "logging", "blocked"
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _load_json(self, file_path: str) -> Dict[str, Any]:
        """Load JSON configuration file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Configuration file not found: {file_path}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in {file_path}: {e}")
            return {}
    
    def validate_configuration(self) -> ValidationResult:
        """Perform comprehensive configuration validation"""
        errors = []
        warnings = []
        recommendations = []
        security_score = 100
        
        # Check if MCP config exists
        if not self.mcp_config:
            errors.append("MCP configuration file is missing or invalid")
            return ValidationResult(False, errors, warnings, recommendations, 0)
        
        # Validate MCP server configuration
        mcp_servers = self.mcp_config.get("mcpServers", {})
        if not mcp_servers:
            errors.append("No MCP servers configured")
            return ValidationResult(False, errors, warnings, recommendations, 0)
        
        for server_name, server_config in mcp_servers.items():
            self.logger.info(f"Validating server: {server_name}")
            
            # Validate basic structure
            structure_errors, structure_warnings, structure_score = self._validate_structure(server_config)
            errors.extend(structure_errors)
            warnings.extend(structure_warnings)
            security_score = min(security_score, structure_score)
            
            # Validate security settings
            security_errors, security_warnings, security_score_delta = self._validate_security_settings(server_config)
            errors.extend(security_errors)
            warnings.extend(security_warnings)
            security_score = min(security_score, security_score_delta)
            
            # Validate tool access controls
            tool_errors, tool_warnings, tool_score = self._validate_tool_access(server_config)
            errors.extend(tool_errors)
            warnings.extend(tool_warnings)
            security_score = min(security_score, tool_score)
            
            # Validate resource limits
            resource_errors, resource_warnings, resource_score = self._validate_resource_limits(server_config)
            errors.extend(resource_errors)
            warnings.extend(resource_warnings)
            security_score = min(security_score, resource_score)
            
            # Generate recommendations
            recommendations.extend(self._generate_recommendations(server_config))
        
        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings, recommendations, security_score)
    
    def _validate_structure(self, config: Dict[str, Any]) -> tuple[List[str], List[str], int]:
        """Validate basic configuration structure"""
        errors = []
        warnings = []
        score = 100
        
        # Check required fields
        required_fields = ["command", "args", "description"]
        for field in required_fields:
            if field not in config:
                errors.append(f"Missing required field: {field}")
                score -= 20
        
        # Check security fields
        missing_security_fields = self.required_security_fields - set(config.keys())
        if missing_security_fields:
            for field in missing_security_fields:
                errors.append(f"Missing security field: {field}")
                score -= 15
        
        return errors, warnings, score
    
    def _validate_security_settings(self, config: Dict[str, Any]) -> tuple[List[str], List[str], int]:
        """Validate security-specific settings"""
        errors = []
        warnings = []
        score = 100
        
        # Validate timeout
        timeout = config.get("timeout", 0)
        if timeout > self.max_timeout:
            warnings.append(f"Timeout {timeout}s exceeds recommended maximum {self.max_timeout}s")
            score -= 10
        elif timeout <= 0:
            errors.append("Timeout must be positive")
            score -= 20
        
        # Validate retry policy
        retry_policy = config.get("retryPolicy", {})
        if retry_policy:
            max_retries = retry_policy.get("maxRetries", 0)
            if max_retries > self.max_retries:
                warnings.append(f"Max retries {max_retries} exceeds recommended maximum {self.max_retries}")
                score -= 5
            
            retry_delay = retry_policy.get("retryDelay", 0)
            if retry_delay < self.min_retry_delay:
                warnings.append(f"Retry delay {retry_delay}ms below recommended minimum {self.min_retry_delay}ms")
                score -= 5
        
        # Validate security section
        security = config.get("security", {})
        if security:
            if security.get("allowNetworkAccess", True):
                warnings.append("Network access is enabled - consider disabling for security")
                score -= 15
            
            if security.get("allowFileSystemAccess", True):
                warnings.append("File system access is enabled - consider disabling for security")
                score -= 15
            
            if security.get("allowProcessExecution", True):
                warnings.append("Process execution is enabled - consider disabling for security")
                score -= 20
            
            if not security.get("sandboxed", False):
                errors.append("Sandboxing is disabled - this is a security risk")
                score -= 25
        
        return errors, warnings, score
    
    def _validate_tool_access(self, config: Dict[str, Any]) -> tuple[List[str], List[str], int]:
        """Validate tool access controls"""
        errors = []
        warnings = []
        score = 100
        
        always_allow = set(config.get("alwaysAllow", []))
        require_approval = set(config.get("requireApproval", []))
        blocked = set(config.get("blocked", []))
        
        # Check for dangerous tools in alwaysAllow
        dangerous_allowed = always_allow.intersection(self.dangerous_tools)
        if dangerous_allowed:
            errors.append(f"Dangerous tools in alwaysAllow: {dangerous_allowed}")
            score -= 30
        
        # Check if dangerous tools are properly blocked
        dangerous_not_blocked = self.dangerous_tools - blocked
        if dangerous_not_blocked:
            warnings.append(f"Dangerous tools not explicitly blocked: {dangerous_not_blocked}")
            score -= 15
        
        # Check for overlaps
        overlap_allow_block = always_allow.intersection(blocked)
        if overlap_allow_block:
            errors.append(f"Tools in both alwaysAllow and blocked: {overlap_allow_block}")
            score -= 20
        
        overlap_approval_block = require_approval.intersection(blocked)
        if overlap_approval_block:
            warnings.append(f"Tools in both requireApproval and blocked: {overlap_approval_block}")
            score -= 10
        
        # Check if any tools require approval
        if not require_approval:
            warnings.append("No tools require approval - consider adding approval requirements")
            score -= 10
        
        return errors, warnings, score
    
    def _validate_resource_limits(self, config: Dict[str, Any]) -> tuple[List[str], List[str], int]:
        """Validate resource limit settings"""
        errors = []
        warnings = []
        score = 100
        
        resource_limits = config.get("resourceLimits", {})
        if not resource_limits:
            errors.append("Resource limits not configured")
            return errors, warnings, 0
        
        # Validate memory limits
        max_memory = resource_limits.get("maxMemoryMB", 0)
        if max_memory > self.max_memory_mb:
            warnings.append(f"Memory limit {max_memory}MB exceeds recommended maximum {self.max_memory_mb}MB")
            score -= 10
        elif max_memory <= 0:
            errors.append("Memory limit must be positive")
            score -= 20
        
        # Validate CPU limits
        max_cpu = resource_limits.get("maxCpuPercent", 0)
        if max_cpu > self.max_cpu_percent:
            warnings.append(f"CPU limit {max_cpu}% exceeds recommended maximum {self.max_cpu_percent}%")
            score -= 10
        elif max_cpu <= 0:
            errors.append("CPU limit must be positive")
            score -= 20
        
        # Validate execution time
        max_exec_time = resource_limits.get("maxExecutionTimeMs", 0)
        if max_exec_time <= 0:
            errors.append("Execution time limit must be positive")
            score -= 20
        
        return errors, warnings, score
    
    def _generate_recommendations(self, config: Dict[str, Any]) -> List[str]:
        """Generate security recommendations"""
        recommendations = []
        
        # Rate limiting recommendations
        rate_limits = config.get("rateLimits", {})
        if not rate_limits:
            recommendations.append("Add rate limiting to prevent abuse")
        else:
            rpm = rate_limits.get("requestsPerMinute", 0)
            if rpm > 20:
                recommendations.append("Consider reducing requests per minute for better security")
        
        # Logging recommendations
        logging_config = config.get("logging", {})
        if not logging_config.get("auditEnabled", False):
            recommendations.append("Enable audit logging for security monitoring")
        
        if not logging_config.get("sensitiveDataMasking", False):
            recommendations.append("Enable sensitive data masking in logs")
        
        # Security recommendations
        security = config.get("security", {})
        if not security:
            recommendations.append("Add comprehensive security configuration")
        
        # Tool access recommendations
        always_allow = config.get("alwaysAllow", [])
        if len(always_allow) > 5:
            recommendations.append("Consider reducing the number of tools in alwaysAllow")
        
        return recommendations
    
    def generate_report(self, result: ValidationResult) -> str:
        """Generate a comprehensive validation report"""
        report = []
        report.append("=" * 60)
        report.append("MCP CONFIGURATION VALIDATION REPORT")
        report.append("=" * 60)
        report.append(f"Configuration File: {self.config_path}")
        report.append(f"Security Score: {result.security_score}/100")
        report.append(f"Status: {'PASS' if result.is_valid else 'FAIL'}")
        report.append("")
        
        if result.errors:
            report.append("ERRORS:")
            for i, error in enumerate(result.errors, 1):
                report.append(f"  {i}. {error}")
            report.append("")
        
        if result.warnings:
            report.append("WARNINGS:")
            for i, warning in enumerate(result.warnings, 1):
                report.append(f"  {i}. {warning}")
            report.append("")
        
        if result.recommendations:
            report.append("RECOMMENDATIONS:")
            for i, rec in enumerate(result.recommendations, 1):
                report.append(f"  {i}. {rec}")
            report.append("")
        
        # Security score interpretation
        report.append("SECURITY SCORE INTERPRETATION:")
        if result.security_score >= 90:
            report.append("  Excellent - Configuration meets high security standards")
        elif result.security_score >= 75:
            report.append("  Good - Configuration is secure with minor improvements needed")
        elif result.security_score >= 60:
            report.append("  Fair - Configuration needs security improvements")
        else:
            report.append("  Poor - Configuration has significant security issues")
        
        report.append("=" * 60)
        return "\n".join(report)

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        print("Usage: python mcp_config_validator.py <config_file> [security_config_file]")
        sys.exit(1)
    
    config_file = sys.argv[1]
    security_config_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    validator = MCPConfigValidator(config_file, security_config_file)
    result = validator.validate_configuration()
    
    report = validator.generate_report(result)
    print(report)
    
    # Exit with error code if validation failed
    sys.exit(0 if result.is_valid else 1)

if __name__ == "__main__":
    main()