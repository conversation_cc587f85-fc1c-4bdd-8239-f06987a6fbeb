<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Workflow Builder - HexStrike AI</title>
    <style>
        :root {
            --primary-color: #6200ea;
            --secondary-color: #03dac6;
            --background-color: #121212;
            --surface-color: #1e1e1e;
            --on-surface-color: #e0e0e0;
            --error-color: #cf6679;
            --success-color: #03dac6;
        }

        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--on-surface-color);
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--on-surface-color);
            cursor: pointer;
            font-size: 16px;
        }

        .workflow-builder {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            gap: 20px;
            margin-top: 20px;
            height: calc(100vh - 150px);
        }

        .components-panel, .properties-panel {
            background-color: var(--surface-color);
            border-radius: 8px;
            padding: 15px;
            overflow-y: auto;
        }

        .panel-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: var(--secondary-color);
        }

        .component-item {
            padding: 10px;
            margin-bottom: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            cursor: grab;
            transition: all 0.2s ease;
        }

        .component-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .canvas {
            background-color: var(--surface-color);
            border-radius: 8px;
            overflow: auto;
            position: relative;
            padding: 20px;
        }

        .node {
            position: absolute;
            width: 180px;
            background-color: rgba(98, 0, 234, 0.2);
            border: 2px solid var(--primary-color);
            border-radius: 6px;
            padding: 10px;
            cursor: move;
        }

        .node-title {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .node-ports {
            display: flex;
            justify-content: space-between;
        }

        .port {
            width: 12px;
            height: 12px;
            background-color: var(--secondary-color);
            border-radius: 50%;
            cursor: pointer;
        }

        .connection {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .property-group {
            margin-bottom: 15px;
        }

        .property-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .property-input {
            width: 100%;
            padding: 8px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: var(--on-surface-color);
        }

        .templates-section {
            margin-top: 20px;
        }

        .template-card {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .template-card:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .template-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .template-description {
            font-size: 14px;
            opacity: 0.8;
        }

        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
        }

        .primary-button {
            background-color: var(--primary-color);
            color: white;
        }

        .secondary-button {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        /* Light theme variables */
        body.light-theme {
            --background-color: #f5f5f5;
            --surface-color: #ffffff;
            --on-surface-color: #121212;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">HexStrike AI - Custom Workflow Builder</div>
            <button class="theme-toggle" id="themeToggle">Toggle Dark/Light Mode</button>
        </header>

        <div class="workflow-builder">
            <div class="components-panel">
                <div class="panel-title">Components</div>
                
                <div class="component-item" draggable="true" data-component-type="data-source">
                    Data Source
                </div>
                
                <div class="component-item" draggable="true" data-component-type="transformer">
                    Data Transformer
                </div>
                
                <div class="component-item" draggable="true" data-component-type="filter">
                    Filter
                </div>
                
                <div class="component-item" draggable="true" data-component-type="ai-processor">
                    AI Processor
                </div>
                
                <div class="component-item" draggable="true" data-component-type="decision">
                    Decision Node
                </div>
                
                <div class="component-item" draggable="true" data-component-type="output">
                    Output
                </div>

                <div class="templates-section">
                    <div class="panel-title">Templates</div>
                    
                    <div class="template-card" data-template="web-scraping">
                        <div class="template-title">Web Scraping Pipeline</div>
                        <div class="template-description">Extract, transform, and analyze web data</div>
                    </div>
                    
                    <div class="template-card" data-template="security-scan">
                        <div class="template-title">Security Scan Workflow</div>
                        <div class="template-description">Automated security testing sequence</div>
                    </div>
                    
                    <div class="template-card" data-template="data-analysis">
                        <div class="template-title">Data Analysis Pipeline</div>
                        <div class="template-description">Process and visualize data insights</div>
                    </div>
                </div>
            </div>

            <div class="canvas" id="workflowCanvas">
                <!-- Nodes will be added here dynamically -->
            </div>

            <div class="properties-panel">
                <div class="panel-title">Properties</div>
                
                <div class="property-group">
                    <label class="property-label">Node Name</label>
                    <input type="text" class="property-input" id="nodeName" placeholder="Enter node name">
                </div>
                
                <div class="property-group">
                    <label class="property-label">Description</label>
                    <textarea class="property-input" id="nodeDescription" rows="3" placeholder="Enter description"></textarea>
                </div>
                
                <div class="property-group">
                    <label class="property-label">Configuration</label>
                    <textarea class="property-input" id="nodeConfig" rows="6" placeholder="JSON configuration"></textarea>
                </div>
                
                <div class="action-buttons">
                    <button class="secondary-button" id="deleteNode">Delete Node</button>
                    <button class="primary-button" id="applyChanges">Apply Changes</button>
                </div>
            </div>
        </div>

        <div class="action-buttons" style="margin-top: 20px;">
            <button class="secondary-button" id="clearCanvas">Clear Canvas</button>
            <button class="primary-button" id="saveWorkflow">Save Workflow</button>
        </div>
    </div>

    <script>
        // Theme toggling
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('light-theme');
        });

        // Canvas and node management
        const canvas = document.getElementById('workflowCanvas');
        let nodes = [];
        let connections = [];
        let selectedNode = null;
        let draggedComponent = null;
        let isCreatingConnection = false;
        let connectionStartPort = null;

        // Make components draggable
        const componentItems = document.querySelectorAll('.component-item');
        componentItems.forEach(item => {
            item.addEventListener('dragstart', (e) => {
                draggedComponent = {
                    type: item.dataset.componentType,
                    label: item.textContent.trim()
                };
            });
        });

        // Canvas drop zone
        canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            if (draggedComponent) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                createNode(draggedComponent.type, draggedComponent.label, x, y);
                draggedComponent = null;
            }
        });

        // Create a node
        function createNode(type, label, x, y) {
            const nodeId = 'node-' + Date.now();
            const node = document.createElement('div');
            node.className = 'node';
            node.id = nodeId;
            node.dataset.type = type;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            
            node.innerHTML = `
                <div class="node-title">${label}</div>
                <div class="node-ports">
                    <div class="port input-port" data-port-type="input"></div>
                    <div class="port output-port" data-port-type="output"></div>
                </div>
            `;
            
            canvas.appendChild(node);
            
            // Make node draggable
            makeNodeDraggable(node);
            
            // Make ports connectable
            setupPorts(node);
            
            // Select node on click
            node.addEventListener('click', () => {
                selectNode(nodeId);
            });
            
            nodes.push({
                id: nodeId,
                type: type,
                label: label,
                x: x,
                y: y,
                config: {}
            });
            
            selectNode(nodeId);
        }

        // Make a node draggable
        function makeNodeDraggable(node) {
            let offsetX, offsetY, isDragging = false;
            
            node.addEventListener('mousedown', (e) => {
                if (e.target.classList.contains('port')) return;
                
                isDragging = true;
                offsetX = e.clientX - node.offsetLeft;
                offsetY = e.clientY - node.offsetTop;
                
                // Bring to front
                node.style.zIndex = '10';
            });
            
            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;
                
                const x = e.clientX - offsetX;
                const y = e.clientY - offsetY;
                
                node.style.left = x + 'px';
                node.style.top = y + 'px';
                
                // Update connections
                updateConnections(node.id);
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    node.style.zIndex = '1';
                    
                    // Update node position in data
                    const nodeData = nodes.find(n => n.id === node.id);
                    if (nodeData) {
                        nodeData.x = parseInt(node.style.left);
                        nodeData.y = parseInt(node.style.top);
                    }
                }
            });
        }

        // Setup connection ports
        function setupPorts(node) {
            const ports = node.querySelectorAll('.port');
            
            ports.forEach(port => {
                port.addEventListener('mousedown', (e) => {
                    e.stopPropagation();
                    isCreatingConnection = true;
                    connectionStartPort = {
                        nodeId: node.id,
                        portType: port.dataset.portType,
                        element: port
                    };
                });
            });
        }

        // Select a node to edit properties
        function selectNode(nodeId) {
            // Deselect previous node
            if (selectedNode) {
                document.getElementById(selectedNode).classList.remove('selected');
            }
            
            selectedNode = nodeId;
            document.getElementById(nodeId).classList.add('selected');
            
            // Load node properties
            const nodeData = nodes.find(n => n.id === nodeId);
            if (nodeData) {
                document.getElementById('nodeName').value = nodeData.label || '';
                document.getElementById('nodeDescription').value = nodeData.description || '';
                document.getElementById('nodeConfig').value = nodeData.config ? JSON.stringify(nodeData.config, null, 2) : '{}';
            }
        }

        // Update connections when nodes move
        function updateConnections(nodeId) {
            connections.forEach(conn => {
                if (conn.sourceNodeId === nodeId || conn.targetNodeId === nodeId) {
                    // Update connection line
                    // This would be implemented with SVG or Canvas drawing
                }
            });
        }

        // Apply property changes
        document.getElementById('applyChanges').addEventListener('click', () => {
            if (!selectedNode) return;
            
            const nodeData = nodes.find(n => n.id === selectedNode);
            if (nodeData) {
                nodeData.label = document.getElementById('nodeName').value;
                nodeData.description = document.getElementById('nodeDescription').value;
                
                try {
                    nodeData.config = JSON.parse(document.getElementById('nodeConfig').value);
                } catch (e) {
                    alert('Invalid JSON configuration');
                    return;
                }
                
                // Update node display
                const nodeElement = document.getElementById(selectedNode);
                nodeElement.querySelector('.node-title').textContent = nodeData.label;
            }
        });

        // Delete selected node
        document.getElementById('deleteNode').addEventListener('click', () => {
            if (!selectedNode) return;
            
            // Remove node element
            const nodeElement = document.getElementById(selectedNode);
            nodeElement.remove();
            
            // Remove from data
            nodes = nodes.filter(n => n.id !== selectedNode);
            
            // Remove connections
            connections = connections.filter(c => 
                c.sourceNodeId !== selectedNode && c.targetNodeId !== selectedNode
            );
            
            selectedNode = null;
            
            // Clear properties panel
            document.getElementById('nodeName').value = '';
            document.getElementById('nodeDescription').value = '';
            document.getElementById('nodeConfig').value = '{}';
        });

        // Clear canvas
        document.getElementById('clearCanvas').addEventListener('click', () => {
            if (confirm('Are you sure you want to clear the canvas? All nodes and connections will be lost.')) {
                // Remove all nodes
                nodes.forEach(node => {
                    const nodeElement = document.getElementById(node.id);
                    if (nodeElement) nodeElement.remove();
                });
                
                // Clear data
                nodes = [];
                connections = [];
                selectedNode = null;
                
                // Clear properties panel
                document.getElementById('nodeName').value = '';
                document.getElementById('nodeDescription').value = '';
                document.getElementById('nodeConfig').value = '{}';
            }
        });

        // Save workflow
        document.getElementById('saveWorkflow').addEventListener('click', () => {
            const workflow = {
                nodes: nodes,
                connections: connections,
                metadata: {
                    name: 'Custom Workflow',
                    created: new Date().toISOString(),
                    version: '1.0'
                }
            };
            
            // In a real application, this would send data to the server
            console.log('Saving workflow:', workflow);
            alert('Workflow saved successfully!');
        });

        // Load templates
        const templateCards = document.querySelectorAll('.template-card');
        templateCards.forEach(card => {
            card.addEventListener('click', () => {
                const templateType = card.dataset.template;
                loadTemplate(templateType);
            });
        });

        // Load a predefined template
        function loadTemplate(templateType) {
            // Clear current canvas
            document.getElementById('clearCanvas').click();
            
            // Create template nodes based on type
            switch(templateType) {
                case 'web-scraping':
                    createNode('data-source', 'Web Scraper', 50, 50);
                    createNode('transformer', 'Data Extractor', 300, 50);
                    createNode('filter', 'Content Filter', 550, 50);
                    createNode('output', 'Database Output', 800, 50);
                    break;
                    
                case 'security-scan':
                    createNode('data-source', 'Target Input', 50, 50);
                    createNode('ai-processor', 'Vulnerability Scanner', 300, 50);
                    createNode('decision', 'Risk Assessment', 550, 50);
                    createNode('output', 'Report Generator', 800, 50);
                    break;
                    
                case 'data-analysis':
                    createNode('data-source', 'Data Source', 50, 50);
                    createNode('transformer', 'Data Cleaner', 300, 50);
                    createNode('ai-processor', 'AI Analysis', 550, 50);
                    createNode('output', 'Visualization', 800, 50);
                    break;
            }
        }
    </script>
</body>
</html>