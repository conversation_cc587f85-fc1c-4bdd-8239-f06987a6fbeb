#!/usr/bin/env python3
"""
XSS Protection Module for HexStrike AI
Provides comprehensive Cross-Site Scripting (XSS) protection
"""

import html
import re
import json
import logging
from typing import Any, Dict, List, Union
from functools import wraps
from flask import request, jsonify, make_response

logger = logging.getLogger(__name__)

class XSSProtection:
    """Comprehensive XSS protection utilities"""
    
    # Dangerous HTML tags that should be stripped
    DANGEROUS_TAGS = [
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'textarea',
        'button', 'select', 'option', 'link', 'meta', 'style', 'base',
        'applet', 'body', 'html', 'head', 'title'
    ]
    
    # Dangerous attributes that should be removed
    DANGEROUS_ATTRIBUTES = [
        'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
        'onkeydown', 'onkeyup', 'onkeypress', 'onfocus', 'onblur',
        'onchange', 'onsubmit', 'onreset', 'onselect', 'onunload',
        'onbeforeunload', 'onresize', 'onscroll', 'ondblclick',
        'oncontextmenu', 'ondrag', 'ondrop', 'javascript:', 'vbscript:',
        'data:', 'expression', 'behavior', 'binding'
    ]
    
    # XSS attack patterns
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'onmouseover\s*=',
        r'expression\s*\(',
        r'url\s*\(',
        r'<iframe[^>]*>',
        r'<object[^>]*>',
        r'<embed[^>]*>',
        r'<link[^>]*>',
        r'<meta[^>]*>',
        r'<style[^>]*>',
        r'<base[^>]*>',
        r'data:text/html',
        r'data:application/javascript'
    ]
    
    @staticmethod
    def sanitize_html(content: str, allow_tags: List[str] = None) -> str:
        """Sanitize HTML content by removing dangerous elements"""
        if not isinstance(content, str):
            return str(content)
        
        # Default allowed tags (safe HTML tags)
        if allow_tags is None:
            allow_tags = ['p', 'br', 'strong', 'em', 'u', 'i', 'b', 'span', 'div']
        
        # Remove dangerous tags
        for tag in XSSProtection.DANGEROUS_TAGS:
            if tag not in allow_tags:
                # Remove opening and closing tags
                content = re.sub(f'<{tag}[^>]*>', '', content, flags=re.IGNORECASE)
                content = re.sub(f'</{tag}>', '', content, flags=re.IGNORECASE)
        
        # Remove dangerous attributes
        for attr in XSSProtection.DANGEROUS_ATTRIBUTES:
            content = re.sub(rf'{attr}[^\s>]*', '', content, flags=re.IGNORECASE)
        
        # Remove XSS patterns
        for pattern in XSSProtection.XSS_PATTERNS:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.DOTALL)
        
        return content
    
    @staticmethod
    def escape_html(content: str) -> str:
        """Escape HTML entities to prevent XSS"""
        if not isinstance(content, str):
            return str(content)
        
        return html.escape(content, quote=True)
    
    @staticmethod
    def sanitize_json_output(data: Any) -> Any:
        """Recursively sanitize JSON output to prevent XSS"""
        if isinstance(data, dict):
            return {key: XSSProtection.sanitize_json_output(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [XSSProtection.sanitize_json_output(item) for item in data]
        elif isinstance(data, str):
            return XSSProtection.escape_html(data)
        else:
            return data
    
    @staticmethod
    def detect_xss_attempt(content: str) -> tuple[bool, str]:
        """Detect potential XSS attempts in content"""
        if not isinstance(content, str):
            return False, "Valid"
        
        # Check for XSS patterns
        for pattern in XSSProtection.XSS_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                logger.warning(f"XSS attempt detected: {content[:100]}...")
                return True, "Potential XSS attack detected"
        
        # Check for dangerous tags
        for tag in XSSProtection.DANGEROUS_TAGS:
            if re.search(f'<{tag}[^>]*>', content, re.IGNORECASE):
                logger.warning(f"Dangerous HTML tag detected: {tag}")
                return True, f"Dangerous HTML tag detected: {tag}"
        
        # Check for dangerous attributes
        for attr in XSSProtection.DANGEROUS_ATTRIBUTES:
            if re.search(rf'{attr}\s*=', content, re.IGNORECASE):
                logger.warning(f"Dangerous HTML attribute detected: {attr}")
                return True, f"Dangerous HTML attribute detected: {attr}"
        
        return False, "Valid"
    
    @staticmethod
    def validate_url(url: str) -> tuple[bool, str]:
        """Validate URL to prevent XSS through URL manipulation"""
        if not isinstance(url, str):
            return False, "URL must be a string"
        
        # Check for dangerous protocols
        dangerous_protocols = ['javascript:', 'vbscript:', 'data:', 'file:']
        for protocol in dangerous_protocols:
            if url.lower().startswith(protocol):
                return False, f"Dangerous protocol detected: {protocol}"
        
        # Check for XSS patterns in URL
        is_xss, message = XSSProtection.detect_xss_attempt(url)
        if is_xss:
            return False, f"XSS attempt in URL: {message}"
        
        return True, "Valid URL"

class ContentSecurityPolicy:
    """Content Security Policy (CSP) management"""
    
    @staticmethod
    def get_strict_csp() -> str:
        """Get a strict Content Security Policy header"""
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",  # Adjust as needed
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self' https:",
            "connect-src 'self'",
            "media-src 'self'",
            "object-src 'none'",
            "child-src 'none'",
            "frame-ancestors 'none'",
            "form-action 'self'",
            "base-uri 'self'",
            "upgrade-insecure-requests"
        ]
        return "; ".join(csp_directives)
    
    @staticmethod
    def get_development_csp() -> str:
        """Get a more permissive CSP for development"""
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' localhost:*",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https: localhost:*",
            "font-src 'self' https: localhost:*",
            "connect-src 'self' localhost:* ws: wss:",
            "media-src 'self'",
            "object-src 'none'",
            "child-src 'none'",
            "frame-ancestors 'none'",
            "form-action 'self'",
            "base-uri 'self'"
        ]
        return "; ".join(csp_directives)

# Decorators for XSS protection
def xss_protect_output(f):
    """Decorator to automatically sanitize JSON output"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            result = f(*args, **kwargs)
            
            # If it's a Flask response, sanitize the JSON data
            if hasattr(result, 'get_json'):
                data = result.get_json()
                if data:
                    sanitized_data = XSSProtection.sanitize_json_output(data)
                    return jsonify(sanitized_data), result.status_code
            
            # If it's a tuple (data, status_code)
            elif isinstance(result, tuple) and len(result) == 2:
                data, status_code = result
                if isinstance(data, dict):
                    sanitized_data = XSSProtection.sanitize_json_output(data)
                    return jsonify(sanitized_data), status_code
            
            return result
            
        except Exception as e:
            logger.error(f"XSS protection error: {str(e)}")
            return result
    
    return decorated_function

def validate_input_xss(f):
    """Decorator to validate input for XSS attempts"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Check JSON input
            if request.is_json:
                data = request.get_json()
                if data:
                    for key, value in data.items():
                        if isinstance(value, str):
                            is_xss, message = XSSProtection.detect_xss_attempt(value)
                            if is_xss:
                                logger.warning(f"XSS attempt blocked in field '{key}': {value[:50]}...")
                                return jsonify({
                                    'error': f'Invalid input in field {key}: {message}'
                                }), 400
            
            # Check query parameters
            for key, value in request.args.items():
                is_xss, message = XSSProtection.detect_xss_attempt(value)
                if is_xss:
                    logger.warning(f"XSS attempt blocked in query param '{key}': {value[:50]}...")
                    return jsonify({
                        'error': f'Invalid query parameter {key}: {message}'
                    }), 400
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"XSS validation error: {str(e)}")
            return jsonify({'error': 'Input validation failed'}), 500
    
    return decorated_function

def add_security_headers(response):
    """Add security headers to prevent XSS and other attacks"""
    # XSS Protection
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    # Content Type Options
    response.headers['X-Content-Type-Options'] = 'nosniff'
    
    # Frame Options
    response.headers['X-Frame-Options'] = 'DENY'
    
    # Content Security Policy
    response.headers['Content-Security-Policy'] = ContentSecurityPolicy.get_strict_csp()
    
    # Referrer Policy
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    
    # Permissions Policy
    response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
    
    return response