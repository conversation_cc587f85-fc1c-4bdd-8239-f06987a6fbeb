#!/usr/bin/env python3
"""
Web Application Firewall (WAF) Protection Module
Provides comprehensive request filtering and attack detection
"""

import re
import logging
import time
from collections import defaultdict, deque
from functools import wraps
from flask import request, jsonify, abort
from typing import Dict, List, Tuple, Optional
import ipaddress

# Configure logging
logger = logging.getLogger(__name__)

class WAFProtection:
    """Web Application Firewall protection class"""
    
    # SQL Injection patterns
    SQL_INJECTION_PATTERNS = [
        r"('|(\-\-)|(;)|(\||\|)|(\*|\*))",
        r"(union|select|insert|delete|update|drop|create|alter|exec|execute)",
        r"(script|javascript|vbscript|onload|onerror|onclick)",
        r"(\<|\>|\%3C|\%3E)",
        r"(eval\s*\(|expression\s*\()",
        r"(base64_decode|file_get_contents|fopen|fwrite)",
        r"(\.\.[\/\\]|\.\.%2f|\.\.%5c)",
        r"(etc\/passwd|boot\.ini|win\.ini)"
    ]
    
    # XSS patterns
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:\s*[^\s]",
        r"on\w+\s*=\s*['\"][^'\"]*['\"]?",
        r"<iframe[^>]*>.*?</iframe>",
        r"<object[^>]*>.*?</object>",
        r"<embed[^>]*>.*?</embed>",
        r"<link[^>]*>.*?</link>",
        r"<meta[^>]*>.*?</meta>",
        r"expression\s*\(",
        r"url\s*\(\s*['\"]?\s*javascript:",
        r"@import\s*['\"]?\s*javascript:"
    ]
    
    # Command injection patterns
    COMMAND_INJECTION_PATTERNS = [
        r"(;|\||&|\$\(|`|\$\{)",
        r"(cat|ls|pwd|id|whoami|uname|ps|netstat|ifconfig)",
        r"(rm|mv|cp|chmod|chown|kill|killall)",
        r"(wget|curl|nc|telnet|ssh|ftp)",
        r"(python|perl|php|ruby|bash|sh|cmd|powershell)"
    ]
    
    # Path traversal patterns
    PATH_TRAVERSAL_PATTERNS = [
        r"\.\.[\/\\]",
        r"\.\.%2f",
        r"\.\.%5c",
        r"%2e%2e%2f",
        r"%2e%2e%5c",
        r"etc[\/\\]passwd",
        r"boot\.ini",
        r"win\.ini"
    ]
    
    # Suspicious user agents
    SUSPICIOUS_USER_AGENTS = [
        r"sqlmap",
        r"nikto",
        r"nessus",
        r"openvas",
        r"w3af",
        r"burp",
        r"zap",
        r"nmap",
        r"masscan",
        r"dirb",
        r"gobuster",
        r"wfuzz",
        r"hydra",
        r"medusa"
    ]
    
    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.ip_blocker = IPBlocker()
        
    @staticmethod
    def detect_sql_injection(content: str) -> Tuple[bool, str]:
        """Detect SQL injection attempts"""
        content_lower = content.lower()
        
        for pattern in WAFProtection.SQL_INJECTION_PATTERNS:
            if re.search(pattern, content_lower, re.IGNORECASE):
                logger.warning(f"SQL injection attempt detected: {pattern}")
                return True, f"SQL injection pattern detected: {pattern}"
        
        return False, ""
    
    @staticmethod
    def detect_xss(content: str) -> Tuple[bool, str]:
        """Detect XSS attempts"""
        for pattern in WAFProtection.XSS_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                logger.warning(f"XSS attempt detected: {pattern}")
                return True, f"XSS pattern detected: {pattern}"
        
        return False, ""
    
    @staticmethod
    def detect_command_injection(content: str) -> Tuple[bool, str]:
        """Detect command injection attempts"""
        for pattern in WAFProtection.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE):
                logger.warning(f"Command injection attempt detected: {pattern}")
                return True, f"Command injection pattern detected: {pattern}"
        
        return False, ""
    
    @staticmethod
    def detect_path_traversal(content: str) -> Tuple[bool, str]:
        """Detect path traversal attempts"""
        for pattern in WAFProtection.PATH_TRAVERSAL_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE):
                logger.warning(f"Path traversal attempt detected: {pattern}")
                return True, f"Path traversal pattern detected: {pattern}"
        
        return False, ""
    
    @staticmethod
    def detect_suspicious_user_agent(user_agent: str) -> Tuple[bool, str]:
        """Detect suspicious user agents"""
        if not user_agent:
            return False, ""
            
        user_agent_lower = user_agent.lower()
        
        for pattern in WAFProtection.SUSPICIOUS_USER_AGENTS:
            if re.search(pattern, user_agent_lower):
                logger.warning(f"Suspicious user agent detected: {user_agent}")
                return True, f"Suspicious user agent: {pattern}"
        
        return False, ""
    
    def analyze_request(self, request_data: Dict) -> Tuple[bool, List[str]]:
        """Comprehensive request analysis"""
        threats = []
        
        # Check all request data
        all_content = str(request_data)
        
        # SQL Injection check
        is_threat, msg = self.detect_sql_injection(all_content)
        if is_threat:
            threats.append(f"SQL Injection: {msg}")
        
        # XSS check
        is_threat, msg = self.detect_xss(all_content)
        if is_threat:
            threats.append(f"XSS: {msg}")
        
        # Command injection check
        is_threat, msg = self.detect_command_injection(all_content)
        if is_threat:
            threats.append(f"Command Injection: {msg}")
        
        # Path traversal check
        is_threat, msg = self.detect_path_traversal(all_content)
        if is_threat:
            threats.append(f"Path Traversal: {msg}")
        
        return len(threats) > 0, threats

class RateLimiter:
    """Rate limiting functionality"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(deque)
    
    def is_rate_limited(self, client_ip: str) -> bool:
        """Check if client is rate limited"""
        now = time.time()
        client_requests = self.requests[client_ip]
        
        # Remove old requests outside the window
        while client_requests and client_requests[0] < now - self.window_seconds:
            client_requests.popleft()
        
        # Check if rate limit exceeded
        if len(client_requests) >= self.max_requests:
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return True
        
        # Add current request
        client_requests.append(now)
        return False

class IPBlocker:
    """IP blocking functionality"""
    
    def __init__(self):
        self.blocked_ips = set()
        self.blocked_networks = []
        self.suspicious_ips = defaultdict(int)
        self.block_threshold = 5
    
    def is_blocked(self, client_ip: str) -> bool:
        """Check if IP is blocked"""
        if client_ip in self.blocked_ips:
            return True
        
        try:
            ip = ipaddress.ip_address(client_ip)
            for network in self.blocked_networks:
                if ip in network:
                    return True
        except ValueError:
            logger.warning(f"Invalid IP address: {client_ip}")
            return True
        
        return False
    
    def add_suspicious_activity(self, client_ip: str):
        """Add suspicious activity for IP"""
        self.suspicious_ips[client_ip] += 1
        
        if self.suspicious_ips[client_ip] >= self.block_threshold:
            self.block_ip(client_ip)
            logger.warning(f"IP {client_ip} blocked due to suspicious activity")
    
    def block_ip(self, client_ip: str):
        """Block an IP address"""
        self.blocked_ips.add(client_ip)
    
    def block_network(self, network: str):
        """Block a network range"""
        try:
            self.blocked_networks.append(ipaddress.ip_network(network))
        except ValueError:
            logger.error(f"Invalid network range: {network}")

# Global WAF instance
waf = WAFProtection()

def waf_protection(f):
    """WAF protection decorator"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if client_ip:
            client_ip = client_ip.split(',')[0].strip()
        
        # Check if IP is blocked
        if waf.ip_blocker.is_blocked(client_ip):
            logger.warning(f"Blocked IP attempted access: {client_ip}")
            abort(403)
        
        # Check rate limiting
        if waf.rate_limiter.is_rate_limited(client_ip):
            abort(429)
        
        # Check user agent
        user_agent = request.headers.get('User-Agent', '')
        is_suspicious, msg = WAFProtection.detect_suspicious_user_agent(user_agent)
        if is_suspicious:
            waf.ip_blocker.add_suspicious_activity(client_ip)
            logger.warning(f"Suspicious user agent from {client_ip}: {msg}")
            abort(403)
        
        # Analyze request data
        request_data = {}
        
        # Get JSON data
        if request.is_json:
            try:
                request_data.update(request.get_json() or {})
            except Exception:
                pass
        
        # Get form data
        request_data.update(request.form.to_dict())
        
        # Get query parameters
        request_data.update(request.args.to_dict())
        
        # Get headers (selective)
        sensitive_headers = ['authorization', 'cookie', 'x-api-key']
        for header in sensitive_headers:
            if header in request.headers:
                request_data[f'header_{header}'] = request.headers[header]
        
        # Analyze for threats
        is_threat, threats = waf.analyze_request(request_data)
        
        if is_threat:
            waf.ip_blocker.add_suspicious_activity(client_ip)
            logger.warning(f"WAF blocked request from {client_ip}: {threats}")
            return jsonify({
                'error': 'Request blocked by WAF',
                'message': 'Malicious content detected'
            }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def advanced_waf_protection(strict_mode: bool = False):
    """Advanced WAF protection with configurable strictness"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            if client_ip:
                client_ip = client_ip.split(',')[0].strip()
            
            # All basic WAF checks
            result = waf_protection(f)(*args, **kwargs)
            
            # Additional strict mode checks
            if strict_mode:
                # Check request size
                if request.content_length and request.content_length > 10 * 1024 * 1024:  # 10MB
                    logger.warning(f"Large request blocked from {client_ip}: {request.content_length} bytes")
                    abort(413)
                
                # Check for unusual headers
                unusual_headers = ['x-forwarded-host', 'x-cluster-client-ip', 'x-real-ip']
                for header in unusual_headers:
                    if header in request.headers:
                        logger.warning(f"Unusual header detected from {client_ip}: {header}")
                        waf.ip_blocker.add_suspicious_activity(client_ip)
            
            return result
        
        return decorated_function
    return decorator