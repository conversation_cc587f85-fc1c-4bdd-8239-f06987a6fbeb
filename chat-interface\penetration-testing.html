<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penetration Testing - HexStrike AI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6 text-red-700">Penetration Testing Tools</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Tool 1 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="pen-nmap-scanner">
                <h2 class="text-xl font-semibold mb-3 text-red-600">Network Scanner</h2>
                <p class="text-gray-700 mb-4">Scan networks for open ports and services to identify potential entry points.</p>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 2 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="pen-vuln-scanner">
                <h2 class="text-xl font-semibold mb-3 text-red-600">Vulnerability Scanner</h2>
                <p class="text-gray-700 mb-4">Identify known vulnerabilities in target systems and applications.</p>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 3 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="pen-web-fuzzer">
                <h2 class="text-xl font-semibold mb-3 text-red-600">Web Fuzzer</h2>
                <p class="text-gray-700 mb-4">Test web applications for input validation issues and hidden endpoints.</p>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 4 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="pen-password-cracker">
                <h2 class="text-xl font-semibold mb-3 text-red-600">Password Cracker</h2>
                <p class="text-gray-700 mb-4">Test password strength and attempt to crack weak credentials.</p>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 5 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="pen-exploit-framework">
                <h2 class="text-xl font-semibold mb-3 text-red-600">Exploit Framework</h2>
                <p class="text-gray-700 mb-4">Execute controlled exploits against vulnerable systems.</p>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
            
            <!-- Tool 6 -->
            <div class="bg-white rounded-lg shadow-md p-6" data-tool-id="pen-social-engineering">
                <h2 class="text-xl font-semibold mb-3 text-red-600">Social Engineering</h2>
                <p class="text-gray-700 mb-4">Create and manage social engineering campaigns to test human security.</p>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Launch Tool
                </button>
            </div>
        </div>
        
        <div class="mt-8">
            <a href="/" class="text-red-600 hover:text-red-800">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script>
        // Tool functionality would be implemented here
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Penetration Testing Tools Loaded');
            
            // Add event listeners to buttons
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    const toolId = this.closest('[data-tool-id]').dataset.toolId;
                    console.log(`Launching tool: ${toolId}`);
                    alert(`Tool ${toolId} would launch here in a production environment`);
                });
            });
        });
    </script>
</body>
</html>