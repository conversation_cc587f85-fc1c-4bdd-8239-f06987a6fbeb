name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: hexstrike-ai

jobs:
  # Code Quality and Linting
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort mypy bandit safety
          pip install -r requirements.txt

      - name: Run Black (Code Formatting)
        run: black --check --diff .

      - name: Run isort (Import Sorting)
        run: isort --check-only --diff .

      - name: Run Flake8 (Linting)
        run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics

      - name: Run MyPy (Type Checking)
        run: mypy . --ignore-missing-imports
        continue-on-error: true

      - name: Run Bandit (Security Linting)
        run: bandit -r . -f json -o bandit-report.json
        continue-on-error: true

      - name: Upload Bandit Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: bandit-report
          path: bandit-report.json

  # Unit and Integration Tests
  test:
    name: Tests
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-cov pytest-mock
          pip install -r requirements.txt
          pip install -r requirements-worker.txt

      - name: Set up test environment
        run: |
          cp .env.docker .env.test
          echo "DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_db" >> .env.test
          echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
          echo "JWT_SECRET_KEY=test_jwt_secret_key_for_testing_only" >> .env.test
          echo "SECRET_KEY=test_flask_secret_key_for_testing_only" >> .env.test

      - name: Run tests
        env:
          FLASK_ENV: testing
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379/0
        run: |
          pytest tests/ -v --cov=. --cov-report=xml --cov-report=html

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

      - name: Upload coverage report
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: htmlcov/

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install security tools
        run: |
          python -m pip install --upgrade pip
          pip install safety semgrep
          pip install -r requirements.txt

      - name: Run Safety (Dependency Vulnerability Scan)
        run: safety check --json --output safety-report.json
        continue-on-error: true

      - name: Run Semgrep (SAST)
        run: |
          semgrep --config=auto --json --output=semgrep-report.json .
        continue-on-error: true

      - name: Upload Security Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            safety-report.json
            semgrep-report.json

  # Docker Build and Security Scan
  docker:
    name: Docker Build & Scan
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build Docker images
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          outputs: type=docker,dest=/tmp/image.tar

      - name: Build Worker Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.worker
          push: false
          tags: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/worker:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          input: /tmp/image.tar
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Push Docker images
        if: github.event_name != 'pull_request'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  # Deployment
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [test, security, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your deployment commands here
          # Example: kubectl apply -f k8s/
          # Example: docker-compose -f docker-compose.prod.yml up -d

      - name: Run smoke tests
        run: |
          echo "Running smoke tests..."
          # Add smoke test commands here
          # Example: curl -f https://staging.example.com/health

      - name: Deploy to production
        if: success()
        run: |
          echo "Deploying to production environment..."
          # Add production deployment commands here

      - name: Notify deployment
        if: always()
        run: |
          echo "Deployment completed with status: ${{ job.status }}"
          # Add notification logic (Slack, email, etc.)

  # Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: docker
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Compose
        run: |
          cp .env.docker .env
          docker-compose up -d
          sleep 30  # Wait for services to start

      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Run performance tests
        run: |
          k6 run --out json=performance-results.json tests/performance/load-test.js
        continue-on-error: true

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results.json

      - name: Cleanup
        if: always()
        run: docker-compose down

  # Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy, performance]
    if: always()
    
    steps:
      - name: Clean up artifacts
        run: |
          echo "Cleaning up temporary artifacts..."
          # Add cleanup commands if needed

      - name: Generate summary report
        run: |
          echo "## CI/CD Pipeline Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY