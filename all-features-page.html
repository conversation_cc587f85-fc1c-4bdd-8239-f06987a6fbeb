<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Features & Options - HexStrike AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --secondary-color: #6b7280;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --border-color: #e2e8f0;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
        }

        [dir="rtl"] {
            direction: rtl;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .page {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 100;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            padding: 1rem;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .title {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .actions-row {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            align-items: center;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            background: var(--bg-primary);
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .filter-chips {
            display: flex;
            gap: 0.5rem;
        }

        .chip {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s;
            user-select: none;
        }

        .chip.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .chip:hover {
            background: var(--primary-hover);
            color: white;
        }

        .tag-multiselect {
            position: relative;
        }

        .tag-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
            max-height: 200px;
            overflow-y: auto;
            z-index: 50;
            display: none;
        }

        .tag-dropdown.open {
            display: block;
        }

        .tag-option {
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 0.875rem;
            border-bottom: 1px solid var(--border-color);
        }

        .tag-option:hover {
            background: var(--bg-secondary);
        }

        .tag-option.selected {
            background: var(--primary-color);
            color: white;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-height: 44px;
        }

        .btn:hover {
            background: var(--bg-tertiary);
            border-color: var(--secondary-color);
        }

        .btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        .main-content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
            width: 100%;
        }

        .group-section {
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-2xl);
            background: var(--bg-primary);
            box-shadow: var(--shadow-sm);
        }

        .group-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            user-select: none;
        }

        .group-header:hover {
            background: var(--bg-secondary);
        }

        .group-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .group-count {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-left: 0.5rem;
        }

        .group-actions {
            display: flex;
            gap: 0.5rem;
        }

        .group-content {
            padding: 1rem;
        }

        .group-content.collapsed {
            display: none;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .feature-row {
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            background: var(--bg-primary);
            transition: all 0.2s;
        }

        .feature-row:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .feature-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            cursor: pointer;
        }

        .checkbox-input {
            width: 1.25rem;
            height: 1.25rem;
            margin-top: 0.125rem;
            cursor: pointer;
        }

        .feature-info {
            flex: 1;
        }

        .feature-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .feature-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .feature-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-beta {
            background: var(--warning-color);
            color: white;
        }

        .badge-tag {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }

        .dependency-hint {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .dependency-hint.depends {
            color: var(--primary-color);
        }

        .dependency-hint.conflicts {
            color: var(--danger-color);
        }

        .footer-bar {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            border-top: 1px solid var(--border-color);
            padding: 1rem;
            display: none;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 -2px 4px rgb(0 0 0 / 0.1);
        }

        .selected-counter {
            font-weight: 600;
            color: var(--text-primary);
        }

        .toast {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--success-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.error {
            background: var(--danger-color);
        }

        .toast.warning {
            background: var(--warning-color);
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .actions-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: auto;
            }

            .filter-chips {
                justify-content: center;
            }

            .group-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .group-actions {
                width: 100%;
                justify-content: space-between;
            }

            .footer-bar {
                display: flex;
            }

            .main-content {
                padding-bottom: 5rem;
            }
        }

        /* RTL Support */
        [dir="rtl"] .actions-row {
            direction: rtl;
        }

        [dir="rtl"] .feature-checkbox {
            direction: rtl;
        }

        [dir="rtl"] .tag-dropdown {
            left: auto;
            right: 0;
        }

        [dir="rtl"] .toast {
            right: auto;
            left: 1rem;
            transform: translateX(-100%);
        }

        [dir="rtl"] .toast.show {
            transform: translateX(0);
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        :focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Virtual scrolling container */
        .virtual-container {
            height: 400px;
            overflow-y: auto;
        }

        .virtual-item {
            min-height: 80px;
        }
    </style>
</head>
<body>
    <div class="page" id="all_features">
        <!-- Sticky Header -->
        <header class="sticky-header" id="sticky_actions">
            <div class="header-content">
                <h1 class="title">All Features & Options</h1>
                <div class="actions-row">
                    <input 
                        type="text" 
                        class="search-input" 
                        id="search" 
                        placeholder="Search features, tags, keys…"
                        aria-label="Search features"
                    >
                    
                    <div class="filter-chips" id="status_filter" role="group" aria-label="Status filter">
                        <button class="chip active" data-status="all">All</button>
                        <button class="chip" data-status="enabled">Enabled</button>
                        <button class="chip" data-status="beta">Beta</button>
                        <button class="chip" data-status="disabled">Disabled</button>
                    </div>
                    
                    <div class="tag-multiselect" id="tag_filter">
                        <button class="btn" id="tag_filter_btn">Filter by Tags</button>
                        <div class="tag-dropdown" id="tag_dropdown"></div>
                    </div>
                    
                    <button class="btn" id="select_all_visible">Select All (Visible)</button>
                    <button class="btn" id="clear_all_visible">Clear (Visible)</button>
                    <button class="btn btn-primary" id="apply_selection">Apply Selection (0)</button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div id="groups" class="feature-groups"></div>
        </main>

        <!-- Mobile Footer -->
        <footer class="footer-bar" id="mobile_footer">
            <span class="selected-counter" id="selected_counter">0 selected</span>
            <button class="btn btn-primary" id="apply_selection_mobile">Apply</button>
        </footer>
    </div>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    <script>
        // Load features from API or fallback to static data
        async function loadFeatures() {
            try {
                const response = await fetch('/api/features/registry');
                if (response.ok) {
                    const data = await response.json();
                    return data.features || [];
                }
            } catch (error) {
                console.warn('Failed to load features from API, using fallback:', error);
            }
            
            // Static fallback features data
            return [
                {
                    "key": "scrape.pdf",
                    "label": "PDF Scraping",
                    "group": "Scraping",
                    "description": "Extract text and tables from PDF files.",
                    "tags": ["scraping", "files", "pdf"],
                    "default": false,
                    "dependsOn": ["auth.basic"],
                    "conflictsWith": ["scrape.realtime"],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "scrape.realtime",
                    "label": "Real-time Scraping",
                    "group": "Scraping",
                    "description": "Live data extraction with WebSocket connections.",
                    "tags": ["scraping", "realtime", "websocket"],
                    "default": false,
                    "dependsOn": ["auth.premium"],
                    "conflictsWith": ["scrape.pdf"],
                    "beta": true,
                    "disabled": false
                },
                {
                    "key": "scrape.web",
                    "label": "Web Scraping",
                    "group": "Scraping",
                    "description": "Extract data from websites and web applications.",
                    "tags": ["scraping", "web", "html"],
                    "default": true,
                    "dependsOn": ["auth.basic"],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "auth.basic",
                    "label": "Basic Authentication",
                    "group": "Authentication",
                    "description": "Standard username/password authentication.",
                    "tags": ["auth", "security", "basic"],
                    "default": true,
                    "dependsOn": [],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "auth.premium",
                    "label": "Premium Authentication",
                    "group": "Authentication",
                    "description": "Advanced authentication with 2FA and SSO.",
                    "tags": ["auth", "security", "premium", "2fa"],
                    "default": false,
                    "dependsOn": ["auth.basic"],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "security.waf",
                    "label": "Web Application Firewall",
                    "group": "Security",
                    "description": "Advanced protection against web attacks.",
                    "tags": ["security", "firewall", "protection"],
                    "default": false,
                    "dependsOn": ["auth.basic"],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "security.pentest",
                    "label": "Penetration Testing",
                    "group": "Security",
                    "description": "Automated security testing and vulnerability assessment.",
                    "tags": ["security", "testing", "vulnerability"],
                    "default": false,
                    "dependsOn": ["auth.premium"],
                    "conflictsWith": [],
                    "beta": true,
                    "disabled": false
                },
                {
                    "key": "ai.chat",
                    "label": "AI Chat Assistant",
                    "group": "AI Features",
                    "description": "Intelligent chat assistant for user support.",
                    "tags": ["ai", "chat", "assistant"],
                    "default": true,
                    "dependsOn": ["auth.basic"],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "ai.analysis",
                    "label": "AI Data Analysis",
                    "group": "AI Features",
                    "description": "Advanced AI-powered data analysis and insights.",
                    "tags": ["ai", "analysis", "insights"],
                    "default": false,
                    "dependsOn": ["auth.premium"],
                    "conflictsWith": [],
                    "beta": true,
                    "disabled": false
                },
                {
                    "key": "export.json",
                    "label": "JSON Export",
                    "group": "Export",
                    "description": "Export data in JSON format.",
                    "tags": ["export", "json", "data"],
                    "default": true,
                    "dependsOn": [],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "export.csv",
                    "label": "CSV Export",
                    "group": "Export",
                    "description": "Export data in CSV format for spreadsheet applications.",
                    "tags": ["export", "csv", "spreadsheet"],
                    "default": false,
                    "dependsOn": [],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": false
                },
                {
                    "key": "export.pdf",
                    "label": "PDF Export",
                    "group": "Export",
                    "description": "Generate PDF reports and documents.",
                    "tags": ["export", "pdf", "reports"],
                    "default": false,
                    "dependsOn": ["auth.basic"],
                    "conflictsWith": [],
                    "beta": false,
                    "disabled": true
                }
            ];
        }

        // Application State
        let state = {
            search: "",
            filters: {
                tags: [],
                status: "all"
            },
            expandedGroups: {},
            selected: {},
            features: [],
            counts: {
                total: 0,
                visible: 0,
                selectedVisible: 0,
                selectedTotal: 0
            }
        };

        // Debounce utility
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Toast notification system
        function showToast(message, type = 'success') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            container.appendChild(toast);
            
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => container.removeChild(toast), 300);
            }, 3000);
        }

        // Initialize application
        async function initApp() {
            try {
                // Load features from API or fallback
                const featuresData = await loadFeatures();
                state.features = featuresData;
            } catch (error) {
                console.error('Failed to initialize features:', error);
                return;
            }

            // Normalize features and set defaults
            state.features = state.features.map(feature => ({
                ...feature,
                dependsOn: feature.dependsOn || [],
                conflictsWith: feature.conflictsWith || [],
                tags: feature.tags || [],
                beta: feature.beta || false,
                disabled: feature.disabled || false
            }));

            // Load user profile preferences (from localStorage)
            const userProfile = JSON.parse(localStorage.getItem('userProfile') || '{}');
            const savedSelections = userProfile.selectedFeatures || {};

            // Set initial selections from user profile or defaults
            state.features.forEach(feature => {
                if (savedSelections.hasOwnProperty(feature.key)) {
                    state.selected[feature.key] = savedSelections[feature.key];
                } else {
                    state.selected[feature.key] = feature.default;
                }
            });

            // Initialize expanded groups (all expanded by default)
            const groups = [...new Set(state.features.map(f => f.group))];
            groups.forEach(group => {
                state.expandedGroups[group] = true;
            });

            // Setup event listeners
            setupEventListeners();
            
            // Initial render
            updateCounts();
            renderFeatures();
            renderTagFilter();
            updateUI();
        }

        // Setup all event listeners
        function setupEventListeners() {
            // Search input with debounce
            const searchInput = document.getElementById('search');
            searchInput.addEventListener('input', debounce((e) => {
                state.search = e.target.value.toLowerCase();
                updateCounts();
                renderFeatures();
                updateUI();
            }, 180));

            // Status filter chips
            document.getElementById('status_filter').addEventListener('click', (e) => {
                if (e.target.classList.contains('chip')) {
                    // Update active chip
                    document.querySelectorAll('#status_filter .chip').forEach(chip => {
                        chip.classList.remove('active');
                    });
                    e.target.classList.add('active');
                    
                    state.filters.status = e.target.dataset.status;
                    updateCounts();
                    renderFeatures();
                    updateUI();
                }
            });

            // Tag filter dropdown
            const tagFilterBtn = document.getElementById('tag_filter_btn');
            const tagDropdown = document.getElementById('tag_dropdown');
            
            tagFilterBtn.addEventListener('click', () => {
                tagDropdown.classList.toggle('open');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.tag-multiselect')) {
                    tagDropdown.classList.remove('open');
                }
            });

            // Bulk actions
            document.getElementById('select_all_visible').addEventListener('click', selectAllVisible);
            document.getElementById('clear_all_visible').addEventListener('click', clearAllVisible);
            document.getElementById('apply_selection').addEventListener('click', applySelection);
            document.getElementById('apply_selection_mobile').addEventListener('click', applySelection);

            // Keyboard navigation
            document.addEventListener('keydown', handleKeyboardNavigation);
        }

        // Filter features based on current state
        function getFilteredFeatures() {
            return state.features.filter(feature => {
                // Search filter
                if (state.search) {
                    const searchTerm = state.search.toLowerCase();
                    const searchableText = [
                        feature.label,
                        feature.description,
                        feature.key,
                        ...feature.tags
                    ].join(' ').toLowerCase();
                    
                    if (!searchableText.includes(searchTerm)) {
                        return false;
                    }
                }

                // Status filter
                if (state.filters.status !== 'all') {
                    switch (state.filters.status) {
                        case 'enabled':
                            if (!state.selected[feature.key]) return false;
                            break;
                        case 'beta':
                            if (!feature.beta) return false;
                            break;
                        case 'disabled':
                            if (!feature.disabled) return false;
                            break;
                    }
                }

                // Tag filter
                if (state.filters.tags.length > 0) {
                    const hasMatchingTag = state.filters.tags.some(tag => 
                        feature.tags.includes(tag)
                    );
                    if (!hasMatchingTag) return false;
                }

                return true;
            });
        }

        // Group filtered features
        function getGroupedFeatures() {
            const filtered = getFilteredFeatures();
            const grouped = {};
            
            filtered.forEach(feature => {
                if (!grouped[feature.group]) {
                    grouped[feature.group] = [];
                }
                grouped[feature.group].push(feature);
            });
            
            return grouped;
        }

        // Update counts
        function updateCounts() {
            const filtered = getFilteredFeatures();
            const selectedTotal = Object.values(state.selected).filter(Boolean).length;
            const selectedVisible = filtered.filter(f => state.selected[f.key]).length;
            
            state.counts = {
                total: state.features.length,
                visible: filtered.length,
                selectedVisible,
                selectedTotal
            };
        }

        // Render tag filter dropdown
        function renderTagFilter() {
            const dropdown = document.getElementById('tag_dropdown');
            const allTags = [...new Set(state.features.flatMap(f => f.tags))].sort();
            
            dropdown.innerHTML = allTags.map(tag => `
                <div class="tag-option ${state.filters.tags.includes(tag) ? 'selected' : ''}" 
                     data-tag="${tag}">
                    ${tag}
                </div>
            `).join('');
            
            // Add click handlers for tag options
            dropdown.addEventListener('click', (e) => {
                if (e.target.classList.contains('tag-option')) {
                    const tag = e.target.dataset.tag;
                    const index = state.filters.tags.indexOf(tag);
                    
                    if (index > -1) {
                        state.filters.tags.splice(index, 1);
                        e.target.classList.remove('selected');
                    } else {
                        state.filters.tags.push(tag);
                        e.target.classList.add('selected');
                    }
                    
                    updateCounts();
                    renderFeatures();
                    updateUI();
                }
            });
        }

        // Render features grouped by category
        function renderFeatures() {
            const container = document.getElementById('groups');
            const grouped = getGroupedFeatures();
            
            // Check if we need virtualization (>250 total features)
            const shouldVirtualize = state.features.length > 250;
            
            container.innerHTML = Object.entries(grouped).map(([groupName, features]) => {
                const selectedCount = features.filter(f => state.selected[f.key]).length;
                const isExpanded = state.expandedGroups[groupName];
                
                return `
                    <div class="group-section" role="group" aria-labelledby="group-${groupName}">
                        <div class="group-header" onclick="toggleGroup('${groupName}')" 
                             role="button" tabindex="0" 
                             aria-expanded="${isExpanded}" 
                             aria-controls="content-${groupName}">
                            <div>
                                <h2 class="group-title" id="group-${groupName}">${groupName}</h2>
                                <span class="group-count">${selectedCount}/${features.length}</span>
                            </div>
                            <div class="group-actions" onclick="event.stopPropagation()">
                                <button class="btn" onclick="selectGroup('${groupName}')" 
                                        aria-label="Select all in ${groupName}">
                                    Select Group
                                </button>
                                <button class="btn" onclick="clearGroup('${groupName}')" 
                                        aria-label="Clear all in ${groupName}">
                                    Clear Group
                                </button>
                            </div>
                        </div>
                        <div class="group-content ${!isExpanded ? 'collapsed' : ''}" 
                             id="content-${groupName}">
                            <div class="feature-list ${shouldVirtualize ? 'virtual-container' : ''}">
                                ${features.map(feature => renderFeatureRow(feature)).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Render individual feature row
        function renderFeatureRow(feature) {
            const isSelected = state.selected[feature.key];
            const dependencyText = feature.dependsOn.length > 0 ? 
                `Depends on: ${feature.dependsOn.join(', ')}` : '';
            const conflictText = feature.conflictsWith.length > 0 ? 
                `Conflicts with: ${feature.conflictsWith.join(', ')}` : '';
            
            return `
                <div class="feature-row virtual-item" data-key="${feature.key}">
                    <label class="feature-checkbox" for="feature-${feature.key}">
                        <input type="checkbox" 
                               class="checkbox-input" 
                               id="feature-${feature.key}"
                               ${isSelected ? 'checked' : ''}
                               ${feature.disabled ? 'disabled' : ''}
                               onchange="toggleFeature('${feature.key}')"
                               aria-describedby="desc-${feature.key}">
                        <div class="feature-info">
                            <div class="feature-label">${feature.label}</div>
                            <div class="feature-description" id="desc-${feature.key}">
                                ${feature.description}
                            </div>
                            <div class="feature-badges">
                                ${feature.beta ? '<span class="badge badge-beta">Beta</span>' : ''}
                                ${feature.tags.map(tag => 
                                    `<span class="badge badge-tag">${tag}</span>`
                                ).join('')}
                            </div>
                            ${dependencyText ? 
                                `<div class="dependency-hint depends">${dependencyText}</div>` : ''}
                            ${conflictText ? 
                                `<div class="dependency-hint conflicts">${conflictText}</div>` : ''}
                        </div>
                    </label>
                </div>
            `;
        }

        // Toggle group expansion
        function toggleGroup(groupName) {
            state.expandedGroups[groupName] = !state.expandedGroups[groupName];
            renderFeatures();
        }

        // Select all features in a group
        function selectGroup(groupName) {
            const grouped = getGroupedFeatures();
            const features = grouped[groupName] || [];
            
            features.forEach(feature => {
                if (!feature.disabled) {
                    enableFeatureWithDependencies(feature.key);
                }
            });
            
            updateCounts();
            renderFeatures();
            updateUI();
            showToast(`Selected all features in ${groupName}`);
        }

        // Clear all features in a group
        function clearGroup(groupName) {
            const grouped = getGroupedFeatures();
            const features = grouped[groupName] || [];
            
            features.forEach(feature => {
                state.selected[feature.key] = false;
            });
            
            updateCounts();
            renderFeatures();
            updateUI();
            showToast(`Cleared all features in ${groupName}`);
        }

        // Select all visible features
        function selectAllVisible() {
            const filtered = getFilteredFeatures();
            const conflicts = [];
            
            filtered.forEach(feature => {
                if (!feature.disabled) {
                    // Check for conflicts
                    const hasConflicts = feature.conflictsWith.some(conflictKey => 
                        state.selected[conflictKey]
                    );
                    
                    if (hasConflicts) {
                        conflicts.push(feature.label);
                    } else {
                        enableFeatureWithDependencies(feature.key);
                    }
                }
            });
            
            if (conflicts.length > 0) {
                const proceed = confirm(
                    `The following features have conflicts and will be skipped:\n${conflicts.join('\n')}\n\nContinue?`
                );
                if (!proceed) return;
            }
            
            updateCounts();
            renderFeatures();
            updateUI();
            showToast(`Selected ${filtered.length - conflicts.length} visible features`);
        }

        // Clear all visible features
        function clearAllVisible() {
            const filtered = getFilteredFeatures();
            
            filtered.forEach(feature => {
                state.selected[feature.key] = false;
            });
            
            updateCounts();
            renderFeatures();
            updateUI();
            showToast(`Cleared ${filtered.length} visible features`);
        }

        // Toggle individual feature
        function toggleFeature(key) {
            const feature = state.features.find(f => f.key === key);
            if (!feature || feature.disabled) return;
            
            const isCurrentlySelected = state.selected[key];
            
            if (!isCurrentlySelected) {
                // Enabling feature
                enableFeatureWithDependencies(key);
            } else {
                // Disabling feature
                disableFeatureWithDependents(key);
            }
            
            updateCounts();
            renderFeatures();
            updateUI();
        }

        // Enable feature and its dependencies
        function enableFeatureWithDependencies(key) {
            const feature = state.features.find(f => f.key === key);
            if (!feature) return;
            
            // Check for conflicts
            const conflicts = feature.conflictsWith.filter(conflictKey => 
                state.selected[conflictKey]
            );
            
            if (conflicts.length > 0) {
                const conflictLabels = conflicts.map(ck => 
                    state.features.find(f => f.key === ck)?.label || ck
                );
                
                const proceed = confirm(
                    `"${feature.label}" conflicts with:\n${conflictLabels.join('\n')}\n\nDisable conflicting features?`
                );
                
                if (proceed) {
                    conflicts.forEach(ck => {
                        state.selected[ck] = false;
                    });
                } else {
                    return;
                }
            }
            
            // Enable the feature
            state.selected[key] = true;
            
            // Auto-enable dependencies
            const enabledDeps = [];
            feature.dependsOn.forEach(depKey => {
                if (!state.selected[depKey]) {
                    const depFeature = state.features.find(f => f.key === depKey);
                    if (depFeature && !depFeature.disabled) {
                        state.selected[depKey] = true;
                        enabledDeps.push(depFeature.label);
                    }
                }
            });
            
            if (enabledDeps.length > 0) {
                showToast(`Enabled dependencies: ${enabledDeps.join(', ')}`);
            }
        }

        // Disable feature and optionally its dependents
        function disableFeatureWithDependents(key) {
            const feature = state.features.find(f => f.key === key);
            if (!feature) return;
            
            // Find features that depend on this one
            const dependents = state.features.filter(f => 
                f.dependsOn.includes(key) && state.selected[f.key]
            );
            
            if (dependents.length > 0) {
                const dependentLabels = dependents.map(f => f.label);
                const proceed = confirm(
                    `Disabling "${feature.label}" will also disable:\n${dependentLabels.join('\n')}\n\nContinue?`
                );
                
                if (proceed) {
                    dependents.forEach(dep => {
                        state.selected[dep.key] = false;
                    });
                } else {
                    return;
                }
            }
            
            state.selected[key] = false;
        }

        // Apply selection and return result
        function applySelection() {
            const selectedKeys = Object.keys(state.selected).filter(k => state.selected[k]);
            const selectedByGroup = {};
            
            // Group selected features
            selectedKeys.forEach(key => {
                const feature = state.features.find(f => f.key === key);
                if (feature) {
                    if (!selectedByGroup[feature.group]) {
                        selectedByGroup[feature.group] = [];
                    }
                    selectedByGroup[feature.group].push(key);
                }
            });
            
            const result = {
                selectedKeys,
                selectedByGroup,
                count: selectedKeys.length,
                timestamp: new Date().toISOString()
            };
            
            // Persist to sessionStorage
            sessionStorage.setItem('featureSelection', JSON.stringify(result));
            
            // Also save to user profile in localStorage
            const userProfile = JSON.parse(localStorage.getItem('userProfile') || '{}');
            userProfile.selectedFeatures = state.selected;
            localStorage.setItem('userProfile', JSON.stringify(userProfile));
            
            // Emit result (could be sent to parent window or API)
            console.log('Feature Selection Applied:', result);
            
            // Show success message
            showToast(`Applied selection: ${selectedKeys.length} features selected`);
            
            // Dispatch custom event for integration
            window.dispatchEvent(new CustomEvent('featuresApplied', { detail: result }));
            
            return result;
        }

        // Update UI elements
        function updateUI() {
            // Update apply button text
            const applyBtn = document.getElementById('apply_selection');
            const applyBtnMobile = document.getElementById('apply_selection_mobile');
            const selectedCounter = document.getElementById('selected_counter');
            
            const selectedText = `Apply Selection (${state.counts.selectedTotal})`;
            const counterText = `${state.counts.selectedTotal} selected`;
            
            applyBtn.textContent = selectedText;
            selectedCounter.textContent = counterText;
            
            // Update tag filter button
            const tagFilterBtn = document.getElementById('tag_filter_btn');
            const tagCount = state.filters.tags.length;
            tagFilterBtn.textContent = tagCount > 0 ? 
                `Filter by Tags (${tagCount})` : 'Filter by Tags';
        }

        // Keyboard navigation
        function handleKeyboardNavigation(e) {
            // Implement keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'a':
                        e.preventDefault();
                        selectAllVisible();
                        break;
                    case 'k':
                        e.preventDefault();
                        document.getElementById('search').focus();
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                // Close any open dropdowns
                document.getElementById('tag_dropdown').classList.remove('open');
            }
        }

        // RTL support
        function toggleRTL() {
            const html = document.documentElement;
            const currentDir = html.getAttribute('dir');
            html.setAttribute('dir', currentDir === 'rtl' ? 'ltr' : 'rtl');
        }

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', initApp);
        
        // Make functions globally available
        window.toggleGroup = toggleGroup;
        window.selectGroup = selectGroup;
        window.clearGroup = clearGroup;
        window.toggleFeature = toggleFeature;
        window.toggleRTL = toggleRTL;
    </script>
</body>
</html>