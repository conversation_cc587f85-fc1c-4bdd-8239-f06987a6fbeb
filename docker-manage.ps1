#!/usr/bin/env pwsh
# HexStrike AI Docker Management Script
# PowerShell script for managing Docker containers on Windows

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet('build', 'start', 'stop', 'restart', 'logs', 'status', 'clean', 'setup', 'backup', 'restore')]
    [string]$Action,
    
    [string]$Service = 'all',
    [switch]$Follow,
    [switch]$Force,
    [string]$BackupPath = './backups'
)

# Color output functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }

# Check if Docker is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    } catch {
        Write-Error "Docker is not running or not installed"
        return $false
    }
}

# Check if .env file exists
function Test-EnvFile {
    if (-not (Test-Path ".env")) {
        Write-Warning ".env file not found. Creating from template..."
        if (Test-Path ".env.docker") {
            Copy-Item ".env.docker" ".env"
            Write-Info "Please edit .env file with your secure values before starting services"
            return $false
        } else {
            Write-Error ".env.docker template not found"
            return $false
        }
    }
    return $true
}

# Generate SSL certificates for development
function New-SSLCertificates {
    Write-Info "Generating self-signed SSL certificates for development..."
    
    if (-not (Test-Path "ssl")) {
        New-Item -ItemType Directory -Path "ssl" | Out-Null
    }
    
    # Generate private key
    openssl genrsa -out ssl/key.pem 2048 2>$null
    
    # Generate certificate
    openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 -subj "/C=US/ST=State/L=City/O=HexStrike/CN=localhost" 2>$null
    
    if (Test-Path "ssl/cert.pem" -and Test-Path "ssl/key.pem") {
        Write-Success "SSL certificates generated successfully"
    } else {
        Write-Warning "Failed to generate SSL certificates. HTTPS may not work."
    }
}

# Setup function
function Invoke-Setup {
    Write-Info "Setting up HexStrike AI Docker environment..."
    
    if (-not (Test-DockerRunning)) { return }
    
    # Check/create .env file
    if (-not (Test-EnvFile)) {
        Write-Warning "Please configure .env file and run setup again"
        return
    }
    
    # Generate SSL certificates
    New-SSLCertificates
    
    # Create necessary directories
    $directories = @('logs', 'uploads', 'scans', 'backups')
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir | Out-Null
            Write-Info "Created directory: $dir"
        }
    }
    
    # Build images
    Write-Info "Building Docker images..."
    docker-compose build --no-cache
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Setup completed successfully!"
        Write-Info "You can now start the services with: ./docker-manage.ps1 start"
    } else {
        Write-Error "Setup failed during image build"
    }
}

# Build function
function Invoke-Build {
    Write-Info "Building Docker images..."
    
    if (-not (Test-DockerRunning)) { return }
    
    $buildArgs = @('build')
    if ($Force) { $buildArgs += '--no-cache' }
    if ($Service -ne 'all') { $buildArgs += $Service }
    
    docker-compose @buildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Build completed successfully"
    } else {
        Write-Error "Build failed"
    }
}

# Start function
function Invoke-Start {
    Write-Info "Starting HexStrike AI services..."
    
    if (-not (Test-DockerRunning)) { return }
    if (-not (Test-EnvFile)) { return }
    
    $startArgs = @('up', '-d')
    if ($Service -ne 'all') { $startArgs += $Service }
    
    docker-compose @startArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Services started successfully"
        Start-Sleep -Seconds 5
        Invoke-Status
    } else {
        Write-Error "Failed to start services"
    }
}

# Stop function
function Invoke-Stop {
    Write-Info "Stopping HexStrike AI services..."
    
    if (-not (Test-DockerRunning)) { return }
    
    $stopArgs = @('down')
    if ($Force) { $stopArgs += '--remove-orphans' }
    
    docker-compose @stopArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Services stopped successfully"
    } else {
        Write-Error "Failed to stop services"
    }
}

# Restart function
function Invoke-Restart {
    Write-Info "Restarting HexStrike AI services..."
    Invoke-Stop
    Start-Sleep -Seconds 3
    Invoke-Start
}

# Logs function
function Invoke-Logs {
    if (-not (Test-DockerRunning)) { return }
    
    $logArgs = @('logs')
    if ($Follow) { $logArgs += '-f' }
    if ($Service -ne 'all') { $logArgs += $Service }
    
    docker-compose @logArgs
}

# Status function
function Invoke-Status {
    Write-Info "HexStrike AI Service Status:"
    
    if (-not (Test-DockerRunning)) { return }
    
    docker-compose ps
    
    Write-Info "`nContainer Health Status:"
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}" --filter "name=hexstrike"
    Write-Host $containers
    
    # Check service endpoints
    Write-Info "`nService Endpoints:"
    Write-Host "Web Application: https://localhost" -ForegroundColor Green
    Write-Host "API Health: https://localhost/health" -ForegroundColor Green
    Write-Host "Database: localhost:5432" -ForegroundColor Yellow
    Write-Host "Redis: localhost:6379" -ForegroundColor Yellow
}

# Clean function
function Invoke-Clean {
    Write-Warning "This will remove all containers, images, and volumes. Are you sure? (y/N)"
    $confirmation = Read-Host
    
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        Write-Info "Cleaning up Docker resources..."
        
        # Stop and remove containers
        docker-compose down --remove-orphans
        
        # Remove images
        docker images --filter "reference=hexstrike*" --format "{{.ID}}" | ForEach-Object {
            docker rmi $_ --force
        }
        
        # Remove volumes if force flag is used
        if ($Force) {
            docker volume ls --filter "name=hexstrike" --format "{{.Name}}" | ForEach-Object {
                docker volume rm $_ --force
            }
        }
        
        Write-Success "Cleanup completed"
    } else {
        Write-Info "Cleanup cancelled"
    }
}

# Backup function
function Invoke-Backup {
    Write-Info "Creating backup of HexStrike AI data..."
    
    if (-not (Test-Path $BackupPath)) {
        New-Item -ItemType Directory -Path $BackupPath | Out-Null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath/hexstrike_backup_$timestamp.sql"
    
    # Backup database
    docker-compose exec -T postgres pg_dump -U hexstrike hexstrike_ai > $backupFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database backup created: $backupFile"
    } else {
        Write-Error "Database backup failed"
    }
}

# Restore function
function Invoke-Restore {
    param([string]$BackupFile)
    
    if (-not $BackupFile) {
        Write-Error "Please specify backup file with -BackupFile parameter"
        return
    }
    
    if (-not (Test-Path $BackupFile)) {
        Write-Error "Backup file not found: $BackupFile"
        return
    }
    
    Write-Warning "This will restore database from backup. Existing data will be lost. Continue? (y/N)"
    $confirmation = Read-Host
    
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        Write-Info "Restoring database from backup..."
        
        # Restore database
        Get-Content $BackupFile | docker-compose exec -T postgres psql -U hexstrike hexstrike_ai
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database restored successfully"
        } else {
            Write-Error "Database restore failed"
        }
    } else {
        Write-Info "Restore cancelled"
    }
}

# Main execution
switch ($Action) {
    'setup' { Invoke-Setup }
    'build' { Invoke-Build }
    'start' { Invoke-Start }
    'stop' { Invoke-Stop }
    'restart' { Invoke-Restart }
    'logs' { Invoke-Logs }
    'status' { Invoke-Status }
    'clean' { Invoke-Clean }
    'backup' { Invoke-Backup }
    'restore' { Invoke-Restore }
    default { Write-Error "Unknown action: $Action" }
}