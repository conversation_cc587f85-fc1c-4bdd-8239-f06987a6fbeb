# Security Tools Installation Guide

## Overview
This guide documents the process of installing and configuring security tools for the HexStrike AI platform on Windows systems.

## Prerequisites
- Windows 10/11 with PowerShell 5.1 or later
- Administrator privileges
- Internet connection

## Installation Process

### 1. Package Manager Setup
The installation script automatically sets up the following package managers:
- **Chocolatey**: For Windows applications and tools
- **Scoop**: For portable applications and development tools
- **pip**: For Python-based security tools
- **npm**: For Node.js-based tools
- **Go modules**: For Go-based tools
- **Cargo**: For Rust-based tools

### 2. Security Tools Categories
The platform supports 127 security tools across multiple categories:

#### Essential Tools
- nmap, gobuster, nuclei, sqlmap, nikto
- burpsuite, dirb, hydra, john, hashcat

#### Network Tools
- wireshark, tcpdump, tshark, aircrack-ng suite
- masscan, rustscan, zmap

#### Web Security Tools
- owasp-zap, wfuzz, ffuf, feroxbuster
- wafw00f, wpscan, xsser

#### Vulnerability Scanning
- nessus, openvas, trivy, bandit
- semgrep, codeql, snyk

#### Password Tools
- hashcat, john, hydra, medusa
- crunch, cewl, cupp

#### Binary Analysis
- ghidra, radare2, ida-free, binwalk
- strings, objdump, readelf

#### Forensics Tools
- volatility, sleuthkit, autopsy
- foremost, scalpel, testdisk

### 3. Installation Script Usage

```powershell
# Run the installation script
.\install_security_tools.ps1

# Check installation status
Invoke-RestMethod -Uri 'http://localhost:8888/health' -Method GET
```

### 4. PATH Configuration Issue

#### Problem
The Python server process may not inherit the updated PATH environment variable after tool installation, causing tools to appear as unavailable even when properly installed.

#### Solution
Restart the server with an explicitly updated PATH:

```powershell
# Stop the current server
# Then restart with updated PATH
$env:PATH = "C:\Program Files (x86)\Nmap;" + $env:PATH
python chat_box_server.py --port 8888
```

#### Verification
After restarting the server with the updated PATH:
- Check the health endpoint: `GET /health`
- Verify tool detection in the `tools_status` field
- Test individual tools via API endpoints

### 5. Tool Detection Mechanism
The platform uses cross-platform tool detection:
- **Windows**: Uses `where` command
- **Unix/Linux**: Uses `which` command
- **Fallback**: Checks common installation paths

### 6. Troubleshooting

#### Tools Not Detected
1. Verify tool installation: `where <tool_name>`
2. Check PATH environment variable
3. Restart server with updated PATH
4. Check tool permissions and execution rights

#### Installation Failures
1. Run PowerShell as Administrator
2. Check internet connectivity
3. Verify package manager installation
4. Review installation logs for specific errors

#### Performance Issues
1. Install tools in batches to avoid system overload
2. Monitor disk space during installation
3. Consider using SSD for better performance

### 7. Maintenance

#### Regular Updates
```powershell
# Update Chocolatey packages
choco upgrade all

# Update Scoop packages
scoop update *

# Update Python packages
pip list --outdated | ForEach-Object { pip install --upgrade $_.Split()[0] }
```

#### Health Monitoring
- Regular health endpoint checks
- Monitor tool availability statistics
- Track installation success rates

### 8. Security Considerations
- Run installations with appropriate privileges
- Verify tool signatures when possible
- Keep tools updated for security patches
- Monitor for deprecated or vulnerable tools

### 9. Platform Integration
The installed tools integrate with the HexStrike AI platform through:
- REST API endpoints for each tool
- Standardized input/output formats
- Error handling and logging
- Security sandboxing and validation

### 10. Support and Resources
- Platform documentation: `/docs`
- Health endpoint: `/health`
- Tool status monitoring: `/api/tools/status`
- Community support: GitHub issues

## Conclusion
This installation process ensures comprehensive security tool availability for the HexStrike AI platform. The automated script handles most scenarios, with manual intervention required only for PATH configuration and troubleshooting specific tool installations.

For additional support or custom tool requirements, refer to the platform documentation or contact the development team.