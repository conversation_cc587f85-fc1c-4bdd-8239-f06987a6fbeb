<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - API Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background: #28a745; color: white; }
        .post { background: #007bff; color: white; }
        .delete { background: #dc3545; color: white; }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .status {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ HexStrike AI - API Documentation</h1>
        
        <div class="success">
            <strong>✅ Server Status:</strong> Running on <code>http://localhost:8888</code>
        </div>
        
        <div class="error">
            <strong>❌ Common Error:</strong> <code>/api</code> endpoint doesn't exist. You must use specific endpoints listed below.
        </div>

        <h2>🔐 Authentication Endpoints</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/auth/login</span> - User login
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/auth/register</span> - User registration
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/auth/verify</span> - Verify authentication
        </div>

        <h2>💬 Chat & AI Endpoints</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/chat</span> - Send chat messages
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/ai/generate_payload</span> - Generate AI payloads
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/ai/test_payload</span> - Test AI payloads
        </div>

        <h2>⚙️ System & Settings</h2>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/features</span> - <span class="status">✅ Working</span> Get available features
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/settings</span> - Get settings
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/settings</span> - Update settings
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/health</span> - Health check
        </div>

        <h2>🔧 Security Tools</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/tools/nmap</span> - Network scanning
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/tools/nuclei</span> - Vulnerability scanning
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/tools/gobuster</span> - Directory enumeration
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/tools/sqlmap</span> - SQL injection testing
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/tools/nikto</span> - Web server scanning
        </div>

        <h2>🕵️ Intelligence & Analysis</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/intelligence/analyze-target</span> - Target analysis
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/intelligence/select-tools</span> - Tool selection
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/intelligence/smart-scan</span> - Smart scanning
        </div>

        <h2>🐛 Bug Bounty Workflows</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/bugbounty/reconnaissance-workflow</span> - Reconnaissance
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/bugbounty/vulnerability-hunting-workflow</span> - Vulnerability hunting
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/bugbounty/osint-workflow</span> - OSINT workflow
        </div>

        <h2>🏁 CTF Features</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/ctf/create-challenge-workflow</span> - Create CTF challenges
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/ctf/auto-solve-challenge</span> - Auto-solve challenges
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/ctf/cryptography-solver</span> - Cryptography solver
        </div>

        <h2>📊 Process Management</h2>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/processes/list</span> - List processes
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/api/processes/dashboard</span> - Process dashboard
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/api/process/execute-async</span> - Execute async processes
        </div>

        <h2>🔗 Quick Access Links</h2>
        <div class="endpoint">
            <a href="http://localhost:8888" target="_blank">🏠 Main Dashboard</a>
        </div>
        <div class="endpoint">
            <a href="http://localhost:8888/chat" target="_blank">💬 Chat Interface</a>
        </div>
        <div class="endpoint">
            <a href="http://localhost:8888/settings" target="_blank">⚙️ Settings Page</a>
        </div>
        <div class="endpoint">
            <a href="http://localhost:8888/health" target="_blank">❤️ Health Check</a>
        </div>

        <h2>📝 Usage Examples</h2>
        <div class="endpoint">
            <strong>PowerShell:</strong><br>
            <code>Invoke-WebRequest -Uri "http://localhost:8888/api/features" -Method GET</code>
        </div>
        <div class="endpoint">
            <strong>Browser:</strong><br>
            <code>http://localhost:8888/api/features</code>
        </div>
    </div>
</body>
</html>