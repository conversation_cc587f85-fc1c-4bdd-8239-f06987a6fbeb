import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

// Icon components
const GridIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
  </svg>
);

const ShieldIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
);

const NetworkIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
  </svg>
);

const ChartIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const iconMap = {
  grid: GridIcon,
  shield: ShieldIcon,
  network: NetworkIcon,
  chart: ChartIcon,
};

interface Tool {
  key: string;
  title: string;
  blurb: string;
  icon: keyof typeof iconMap;
}

const tools: Tool[] = [
  { key: 'scrape', title: 'Scrape', blurb: 'Extract clean data from pages & files.', icon: 'grid' },
  { key: 'test', title: 'Test', blurb: 'Run safe, allowlisted security checks.', icon: 'shield' },
  { key: 'crawl', title: 'Crawl', blurb: 'Map sites with depth & rules.', icon: 'network' },
  { key: 'analyze', title: 'Analyze', blurb: 'Turn raw data into insights.', icon: 'chart' },
];

const featuresMap = {
  scrape: [
    'Point-and-click selectors, auto-pagination',
    'File scraping: PDF, DOCX, XLSX, HTML',
    'Export to CSV/JSON, save to DB',
    'Auth support (username/password, token)',
  ],
  test: [
    'Allowlisted targets with safe presets',
    'Rate limits + audit logs',
    'OWASP quick checks',
    'Result diff & remediation tips',
  ],
  crawl: [
    'Depth, robots, and sitemaps aware',
    'Queue & retry policies',
    'Rule builder (include/exclude)',
    'Live progress with pause/resume',
  ],
  analyze: [
    'AI summaries & insight cards',
    'Entity/metric extraction',
    'Charts & downloadable reports',
    'Project reports',
  ],
};

interface ToolCardProps {
  tool: Tool;
  isActive: boolean;
  onClick: () => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
  tabIndex: number;
}

const ToolCard: React.FC<ToolCardProps> = ({ tool, isActive, onClick, onKeyDown, tabIndex }) => {
  const IconComponent = iconMap[tool.icon];
  
  return (
    <button
      className={cn(
        // Base styles
        'group relative p-6 rounded-2xl border transition-all duration-200 transform-gpu',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2',
        'text-left w-full',
        // RTL support
        'rtl:text-right',
        // Raised card states
        isActive
          ? 'shadow-inner border-slate-300 bg-slate-50 scale-[0.98]'
          : 'shadow-sm border-slate-200 bg-white hover:shadow-md hover:-translate-y-0.5 hover:border-slate-300',
        // Disabled state (if needed)
        'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-sm disabled:hover:translate-y-0'
      )}
      onClick={onClick}
      onKeyDown={onKeyDown}
      tabIndex={tabIndex}
      role="tab"
      aria-selected={isActive}
      aria-pressed={isActive}
    >
      {/* Icon container */}
      <div className={cn(
        'inline-flex items-center justify-center w-12 h-12 rounded-xl border border-slate-200 bg-white mb-4',
        'group-hover:border-slate-300 transition-colors duration-200',
        isActive && 'border-slate-300 bg-slate-50'
      )}>
        <IconComponent className="w-6 h-6 text-slate-700" />
      </div>
      
      {/* Content */}
      <div className="space-y-2">
        <h3 className="font-semibold text-slate-900 text-lg">
          {tool.title}
        </h3>
        <p className="text-slate-600 text-sm leading-relaxed">
          {tool.blurb}
        </p>
      </div>
    </button>
  );
};

const LandingPage: React.FC = () => {
  const [activeTool, setActiveTool] = useState<string>('scrape');
  const [primaryCta, setPrimaryCta] = useState('Start Free');

  // Handle tool selection
  const handleToolSelect = (toolKey: string) => {
    setActiveTool(toolKey);
    const tool = tools.find(t => t.key === toolKey);
    if (tool) {
      setPrimaryCta(`Start ${tool.title}`);
    }
  };

  // Keyboard navigation for tool picker
  const handleKeyDown = (e: React.KeyboardEvent, toolKey: string) => {
    const currentIndex = tools.findIndex(t => t.key === toolKey);
    let nextIndex = currentIndex;

    switch (e.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        nextIndex = (currentIndex + 1) % tools.length;
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        nextIndex = currentIndex === 0 ? tools.length - 1 : currentIndex - 1;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        handleToolSelect(toolKey);
        return;
    }

    if (nextIndex !== currentIndex) {
      const nextButton = document.querySelector(`[data-tool="${tools[nextIndex].key}"]`) as HTMLButtonElement;
      nextButton?.focus();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-slate-50" dir="ltr">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <h1 className="text-xl font-bold text-slate-900">HexStrike AI</h1>
            </div>
            
            {/* Navigation */}
            <nav className="hidden md:flex space-x-8 rtl:space-x-reverse">
              <a href="#" className="text-slate-600 hover:text-slate-900 transition-colors duration-200">Features</a>
              <a href="#" className="text-slate-600 hover:text-slate-900 transition-colors duration-200">Pricing</a>
              <a href="#" className="text-slate-600 hover:text-slate-900 transition-colors duration-200">FAQ</a>
            </nav>
            
            {/* CTA */}
            <button className="bg-slate-900 text-white px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2">
              Get Started
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-12">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 mb-6">
            Choose your tool. Get instant results.
          </h1>
          <p className="text-xl text-slate-600 mb-8 leading-relaxed">
            One workspace for scraping, safe testing, crawling, and AI analysis—built for speed and clarity.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-slate-900 text-white px-8 py-3 rounded-lg hover:bg-slate-800 transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 text-lg font-medium">
              {primaryCta}
            </button>
            <button className="border border-slate-300 text-slate-700 px-8 py-3 rounded-lg hover:bg-slate-50 transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 text-lg font-medium">
              Watch Demo
            </button>
          </div>
        </div>
      </section>

      {/* Tool Picker */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" role="tablist">
          {tools.map((tool, index) => (
            <ToolCard
              key={tool.key}
              tool={tool}
              isActive={activeTool === tool.key}
              onClick={() => handleToolSelect(tool.key)}
              onKeyDown={(e) => handleKeyDown(e, tool.key)}
              tabIndex={activeTool === tool.key ? 0 : -1}
              data-tool={tool.key}
            />
          ))}
        </div>
      </section>

      {/* Features Panel */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-2xl border border-slate-200 shadow-sm p-8">
          <h2 className="text-2xl font-bold text-slate-900 mb-6">
            {tools.find(t => t.key === activeTool)?.title} Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {featuresMap[activeTool as keyof typeof featuresMap]?.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                <div className="flex-shrink-0 w-2 h-2 bg-slate-400 rounded-full mt-2"></div>
                <p className="text-slate-600">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold">HexStrike AI</h3>
            </div>
            <nav className="flex space-x-6 rtl:space-x-reverse">
              <a href="#" className="text-slate-300 hover:text-white transition-colors duration-200">Privacy</a>
              <a href="#" className="text-slate-300 hover:text-white transition-colors duration-200">Terms</a>
              <a href="#" className="text-slate-300 hover:text-white transition-colors duration-200">Contact</a>
            </nav>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;