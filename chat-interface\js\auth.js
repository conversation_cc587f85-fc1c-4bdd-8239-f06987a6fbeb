/**
 * Authentication Module for Chat Box AI
 * Handles user authentication, session management, and user roles
 */

// Default users for demo purposes
const DEFAULT_USERS = [
    {
        username: 'admin',
        password: 'admin123',
        role: 'admin',
        email: '<EMAIL>',
        permissions: ['chat', 'admin', 'security']
    },
    {
        username: 'user',
        password: 'user123',
        role: 'user',
        email: '<EMAIL>',
        permissions: ['chat']
    },
    {
        username: 'guest',
        password: 'guest',
        role: 'guest',
        email: '<EMAIL>',
        permissions: ['chat']
    }
];

/**
 * Authenticate user with username and password
 * @param {string} username - User's username
 * @param {string} password - User's password
 * @returns {Object|null} User object if authentication successful, null otherwise
 */
export function authenticateUser(username, password) {
    // Check against default users
    const user = DEFAULT_USERS.find(u => 
        u.username === username && u.password === password
    );
    
    if (user) {
        // Create session user object (without password)
        const sessionUser = {
            username: user.username,
            role: user.role,
            email: user.email,
            permissions: user.permissions,
            loginTime: new Date().toISOString(),
            sessionId: generateSessionId()
        };
        
        console.log('User authenticated successfully:', sessionUser.username);
        return sessionUser;
    }
    
    console.log('Authentication failed for username:', username);
    return null;
}

/**
 * Store user session in sessionStorage (secure, tab-scoped)
 * @param {Object} user - User object to store
 * @param {string} accessToken - JWT access token
 * @param {string} refreshToken - JWT refresh token (optional)
 */
export function storeUserSession(user, accessToken, refreshToken = null) {
    try {
        sessionStorage.setItem('isLoggedIn', 'true');
        sessionStorage.setItem('username', user.username);
        sessionStorage.setItem('currentRole', user.role);
        sessionStorage.setItem('userEmail', user.email);
        sessionStorage.setItem('userPermissions', JSON.stringify(user.permissions));
        sessionStorage.setItem('loginTime', user.loginTime);
        sessionStorage.setItem('sessionId', user.sessionId);
        sessionStorage.setItem('access_token', accessToken);
        
        if (refreshToken) {
            sessionStorage.setItem('refresh_token', refreshToken);
        }
        
        console.log('User session stored successfully in sessionStorage');
    } catch (error) {
        console.error('Failed to store user session:', error);
    }
}

/**
 * Clear user session from sessionStorage
 */
export function clearUserSession() {
    try {
        sessionStorage.removeItem('isLoggedIn');
        sessionStorage.removeItem('username');
        sessionStorage.removeItem('currentRole');
        sessionStorage.removeItem('userEmail');
        sessionStorage.removeItem('userPermissions');
        sessionStorage.removeItem('loginTime');
        sessionStorage.removeItem('sessionId');
        sessionStorage.removeItem('access_token');
        sessionStorage.removeItem('refresh_token');
        
        console.log('User session cleared successfully from sessionStorage');
    } catch (error) {
        console.error('Failed to clear user session:', error);
    }
}

/**
 * Check if user has specific permission
 * @param {string} permission - Permission to check
 * @returns {boolean} True if user has permission, false otherwise
 */
export function hasPermission(permission) {
    try {
        const permissions = JSON.parse(sessionStorage.getItem('userPermissions') || '[]');
        return permissions.includes(permission);
    } catch (error) {
        console.error('Failed to check permission:', error);
        return false;
    }
}

/**
 * Get current user information
 * @returns {Object|null} Current user object or null if not logged in
 */
export function getCurrentUser() {
    try {
        const isLoggedIn = sessionStorage.getItem('isLoggedIn') === 'true';
        if (!isLoggedIn) return null;
        
        return {
            username: sessionStorage.getItem('username'),
            role: sessionStorage.getItem('currentRole'),
            email: sessionStorage.getItem('userEmail'),
            permissions: JSON.parse(sessionStorage.getItem('userPermissions') || '[]'),
            loginTime: sessionStorage.getItem('loginTime'),
            sessionId: sessionStorage.getItem('sessionId')
        };
    } catch (error) {
        console.error('Failed to get current user:', error);
        return null;
    }
}

/**
 * Validate session (check if session is still valid)
 * @returns {boolean} True if session is valid, false otherwise
 */
export function validateSession() {
    try {
        const isLoggedIn = sessionStorage.getItem('isLoggedIn') === 'true';
        const sessionId = sessionStorage.getItem('sessionId');
        const loginTime = sessionStorage.getItem('loginTime');
        const accessToken = sessionStorage.getItem('access_token');
        
        if (!isLoggedIn || !sessionId || !loginTime || !accessToken) {
            return false;
        }
        
        // Check if JWT token is expired
        if (isTokenExpired(accessToken)) {
            console.log('JWT token expired');
            clearUserSession();
            return false;
        }
        
        // Check if session is not older than 24 hours (fallback)
        const loginDate = new Date(loginTime);
        const now = new Date();
        const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
        
        if (hoursDiff > 24) {
            console.log('Session expired');
            clearUserSession();
            return false;
        }
        
        return true;
    } catch (error) {
        console.error('Failed to validate session:', error);
        return false;
    }
}

/**
 * Generate a unique session ID
 * @returns {string} Unique session ID
 */
function generateSessionId() {
    return 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Enable guest access (bypass login)
 * @returns {Object} Guest user object
 */
export function enableGuestAccess() {
    const guestUser = {
        username: 'Guest',
        role: 'guest',
        email: '<EMAIL>',
        permissions: ['chat'],
        loginTime: new Date().toISOString(),
        sessionId: generateSessionId()
    };
    
    storeUserSession(guestUser, 'guest_token');
    console.log('Guest access enabled');
    return guestUser;
}

/**
 * Get available demo users for testing
 * @returns {Array} Array of demo user credentials (without passwords)
 */
export function getDemoUsers() {
    return DEFAULT_USERS.map(user => ({
        username: user.username,
        role: user.role,
        email: user.email
    }));
}

/**
 * Get JWT access token from sessionStorage
 * @returns {string|null} JWT token or null if not found
 */
export function getAccessToken() {
    return sessionStorage.getItem('access_token');
}

/**
 * Get JWT refresh token from sessionStorage
 * @returns {string|null} Refresh token or null if not found
 */
export function getRefreshToken() {
    return sessionStorage.getItem('refresh_token');
}

/**
 * Check if JWT token is expired
 * @param {string} token - JWT token to check
 * @returns {boolean} True if token is expired, false otherwise
 */
export function isTokenExpired(token) {
    try {
        if (!token) return true;
        
        // Decode JWT payload (base64)
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        
        // Check if token is expired (exp claim)
        return payload.exp && payload.exp < currentTime;
    } catch (error) {
        console.error('Failed to decode JWT token:', error);
        return true;
    }
}

/**
 * Logout user and clear session
 * @param {boolean} callBackend - Whether to call backend logout endpoint
 * @returns {Promise<boolean>} True if logout successful
 */
export async function logout(callBackend = true) {
    try {
        const token = getAccessToken();
        
        if (callBackend && token) {
            // Call backend logout endpoint to blacklist token
            try {
                await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
            } catch (error) {
                console.warn('Failed to call backend logout:', error);
            }
        }
        
        // Clear session storage
        clearUserSession();
        
        console.log('User logged out successfully');
        return true;
    } catch (error) {
        console.error('Logout failed:', error);
        return false;
    }
}

/**
 * Refresh JWT access token using refresh token
 * @returns {Promise<string|null>} New access token or null if refresh failed
 */
export async function refreshAccessToken() {
    try {
        const refreshToken = getRefreshToken();
        
        if (!refreshToken) {
            console.log('No refresh token available');
            return null;
        }
        
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${refreshToken}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.access_token) {
                sessionStorage.setItem('access_token', data.access_token);
                console.log('Access token refreshed successfully');
                return data.access_token;
            }
        }
        
        console.log('Token refresh failed');
        return null;
    } catch (error) {
        console.error('Token refresh error:', error);
        return null;
    }
}

/**
 * Make authenticated API request with automatic token refresh
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} Fetch response
 */
export async function authenticatedFetch(url, options = {}) {
    let token = getAccessToken();
    
    // Check if token is expired and try to refresh
    if (!token || isTokenExpired(token)) {
        token = await refreshAccessToken();
        
        if (!token) {
            // Redirect to login if refresh failed
            await logout(false);
            throw new Error('Authentication required');
        }
    }
    
    // Add Authorization header
    const headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
    
    const response = await fetch(url, {
        ...options,
        headers
    });
    
    // Handle 401 Unauthorized - token might be invalid
    if (response.status === 401) {
        // Try to refresh token once
        const newToken = await refreshAccessToken();
        
        if (newToken) {
            // Retry request with new token
            headers['Authorization'] = `Bearer ${newToken}`;
            return fetch(url, {
                ...options,
                headers
            });
        } else {
            // Refresh failed, logout user
            await logout(false);
            throw new Error('Authentication required');
        }
    }
    
    return response;
}