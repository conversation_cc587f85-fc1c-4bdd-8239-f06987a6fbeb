<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Select Your Capability</title>
    <style>
        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #f0f9ff, #ffffff, #f3e8ff);
            color: #333;
            min-height: 100vh;
            transition: background 0.3s ease, color 0.3s ease;
        }
        
        /* Dark mode */
        body.dark {
            background: linear-gradient(135deg, #111827, #1f2937, #4c1d95);
            color: #f3f4f6;
        }
        
        /* Header styles */
        header {
            background-color: rgba(255, 255, 255, 0.8);
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 50;
            backdrop-filter: blur(10px);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        
        body.dark header {
            background-color: rgba(31, 41, 55, 0.8);
            border-bottom-color: #374151;
        }
        
        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(to right, #3b82f6, #8b5cf6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
        }
        
        .logo-text h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
            color: #111827;
            transition: color 0.3s ease;
        }
        
        body.dark .logo-text h1 {
            color: #f3f4f6;
        }
        
        .logo-text p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
            transition: color 0.3s ease;
        }
        
        body.dark .logo-text p {
            color: #9ca3af;
        }
        
        .dark-mode-toggle {
            background: #f3f4f6;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        body.dark .dark-mode-toggle {
            background: #374151;
        }
        
        .dark-mode-toggle svg {
            width: 20px;
            height: 20px;
            color: #4b5563;
            transition: color 0.3s ease;
        }
        
        body.dark .dark-mode-toggle svg {
            color: #e5e7eb;
        }
        
        /* Main content styles */
        main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .hero h2 {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #111827;
            transition: color 0.3s ease;
        }
        
        body.dark .hero h2 {
            color: #f3f4f6;
        }
        
        .hero p {
            font-size: 18px;
            color: #4b5563;
            max-width: 800px;
            margin: 0 auto;
            transition: color 0.3s ease;
        }
        
        body.dark .hero p {
            color: #9ca3af;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Card grid */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .capability-card {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            animation: slideUp 0.6s ease-out;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        body.dark .capability-card {
            background: rgba(31, 41, 55, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .capability-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .capability-card.pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite;
        }
        
        .card-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-icon svg {
            width: 32px;
            height: 32px;
            color: white;
        }
        
        .capability-card h3 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #111827;
            transition: color 0.3s ease;
        }
        
        body.dark .capability-card h3 {
            color: #f3f4f6;
        }
        
        .capability-card p {
            color: #4b5563;
            margin-bottom: 20px;
            transition: color 0.3s ease;
        }
        
        body.dark .capability-card p {
            color: #9ca3af;
        }
        
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }
        
        .tag {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* Card colors */
        .ai-chat .card-icon {
            background: linear-gradient(to right, #3b82f6, #06b6d4);
        }
        
        .scraping .card-icon {
            background: linear-gradient(to right, #10b981, #059669);
        }
        
        .pentest .card-icon {
            background: linear-gradient(to right, #ef4444, #ec4899);
        }
        
        .security .card-icon {
            background: linear-gradient(to right, #8b5cf6, #6366f1);
        }
        
        .intelligence .card-icon {
            background: linear-gradient(to right, #f59e0b, #f97316);
        }
        
        .workflow .card-icon {
            background: linear-gradient(to right, #14b8a6, #3b82f6);
        }
        
        /* Tags */
        .tag-blue {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        body.dark .tag-blue {
            background-color: #1e3a8a;
            color: #93c5fd;
        }
        
        .tag-cyan {
            background-color: #cffafe;
            color: #155e75;
        }
        
        body.dark .tag-cyan {
            background-color: #164e63;
            color: #a5f3fc;
        }
        
        .tag-green {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        body.dark .tag-green {
            background-color: #065f46;
            color: #a7f3d0;
        }
        
        .tag-emerald {
            background-color: #ecfdf5;
            color: #047857;
        }
        
        body.dark .tag-emerald {
            background-color: #047857;
            color: #a7f3d0;
        }
        
        .tag-red {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        
        body.dark .tag-red {
            background-color: #991b1b;
            color: #fecaca;
        }
        
        .tag-pink {
            background-color: #fce7f3;
            color: #9d174d;
        }
        
        body.dark .tag-pink {
            background-color: #9d174d;
            color: #fbcfe8;
        }
        
        .tag-purple {
            background-color: #ede9fe;
            color: #5b21b6;
        }
        
        body.dark .tag-purple {
            background-color: #5b21b6;
            color: #ddd6fe;
        }
        
        .tag-indigo {
            background-color: #e0e7ff;
            color: #3730a3;
        }
        
        body.dark .tag-indigo {
            background-color: #3730a3;
            color: #c7d2fe;
        }
        
        .tag-yellow {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        body.dark .tag-yellow {
            background-color: #92400e;
            color: #fef3c7;
        }
        
        .tag-orange {
            background-color: #ffedd5;
            color: #9a3412;
        }
        
        body.dark .tag-orange {
            background-color: #9a3412;
            color: #fed7aa;
        }
        
        .tag-teal {
            background-color: #ccfbf1;
            color: #115e59;
        }
        
        body.dark .tag-teal {
            background-color: #115e59;
            color: #99f6e4;
        }
        
        /* CTA */
        .cta {
            text-align: center;
            margin-top: 40px;
            animation: fadeIn 0.5s ease-in-out;
            animation-delay: 0.7s;
        }
        
        .cta-button {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: linear-gradient(to right, #2563eb, #4f46e5);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }
        
        .cta-button svg {
            margin-left: 8px;
            width: 20px;
            height: 20px;
        }
        
        /* Footer */
        footer {
            background-color: white;
            border-top: 1px solid #e5e7eb;
            padding: 30px 0;
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        
        body.dark footer {
            background-color: #111827;
            border-top-color: #374151;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .social-links {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .social-link {
            color: #9ca3af;
            transition: color 0.3s ease;
        }
        
        .social-link:hover {
            color: #6b7280;
        }
        
        body.dark .social-link {
            color: #6b7280;
        }
        
        body.dark .social-link:hover {
            color: #9ca3af;
        }
        
        .copyright {
            color: #9ca3af;
            font-size: 14px;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero h2 {
                font-size: 28px;
            }
            
            .hero p {
                font-size: 16px;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .footer-container {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <div class="logo-container">
                <div class="logo">H</div>
                <div class="logo-text">
                    <h1>HexStrike AI</h1>
                    <p>Choose Your Capability</p>
                </div>
            </div>
            <button id="darkModeToggle" class="dark-mode-toggle">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                </svg>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <div class="hero">
            <h2>Select Your <span class="gradient-text">Capability</span></h2>
            <p>Choose from our comprehensive suite of AI-powered tools for cybersecurity, data intelligence, and automation.</p>
        </div>

        <!-- Capabilities Grid -->
        <div class="card-grid">
            <!-- AI Chatting -->
            <div class="capability-card ai-chat" data-capability="ai-chat" style="animation-delay: 0.1s">
                <div class="card-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3>AI Chatting</h3>
                <p>Intelligent conversational AI for cybersecurity guidance, threat analysis, and real-time assistance.</p>
                <div class="tag-container">
                    <span class="tag tag-blue">GPT Integration</span>
                    <span class="tag tag-cyan">Real-time</span>
                </div>
            </div>

            <!-- Web Scraping -->
            <div class="capability-card scraping" data-capability="scraping" style="animation-delay: 0.2s">
                <div class="card-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                    </svg>
                </div>
                <h3>Web Scraping</h3>
                <p>Advanced data extraction and intelligence gathering from websites with stealth capabilities.</p>
                <div class="tag-container">
                    <span class="tag tag-green">Stealth Mode</span>
                    <span class="tag tag-emerald">Data Mining</span>
                </div>
            </div>

            <!-- Penetration Testing -->
            <div class="capability-card pentest" data-capability="pentest" style="animation-delay: 0.3s">
                <div class="card-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3>Penetration Testing</h3>
                <p>Comprehensive security testing and vulnerability assessment with automated reporting.</p>
                <div class="tag-container">
                    <span class="tag tag-red">Vulnerability Scan</span>
                    <span class="tag tag-pink">Automated</span>
                </div>
            </div>

            <!-- Security Analysis -->
            <div class="capability-card security" data-capability="security" style="animation-delay: 0.4s">
                <div class="card-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <h3>Security Analysis</h3>
                <p>Deep security assessment and threat intelligence with AI-powered insights.</p>
                <div class="tag-container">
                    <span class="tag tag-purple">Threat Intel</span>
                    <span class="tag tag-indigo">AI Analysis</span>
                </div>
                <a href="/security-testing-interface" class="card-link"></a>
            </div>

            <!-- Data Intelligence -->
            <div class="capability-card intelligence" data-capability="intelligence" style="animation-delay: 0.5s">
                <div class="card-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3>Data Intelligence</h3>
                <p>Advanced analytics and reporting with visualization and correlation capabilities.</p>
                <div class="tag-container">
                    <span class="tag tag-yellow">Analytics</span>
                    <span class="tag tag-orange">Visualization</span>
                </div>
                <a href="/scraper-interface" class="card-link"></a>
            </div>

            <!-- Custom Workflow -->
            <div class="capability-card workflow" data-capability="workflow" style="animation-delay: 0.6s">
                <div class="card-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h3>Custom Workflow</h3>
                <p>Build your own security automation workflows with our drag-and-drop interface.</p>
                <div class="tag-container">
                    <span class="tag tag-teal">Automation</span>
                    <span class="tag tag-blue">Customizable</span>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="cta">
            <a href="/all-features-selector.html" class="cta-button">
                View All Features
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-container">
            <div class="social-links">
                <a href="#" class="social-link">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"></path>
                    </svg>
                </a>
                <a href="#" class="social-link">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                    </svg>
                </a>
                <a href="#" class="social-link">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
            <p class="copyright">&copy; 2025 HexStrike AI. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Dark mode toggle functionality
            const darkModeToggle = document.getElementById('darkModeToggle');
            const body = document.body;
            
            // Check for saved theme preference or use system preference
            if (localStorage.getItem('darkMode') === 'true' || 
                (localStorage.getItem('darkMode') === null && 
                window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                body.classList.add('dark');
            }
            
            // Toggle dark mode
            darkModeToggle.addEventListener('click', function() {
                body.classList.toggle('dark');
                localStorage.setItem('darkMode', body.classList.contains('dark'));
            });
            
            // Add click event listeners to all capability cards
            document.querySelectorAll('.capability-card').forEach(card => {
                card.addEventListener('click', function() {
                    const capability = this.getAttribute('data-capability');
                    navigateToCapability(capability);
                });
                
                // Add active hover effect
                card.addEventListener('mouseenter', function() {
                    this.classList.add('pulse-glow');
                });
                
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('pulse-glow');
                });
            });
            
            // Function to navigate to the selected capability
            function navigateToCapability(capability) {
                // Show visual feedback before navigation
                const selectedCard = document.querySelector(`.${capability}`);
                selectedCard.style.transform = 'scale(0.95)';
                
                setTimeout(() => {
                    selectedCard.style.transform = 'scale(1)';
                    
                    // Map capabilities to their respective server routes 
                    const capabilityRoutes = {
                        'ai-chat': '/chat-interface',
                        'scraping': '/scraper-interface',
                        'pentest': '/penetration-testing',
                        'security': '/security-analysis',        
                        'intelligence': '/all-features',
                        'workflow': '/custom-workflow-interface'
                    };
                    
                    // Navigate to the appropriate page
                    if (capabilityRoutes[capability]) {
                        // Create an anchor element and click it for proper navigation
                        const link = document.createElement('a');
                        link.href = capabilityRoutes[capability];
                        link.target = '_blank'; // Open in new tab to ensure it works
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }
                }, 200);
            }
        });
    </script>
</body>
</html>