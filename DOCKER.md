# HexStrike AI Docker Setup

This document provides comprehensive instructions for deploying HexStrike AI using Docker containers with production-ready security configurations.

## 🏗️ Architecture Overview

The Docker setup includes the following services:

- **chatbox-server**: Main Flask application server
- **worker**: Background task processor with security tools
- **postgres**: PostgreSQL database with security extensions
- **redis**: Redis cache and message queue
- **nginx**: Reverse proxy with SSL termination and security headers

## 📋 Prerequisites

- Docker Desktop for Windows (latest version)
- PowerShell 5.1 or later
- OpenSSL (for SSL certificate generation)
- At least 4GB RAM available for containers
- 10GB free disk space

## 🚀 Quick Start

### 1. Initial Setup

```powershell
# Clone the repository (if not already done)
git clone <repository-url>
cd hexstrike-ai

# Run the setup script
.\docker-manage.ps1 setup
```

### 2. Configure Environment

Edit the `.env` file created during setup:

```bash
# Update these values with secure passwords
POSTGRES_PASSWORD=your_secure_database_password
REDIS_PASSWORD=your_secure_redis_password
JWT_SECRET_KEY=your_jwt_secret_minimum_32_characters
SECRET_KEY=your_flask_secret_minimum_32_characters
```

### 3. Start Services

```powershell
# Start all services
.\docker-manage.ps1 start

# Check status
.\docker-manage.ps1 status
```

### 4. Access the Application

- **Web Interface**: https://localhost
- **API Health Check**: https://localhost/health
- **Database**: localhost:5432
- **Redis**: localhost:6379

## 🛠️ Management Commands

The `docker-manage.ps1` script provides comprehensive container management:

### Basic Operations

```powershell
# Build images
.\docker-manage.ps1 build

# Start services
.\docker-manage.ps1 start

# Stop services
.\docker-manage.ps1 stop

# Restart services
.\docker-manage.ps1 restart

# View logs
.\docker-manage.ps1 logs

# Follow logs in real-time
.\docker-manage.ps1 logs -Follow

# View specific service logs
.\docker-manage.ps1 logs -Service chatbox-server
```

### Maintenance Operations

```powershell
# Check service status
.\docker-manage.ps1 status

# Create database backup
.\docker-manage.ps1 backup

# Restore from backup
.\docker-manage.ps1 restore -BackupFile ./backups/hexstrike_backup_20240107_120000.sql

# Clean up (removes containers and images)
.\docker-manage.ps1 clean

# Force clean (includes volumes)
.\docker-manage.ps1 clean -Force
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `POSTGRES_DB` | Database name | hexstrike_ai | Yes |
| `POSTGRES_USER` | Database user | hexstrike | Yes |
| `POSTGRES_PASSWORD` | Database password | - | Yes |
| `REDIS_PASSWORD` | Redis password | - | Yes |
| `JWT_SECRET_KEY` | JWT signing key | - | Yes |
| `SECRET_KEY` | Flask secret key | - | Yes |
| `CHAT_BOX_PORT` | Application port | 3000 | No |
| `WORKER_CONCURRENCY` | Worker processes | 2 | No |
| `WORKER_TIMEOUT` | Worker timeout (seconds) | 300 | No |

### SSL Configuration

For production deployment:

1. Replace self-signed certificates in `ssl/` directory
2. Update `nginx.conf` with your domain name
3. Configure proper SSL certificate paths

### Security Settings

The Docker setup includes several security features:

- **Non-root containers**: All services run as non-root users
- **Read-only filesystems**: Containers use read-only root filesystems
- **Security headers**: Nginx adds comprehensive security headers
- **Rate limiting**: API endpoints have rate limiting configured
- **Network isolation**: Services communicate through isolated Docker network
- **Resource limits**: Memory and CPU limits prevent resource exhaustion

## 📊 Monitoring and Logging

### Log Locations

- **Application logs**: `./logs/`
- **Nginx logs**: Docker volume `nginx_logs`
- **Container logs**: Use `docker-compose logs`

### Health Checks

All services include health checks:

```powershell
# Check container health
docker ps --filter "name=hexstrike"

# Detailed health status
docker inspect hexstrike-chatbox | Select-String -Pattern "Health"
```

### Performance Monitoring

```powershell
# Monitor resource usage
docker stats

# View container processes
docker-compose top
```

## 🔒 Security Considerations

### Production Deployment

1. **Change default passwords**: Update all passwords in `.env`
2. **Use proper SSL certificates**: Replace self-signed certificates
3. **Configure firewall**: Limit access to necessary ports only
4. **Regular updates**: Keep Docker images updated
5. **Backup strategy**: Implement regular database backups
6. **Log monitoring**: Set up log aggregation and monitoring

### Network Security

- Services communicate through isolated Docker network
- Only necessary ports are exposed to host
- Nginx provides SSL termination and security headers
- Rate limiting prevents abuse

### Container Security

- All containers run as non-root users
- Read-only filesystems prevent tampering
- Resource limits prevent DoS attacks
- Security scanning of base images

## 🐛 Troubleshooting

### Common Issues

#### Services won't start

```powershell
# Check Docker is running
docker version

# Check logs for errors
.\docker-manage.ps1 logs

# Rebuild images
.\docker-manage.ps1 build -Force
```

#### Database connection issues

```powershell
# Check PostgreSQL health
docker-compose exec postgres pg_isready -U hexstrike

# Check database logs
docker-compose logs postgres
```

#### SSL certificate issues

```powershell
# Regenerate certificates
Remove-Item ssl/* -Force
.\docker-manage.ps1 setup
```

#### Performance issues

```powershell
# Check resource usage
docker stats

# Increase worker concurrency
# Edit .env: WORKER_CONCURRENCY=4
.\docker-manage.ps1 restart
```

### Log Analysis

```powershell
# Application errors
.\docker-manage.ps1 logs -Service chatbox-server | Select-String "ERROR"

# Security events
.\docker-manage.ps1 logs -Service nginx | Select-String "403\|429"

# Database issues
.\docker-manage.ps1 logs -Service postgres | Select-String "ERROR"
```

## 📈 Scaling

### Horizontal Scaling

```powershell
# Scale worker processes
docker-compose up -d --scale worker=3

# Scale with load balancer (requires additional configuration)
docker-compose up -d --scale chatbox-server=2
```

### Resource Optimization

Edit `docker-compose.yml` to adjust resource limits:

```yaml
services:
  chatbox-server:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
```

## 🔄 Updates and Maintenance

### Updating the Application

```powershell
# Pull latest code
git pull origin main

# Rebuild and restart
.\docker-manage.ps1 build -Force
.\docker-manage.ps1 restart
```

### Database Migrations

```powershell
# Run migrations
docker-compose exec chatbox-server python -m flask db upgrade
```

### Backup Strategy

```powershell
# Automated daily backup (add to scheduled task)
.\docker-manage.ps1 backup

# Cleanup old backups (keep last 7 days)
Get-ChildItem ./backups/*.sql | Where-Object {$_.CreationTime -lt (Get-Date).AddDays(-7)} | Remove-Item
```

## 📞 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review container logs for error messages
3. Ensure all prerequisites are met
4. Verify environment configuration

## 📄 License

This Docker configuration is part of the HexStrike AI project and follows the same licensing terms.