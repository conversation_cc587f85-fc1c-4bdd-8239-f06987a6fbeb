<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Intelligence Interface - HexStrike AI</title>
    <style>
        :root {
            --primary-color: #ff9800;
            --secondary-color: #2196f3;
            --dark-bg: #1d3557;
            --light-bg: #f8f9fa;
            --accent-color: #4caf50;
            --text-light: #f1faee;
            --text-dark: #1d3557;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 20px 0;
            text-align: center;
        }
        
        header h1 {
            margin-bottom: 10px;
        }
        
        .data-form {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="url"],
        select,
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #f57c00;
        }
        
        .results {
            margin-top: 30px;
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .results pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        .back-button {
            display: inline-block;
            margin-top: 20px;
            background-color: var(--secondary-color);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
        }
        
        .back-button:hover {
            background-color: #1976d2;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .tool-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border-left: 5px solid var(--primary-color);
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .tool-card.selected {
            border-left: 5px solid #2ecc71;
        }
        
        .tool-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .dashboard-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-card h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .chart-container {
            height: 200px;
            margin-top: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th, .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .data-table tr:hover {
            background-color: #f9f9f9;
        }
        
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag-blue {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        .tag-green {
            background-color: #e8f5e9;
            color: #388e3c;
        }
        
        .tag-red {
            background-color: #ffebee;
            color: #d32f2f;
        }
        
        .tag-orange {
            background-color: #fff3e0;
            color: #e64a19;
        }
        
        .tag-purple {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        
        .tag-indigo {
            background-color: #e8eaf6;
            color: #303f9f;
        }
        
        .tag-pink {
            background-color: #fce4ec;
            color: #c2185b;
        }
        
        .tag-yellow {
            background-color: #fffde7;
            color: #fbc02d;
        }
        
        .tag-cyan {
            background-color: #e0f7fa;
            color: #00acc1;
        }
        
        .tag-emerald {
            background-color: #e0f2f1;
            color: #00897b;
        }
        
        .tag-teal {
            background-color: #e0f2f1;
            color: #00796b;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>HexStrike AI - Data Intelligence Interface</h1>
            <p>Advanced data analysis and intelligence gathering tools</p>
        </div>
    </header>
    
    <div class="container">
        <div class="data-form">
            <h2>Data Intelligence Configuration</h2>
            <form id="dataForm">
                <div class="form-group">
                    <label for="dataSource">Data Source:</label>
                    <select id="dataSource" name="dataSource">
                        <option value="web">Web Data</option>
                        <option value="api">API Endpoints</option>
                        <option value="database">Database</option>
                        <option value="files">File System</option>
                        <option value="stream">Data Stream</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="sourceUrl">Source URL or Connection String:</label>
                    <input type="text" id="sourceUrl" name="sourceUrl" placeholder="https://example.com/api or connection string" required>
                </div>
                
                <div class="form-group">
                    <label for="analysisType">Analysis Type:</label>
                    <select id="analysisType" name="analysisType">
                        <option value="descriptive">Descriptive Analytics</option>
                        <option value="diagnostic">Diagnostic Analytics</option>
                        <option value="predictive">Predictive Analytics</option>
                        <option value="prescriptive">Prescriptive Analytics</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="dataFormat">Data Format:</label>
                    <select id="dataFormat" name="dataFormat">
                        <option value="json">JSON</option>
                        <option value="csv">CSV</option>
                        <option value="xml">XML</option>
                        <option value="sql">SQL</option>
                        <option value="text">Plain Text</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="additionalParams">Additional Parameters:</label>
                    <textarea id="additionalParams" name="additionalParams" rows="4" placeholder="Enter any specific parameters, filters, or special instructions"></textarea>
                </div>
                
                <button type="submit" id="startAnalysis">Start Data Analysis</button>
            </form>
        </div>
        
        <div class="dashboard">
            <div class="dashboard-card">
                <h3>Data Overview</h3>
                <p>Summary statistics and key metrics from your data source</p>
                <div class="chart-container">
                    <p>Data overview visualization will appear here</p>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h3>Trend Analysis</h3>
                <p>Temporal patterns and trend identification</p>
                <div class="chart-container">
                    <p>Trend visualization will appear here</p>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h3>Correlation Matrix</h3>
                <p>Relationships between different data variables</p>
                <div class="chart-container">
                    <p>Correlation matrix will appear here</p>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h3>Anomaly Detection</h3>
                <p>Identification of outliers and unusual patterns</p>
                <div class="chart-container">
                    <p>Anomaly visualization will appear here</p>
                </div>
            </div>
        </div>
        
        <div class="tool-grid">
            <div class="tool-card">
                <h3>Data Extraction</h3>
                <p>Extract structured data from various sources</p>
                <div class="tag-container">
                    <span class="tag tag-blue">Web Scraping</span>
                    <span class="tag tag-green">API Integration</span>
                </div>
            </div>
            
            <div class="tool-card">
                <h3>Data Transformation</h3>
                <p>Clean, normalize, and prepare data for analysis</p>
                <div class="tag-container">
                    <span class="tag tag-orange">ETL Pipeline</span>
                    <span class="tag tag-purple">Data Cleaning</span>
                </div>
            </div>
            
            <div class="tool-card">
                <h3>Predictive Modeling</h3>
                <p>Build models to forecast future trends and behaviors</p>
                <div class="tag-container">
                    <span class="tag tag-red">Machine Learning</span>
                    <span class="tag tag-indigo">Time Series</span>
                </div>
            </div>
            
            <div class="tool-card">
                <h3>Natural Language Processing</h3>
                <p>Extract insights from text data</p>
                <div class="tag-container">
                    <span class="tag tag-cyan">Sentiment Analysis</span>
                    <span class="tag tag-emerald">Entity Recognition</span>
                </div>
            </div>
            
            <div class="tool-card">
                <h3>Data Visualization</h3>
                <p>Create interactive charts and dashboards</p>
                <div class="tag-container">
                    <span class="tag tag-yellow">Charts</span>
                    <span class="tag tag-pink">Dashboards</span>
                </div>
            </div>
            
            <div class="tool-card">
                <h3>Automated Reporting</h3>
                <p>Generate scheduled reports with key insights</p>
                <div class="tag-container">
                    <span class="tag tag-teal">PDF Reports</span>
                    <span class="tag tag-blue">Email Delivery</span>
                </div>
            </div>
        </div>
        
        <div class="results" style="display: none;">
            <h2>Data Analysis Results</h2>
            <div id="resultsSummary"></div>
            
            <h3>Data Sample</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Value</th>
                        <th>Timestamp</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <!-- Data rows will be inserted here -->
                </tbody>
            </table>
            
            <h3>Analysis Details</h3>
            <pre id="resultsDetail"></pre>
            <a href="#" class="back-button" id="backToForm">Back to Configuration</a>
        </div>
    </div>
    
    <script>
        document.getElementById('dataForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            document.getElementById('startAnalysis').textContent = 'Analyzing Data...';
            document.getElementById('startAnalysis').disabled = true;
            
            // Simulate analysis running
            setTimeout(() => {
                // Generate sample data for the table
                const tableBody = document.getElementById('dataTableBody');
                tableBody.innerHTML = '';
                
                const sampleData = [
                    { id: 1, name: 'Product A', category: 'Electronics', value: 1250, timestamp: '2025-09-12 08:30:45' },
                    { id: 2, name: 'Product B', category: 'Clothing', value: 450, timestamp: '2025-09-12 09:15:22' },
                    { id: 3, name: 'Product C', category: 'Electronics', value: 899, timestamp: '2025-09-12 10:05:17' },
                    { id: 4, name: 'Product D', category: 'Home', value: 1599, timestamp: '2025-09-12 11:22:33' },
                    { id: 5, name: 'Product E', category: 'Clothing', value: 299, timestamp: '2025-09-12 12:45:01' }
                ];
                
                sampleData.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${item.category}</td>
                        <td>$${item.value}</td>
                        <td>${item.timestamp}</td>
                    `;
                    tableBody.appendChild(row);
                });
                
                // Show results
                document.querySelector('.results').style.display = 'block';
                document.getElementById('resultsSummary').innerHTML = `
                    <h3>Summary</h3>
                    <p>Data Source: ${document.getElementById('dataSource').options[document.getElementById('dataSource').selectedIndex].text}</p>
                    <p>Analysis Type: ${document.getElementById('analysisType').options[document.getElementById('analysisType').selectedIndex].text}</p>
                    <p>Data Format: ${document.getElementById('dataFormat').options[document.getElementById('dataFormat').selectedIndex].text}</p>
                    <p>Records Analyzed: 1,245</p>
                    <p>Key Findings: 3 significant patterns detected</p>
                `;
                
                document.getElementById('resultsDetail').textContent = `
DATA INTELLIGENCE REPORT
========================
Source: ${document.getElementById('sourceUrl').value}
Analysis Date: ${new Date().toISOString().split('T')[0]}
Analysis Type: ${document.getElementById('analysisType').options[document.getElementById('analysisType').selectedIndex].text}

SUMMARY STATISTICS
-----------------
Total Records: 1,245
Missing Values: 23 (1.8%)
Outliers Detected: 15 (1.2%)
Data Range: 2025-01-01 to 2025-09-12

KEY METRICS
----------
Average Value: $724.50
Median Value: $499.99
Standard Deviation: $325.75
Min Value: $49.99
Max Value: $2,499.99

CATEGORY DISTRIBUTION
-------------------
Electronics: 45%
Clothing: 30%
Home: 15%
Other: 10%

TREND ANALYSIS
-------------
- Upward trend detected in Electronics category (8.5% growth)
- Seasonal pattern identified in Clothing category
- Correlation coefficient between price and sales: -0.65

RECOMMENDATIONS
-------------
1. Focus marketing efforts on Electronics category
2. Implement price optimization for Clothing items
3. Investigate anomalies in Home category pricing
4. Consider expanding data collection to include customer demographics
`;
                
                // Reset form state
                document.getElementById('startAnalysis').textContent = 'Start Data Analysis';
                document.getElementById('startAnalysis').disabled = false;
                
                // Update dashboard visualizations (simulated)
                const chartContainers = document.querySelectorAll('.chart-container');
                chartContainers.forEach(container => {
                    container.innerHTML = '<p>Data visualization loaded</p>';
                });
            }, 3000);
        });
        
        document.getElementById('backToForm').addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector('.results').style.display = 'none';
        });
    </script>
</body>
</html>