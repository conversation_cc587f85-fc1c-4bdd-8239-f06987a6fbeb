#!/usr/bin/env python3
"""
Backbone Integration Module
Integrates backbone security, API endpoints, and UI components into the main Flask application.

Security Features:
- Admin-only access control
- Rate limiting and allowlist validation
- Audit logging for all operations
- Idempotency key support

Author: HexStrike Security Team
Date: 2024-12-12
"""

import os
import json
import time
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, render_template_string, send_from_directory
from functools import wraps

# Import our backbone modules
from backbone_security import (
    BackboneSecurityManager, 
    require_admin, 
    rate_limit_admin,
    validate_allowlist_urls,
    generate_idempotency_key
)
from backbone_api import backbone_bp
from safe_pentest import SafePentestEngine, run_safe_pentest

class BackboneIntegration:
    """
    Main integration class for Backbone Checks functionality.
    Handles Flask blueprint registration and route management.
    """
    
    def __init__(self, app=None):
        self.app = app
        self.security_manager = BackboneSecurityManager()
        self.pentest_engine = SafePentestEngine()
        self.blueprint = None
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the Flask application with backbone functionality."""
        self.app = app
        self.blueprint = self.create_blueprint()
        app.register_blueprint(self.blueprint, url_prefix='/backbone')
        
        # Security manager is already initialized globally
        
        # Add template globals for admin UI
        app.jinja_env.globals.update(
            backbone_version='1.0.0',
            backbone_modules=['scrape', 'crawl', 'safe_pentest', 'analyze']
        )
    
    def create_blueprint(self):
        """Create and configure the backbone blueprint."""
        bp = Blueprint('backbone', __name__)
        
        # Admin UI Route
        @bp.route('/')
        @bp.route('/admin')
        @require_admin
        def admin_dashboard():
            """Serve the backbone admin dashboard."""
            try:
                # Read the HTML file
                html_path = os.path.join(os.path.dirname(__file__), 'backbone-admin.html')
                with open(html_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # Log admin access
                self.security_manager.audit_log(
                    actor_id=request.headers.get('X-User-ID', 'admin'),
                    action='admin_dashboard_access',
                    target='backbone_admin_ui',
                    result='success',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                return html_content
                
            except FileNotFoundError:
                return jsonify({
                    'error': 'Admin dashboard not found',
                    'code': 'DASHBOARD_MISSING'
                }), 404
            except Exception as e:
                return jsonify({
                    'error': f'Failed to load dashboard: {str(e)}',
                    'code': 'DASHBOARD_ERROR'
                }), 500
        
        # Health Check API
        @bp.route('/api/backbone/health', methods=['GET'])
        @require_admin
        @rate_limit_admin(limit=30, window_minutes=1)
        def health_check():
            """Get comprehensive system health status."""
            start_time = time.time()
            
            try:
                # Get health status directly from backbone_bp routes
                health_data = {
                    'status': 'healthy',
                    'timestamp': datetime.utcnow().isoformat() + 'Z',
                    'uptime_seconds': int(time.time() - start_time),
                    'modules': {
                        'scrape': {'status': 'available', 'last_check': None},
                        'crawl': {'status': 'available', 'last_check': None},
                        'safe_pentest': {'status': 'available', 'last_check': None},
                        'analyze': {'status': 'available', 'last_check': None}
                    }
                }
                
                # Add security context
                health_data['security'] = {
                    'admin_access': True,
                    'rate_limit_remaining': getattr(request, 'rate_limit_remaining', 0),
                    'allowlist_enabled': bool(os.getenv('ALLOWLIST_HOSTS')),
                    'audit_enabled': True
                }
                
                # Log health check
                elapsed_ms = int((time.time() - start_time) * 1000)
                self.security_manager.audit_log(
                    actor_id=request.headers.get('X-User-ID', 'admin'),
                    action='health_check',
                    target='backbone_health_api',
                    result='success',
                    elapsed_ms=elapsed_ms,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                return jsonify(health_data)
                
            except Exception as e:
                elapsed_ms = int((time.time() - start_time) * 1000)
                self.security_manager.audit_log(
                    actor_id=request.headers.get('X-User-ID', 'admin'),
                    action='health_check',
                    target='backbone_health_api',
                    result='error',
                    error=str(e),
                    elapsed_ms=elapsed_ms,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                return jsonify({
                    'error': f'Health check failed: {str(e)}',
                    'code': 'HEALTH_CHECK_ERROR',
                    'timestamp': datetime.utcnow().isoformat() + 'Z'
                }), 500
        
        # Self-Test API
        @bp.route('/api/backbone/self-test', methods=['POST'])
        @require_admin
        @rate_limit_admin(limit=5, window_minutes=1)  # Stricter rate limit for intensive operations
        def self_test():
            """Execute comprehensive backbone self-tests."""
            start_time = time.time()
            
            try:
                # Get request data
                data = request.get_json()
                if not data or 'targets' not in data:
                    return jsonify({
                        'error': 'Missing targets in request body',
                        'code': 'INVALID_REQUEST'
                    }), 400
                
                targets = data['targets']
                required_fields = ['scrapeUrl', 'crawlUrl', 'testHost']
                
                # Validate required fields
                for field in required_fields:
                    if not targets.get(field):
                        return jsonify({
                            'error': f'Missing required field: {field}',
                            'code': 'MISSING_FIELD'
                        }), 400
                
                # Validate URLs against allowlist
                urls_to_check = [targets['scrapeUrl'], targets['crawlUrl']]
                for url in urls_to_check:
                    if not security_manager.validate_host_allowlist(url):
                        return jsonify({
                            'error': f'URL not in allowlist: {url}',
                            'code': 'URL_NOT_ALLOWED'
                        }), 403
                
                # Check idempotency
                idempotency_key = request.headers.get('X-Idempotency-Key')
                if idempotency_key:
                    cached_result = self.security_manager.idempotency_manager.get_result(idempotency_key)
                    if cached_result:
                        # Log idempotent request
                        self.security_manager.audit_log(
                            actor_id=request.headers.get('X-User-ID', 'admin'),
                            action='self_test_idempotent',
                            target=f"targets:{json.dumps(targets, sort_keys=True)}",
                            result='cached',
                            ip_address=request.remote_addr,
                            user_agent=request.headers.get('User-Agent', '')
                        )
                        return jsonify(cached_result)
                
                # Execute self-tests
                # Execute safe pentest for each target
                test_results = {
                    'timestamp': datetime.utcnow().isoformat() + 'Z',
                    'targets': targets,
                    'results': {}
                }
                
                for target_type, url in targets.items():
                    if url:
                        pentest_result = run_safe_pentest(url)
                        test_results['results'][target_type] = pentest_result
                
                # Store result for idempotency if key provided
                if idempotency_key:
                    self.security_manager.idempotency_manager.store_result(
                        idempotency_key, test_results
                    )
                
                # Log successful test execution
                elapsed_ms = int((time.time() - start_time) * 1000)
                self.security_manager.audit_log(
                    actor_id=request.headers.get('X-User-ID', 'admin'),
                    action='self_test_execution',
                    target=f"targets:{json.dumps(targets, sort_keys=True)}",
                    result='success',
                    elapsed_ms=elapsed_ms,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                return jsonify(test_results)
                
            except Exception as e:
                elapsed_ms = int((time.time() - start_time) * 1000)
                self.security_manager.audit_log(
                    actor_id=request.headers.get('X-User-ID', 'admin'),
                    action='self_test_execution',
                    target=f"targets:{json.dumps(data.get('targets', {}), sort_keys=True) if data else 'unknown'}",
                    result='error',
                    error=str(e),
                    elapsed_ms=elapsed_ms,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                return jsonify({
                    'error': f'Self-test failed: {str(e)}',
                    'code': 'SELF_TEST_ERROR',
                    'timestamp': datetime.utcnow().isoformat() + 'Z'
                }), 500
        
        # Audit Log API
        @bp.route('/api/backbone/audit', methods=['GET'])
        @require_admin
        @rate_limit_admin(limit=10, window_minutes=1)
        def get_audit_log():
            """Retrieve recent audit log entries."""
            try:
                # Get query parameters
                limit = min(int(request.args.get('limit', 100)), 1000)  # Max 1000 entries
                offset = int(request.args.get('offset', 0))
                action_filter = request.args.get('action')
                
                # Get audit entries (this would typically come from a database)
                # For now, return a sample structure
                audit_entries = {
                    'entries': [
                        {
                            'id': 'audit_001',
                            'timestamp': datetime.utcnow().isoformat() + 'Z',
                            'actor_id': 'admin',
                            'action': 'health_check',
                            'target': 'backbone_health_api',
                            'result': 'success',
                            'elapsed_ms': 45,
                            'ip_address': request.remote_addr,
                            'user_agent': request.headers.get('User-Agent', '')
                        }
                    ],
                    'pagination': {
                        'limit': limit,
                        'offset': offset,
                        'total': 1,
                        'has_more': False
                    }
                }
                
                # Log audit access
                self.security_manager.audit_log(
                    actor_id=request.headers.get('X-User-ID', 'admin'),
                    action='audit_log_access',
                    target='backbone_audit_api',
                    result='success',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                return jsonify(audit_entries)
                
            except Exception as e:
                return jsonify({
                    'error': f'Failed to retrieve audit log: {str(e)}',
                    'code': 'AUDIT_LOG_ERROR'
                }), 500
        
        # Configuration API
        @bp.route('/api/backbone/config', methods=['GET'])
        @require_admin
        @rate_limit_admin(limit=20, window_minutes=1)
        def get_configuration():
            """Get backbone configuration and security settings."""
            try:
                config = {
                    'version': '1.0.0',
                    'modules': ['scrape', 'crawl', 'safe_pentest', 'analyze'],
                    'security': {
                        'admin_required': True,
                        'rate_limiting': {
                            'health_check': '30/minute',
                            'self_test': '5/minute',
                            'audit_log': '10/minute'
                        },
                        'allowlist_enabled': bool(os.getenv('ALLOWLIST_HOSTS')),
                        'allowlist_hosts': os.getenv('ALLOWLIST_HOSTS', '').split(',') if os.getenv('ALLOWLIST_HOSTS') else [],
                        'idempotency_enabled': True,
                        'audit_logging': True
                    },
                    'timeouts': {
                        'scrape_timeout': 20,
                        'crawl_timeout': 20,
                        'pentest_timeout': 20,
                        'analyze_timeout': 20
                    },
                    'safe_pentest': {
                        'allowed_ports': [80, 443],
                        'max_requests_per_host': 5,
                        'request_delay': 1.0
                    }
                }
                
                return jsonify(config)
                
            except Exception as e:
                return jsonify({
                    'error': f'Failed to get configuration: {str(e)}',
                    'code': 'CONFIG_ERROR'
                }), 500
        
        return bp
    
    def register_cli_commands(self, app):
        """Register CLI commands for backbone management."""
        
        @app.cli.command('backbone-init')
        def init_backbone():
            """Initialize backbone security and database tables."""
            click.echo('Initializing Backbone Checks...')
            
            try:
                # Initialize security manager
                self.security_manager.init_app(app)
                
                # Create any necessary database tables
                # (This would be implemented based on your database setup)
                
                click.echo('✓ Backbone Checks initialized successfully')
                
            except Exception as e:
                click.echo(f'✗ Initialization failed: {str(e)}', err=True)
        
        @app.cli.command('backbone-test')
        def test_backbone():
            """Run backbone system tests."""
            click.echo('Running Backbone system tests...')
            
            try:
                # Test security manager
                security_ok = self.security_manager.test_connection()
                click.echo(f'Security Manager: {"✓" if security_ok else "✗"}')
                
                # Test API endpoints
                api_ok = True  # Placeholder for API health test
                click.echo(f'API Health: {"✓" if api_ok else "✗"}')
                
                # Test pentest engine
                pentest_ok = self.pentest_engine.test_connection()
                click.echo(f'Pentest Engine: {"✓" if pentest_ok else "✗"}')
                
                if all([security_ok, api_ok, pentest_ok]):
                    click.echo('✓ All backbone tests passed')
                else:
                    click.echo('✗ Some backbone tests failed', err=True)
                    
            except Exception as e:
                click.echo(f'✗ Test execution failed: {str(e)}', err=True)


def create_backbone_app(app):
    """
    Factory function to integrate backbone functionality into an existing Flask app.
    
    Args:
        app: Flask application instance
        
    Returns:
        BackboneIntegration: Configured backbone integration instance
    """
    backbone = BackboneIntegration()
    backbone.init_app(app)
    
    # Register CLI commands if Click is available
    try:
        import click
        backbone.register_cli_commands(app)
    except ImportError:
        pass
    
    return backbone


# Example usage and testing
if __name__ == '__main__':
    from flask import Flask
    
    # Create test Flask app
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-secret-key-change-in-production'
    
    # Set up environment variables for testing
    os.environ['ALLOWLIST_HOSTS'] = 'example.com,httpbin.org,jsonplaceholder.typicode.com'
    
    # Initialize backbone
    backbone = create_backbone_app(app)
    
    # Add a simple route for testing
    @app.route('/')
    def index():
        return '''
        <h1>Backbone Checks Test Server</h1>
        <p><a href="/backbone/admin">Admin Dashboard</a></p>
        <p><a href="/backbone/api/backbone/health">Health Check API</a></p>
        '''
    
    print("Starting Backbone Checks test server...")
    print("Admin Dashboard: http://localhost:5000/backbone/admin")
    print("Health API: http://localhost:5000/backbone/api/backbone/health")
    
    app.run(debug=True, host='0.0.0.0', port=5000)