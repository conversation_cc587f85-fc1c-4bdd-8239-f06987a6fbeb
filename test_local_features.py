#!/usr/bin/env python3
"""
HexStrike AI - Comprehensive Local Feature Testing Suite
Tests all application features on the correct port (8888)
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8888"
TEST_RESULTS = []

def log_test(name, status, duration, details=""):
    """Log test results"""
    result = {
        "test": name,
        "status": "✅ PASS" if status else "❌ FAIL",
        "duration": f"{duration:.0f}ms",
        "details": details,
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    TEST_RESULTS.append(result)
    print(f"{result['status']} {name} ({result['duration']})")
    if details:
        print(f"   {details}")

def test_endpoint(endpoint, method="GET", data=None, expected_status=200):
    """Test a specific endpoint"""
    start_time = time.time()
    try:
        url = f"{BASE_URL}{endpoint}"
        headers = {'Content-Type': 'application/json'}
        
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        
        duration = (time.time() - start_time) * 1000
        success = response.status_code == expected_status
        
        details = f"Status: {response.status_code}"
        if not success:
            details += f" (Expected: {expected_status})"
        
        return success, duration, details
    except Exception as e:
        duration = (time.time() - start_time) * 1000
        return False, duration, f"Error: {str(e)}"

def main():
    print("🚀 HexStrike AI - Comprehensive Local Feature Testing")
    print("=" * 60)
    print(f"Testing server at: {BASE_URL}")
    print()
    
    # Test 1: Core Application Pages
    print("🏠 Core Application Pages")
    core_pages = [
        ("/", "Main Dashboard"),
        ("/chat-interface", "Chat Interface"),
        ("/settings", "Settings Page"),
        ("/landing-page/", "Landing Page"),
        ("/all-features/", "All Features Page")
    ]
    
    for endpoint, name in core_pages:
        success, duration, details = test_endpoint(endpoint)
        log_test(name, success, duration, details)
    
    # Test 2: Health and Status Endpoints
    print("\n🏥 Health and Status")
    health_endpoints = [
        ("/health", "Health Check"),
        ("/api/features", "Features API"),
        ("/api/settings", "Settings API", "GET"),
        ("/api/scraping/status", "Scraping Status")
    ]
    
    for endpoint_data in health_endpoints:
        if len(endpoint_data) == 2:
            endpoint, name = endpoint_data
            method = "GET"
        else:
            endpoint, name, method = endpoint_data
        
        success, duration, details = test_endpoint(endpoint, method)
        log_test(name, success, duration, details)
    
    # Test 3: Authentication System
    print("\n🔐 Authentication System")
    auth_data = {
        "username": "test_user",
        "password": "test_password"
    }
    
    success, duration, details = test_endpoint("/api/auth/login", "POST", auth_data, 401)  # Expect 401 for invalid creds
    log_test("Authentication Login", True if "401" in details else False, duration, details)
    
    # Test 4: Chat System
    print("\n💬 AI Chat System")
    chat_data = {
        "message": "Hello, test message",
        "conversation_id": "test_conv_001"
    }
    
    success, duration, details = test_endpoint("/api/chat", "POST", chat_data)
    log_test("AI Chat API", success, duration, details)
    
    # Test 5: Core Security Tools
    print("\n🛡️ Core Security Tools")
    security_tools = [
        ("/api/tools/nmap", "NMAP Scanner", {"target": "127.0.0.1", "options": "-sn"}),
        ("/api/tools/nuclei", "Nuclei Scanner", {"target": "http://example.com", "templates": "basic"}),
        ("/api/tools/gobuster", "Gobuster", {"target": "http://example.com", "wordlist": "common"}),
        ("/api/tools/sqlmap", "SQLMap", {"target": "http://example.com", "data": "id=1"}),
        ("/api/tools/nikto", "Nikto Scanner", {"target": "http://example.com"})
    ]
    
    for endpoint, name, data in security_tools:
        success, duration, details = test_endpoint(endpoint, "POST", data)
        log_test(name, success, duration, details)
    
    # Test 6: Advanced Security Features
    print("\n🔬 Advanced Security Features")
    advanced_tools = [
        ("/api/tools/metasploit", "Metasploit", {"module": "auxiliary/scanner/portscan/tcp", "target": "127.0.0.1"}),
        ("/api/tools/hydra", "Hydra Brute Force", {"target": "127.0.0.1", "service": "ssh", "userlist": "admin", "passlist": "password"}),
        ("/api/tools/wpscan", "WordPress Scanner", {"target": "http://example.com"}),
        ("/api/tools/ffuf", "FFUF Fuzzer", {"target": "http://example.com/FUZZ", "wordlist": "common"})
    ]
    
    for endpoint, name, data in advanced_tools:
        success, duration, details = test_endpoint(endpoint, "POST", data)
        log_test(name, success, duration, details)
    
    # Test 7: Intelligence and Analysis
    print("\n🧠 AI Intelligence Features")
    intelligence_features = [
        ("/api/intelligence/analyze-target", "Target Analysis", {"target": "example.com"}),
        ("/api/intelligence/select-tools", "Tool Selection", {"target_type": "web_application"}),
        ("/api/intelligence/smart-scan", "Smart Scan", {"target": "example.com", "scan_type": "comprehensive"}),
        ("/api/ai/generate_payload", "Payload Generation", {"vulnerability_type": "xss", "target_context": "web"})
    ]
    
    for endpoint, name, data in intelligence_features:
        success, duration, details = test_endpoint(endpoint, "POST", data)
        log_test(name, success, duration, details)
    
    # Test 8: Bug Bounty Workflows
    print("\n🎯 Bug Bounty Workflows")
    bugbounty_workflows = [
        ("/api/bugbounty/reconnaissance-workflow", "Reconnaissance", {"target": "example.com"}),
        ("/api/bugbounty/vulnerability-hunting-workflow", "Vulnerability Hunting", {"target": "example.com"}),
        ("/api/bugbounty/osint-workflow", "OSINT Workflow", {"target": "example.com"})
    ]
    
    for endpoint, name, data in bugbounty_workflows:
        success, duration, details = test_endpoint(endpoint, "POST", data)
        log_test(name, success, duration, details)
    
    # Test 9: Process Management
    print("\n⚙️ Process Management")
    process_endpoints = [
        ("/api/processes/list", "Process List"),
        ("/api/processes/dashboard", "Process Dashboard"),
        ("/api/process/pool-stats", "Pool Statistics"),
        ("/api/process/health-check", "Health Check")
    ]
    
    for endpoint, name in process_endpoints:
        success, duration, details = test_endpoint(endpoint)
        log_test(name, success, duration, details)
    
    # Test 10: CTF Features
    print("\n🏁 CTF Features")
    ctf_features = [
        ("/api/ctf/suggest-tools", "CTF Tool Suggestions", {"challenge_type": "crypto"}),
        ("/api/ctf/cryptography-solver", "Crypto Solver", {"cipher_text": "test", "cipher_type": "caesar"}),
        ("/api/ctf/forensics-analyzer", "Forensics Analyzer", {"file_type": "image", "analysis_type": "metadata"})
    ]
    
    for endpoint, name, data in ctf_features:
        success, duration, details = test_endpoint(endpoint, "POST", data)
        log_test(name, success, duration, details)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Comprehensive Test Summary")
    print("=" * 60)
    
    total_tests = len(TEST_RESULTS)
    passed_tests = sum(1 for result in TEST_RESULTS if "✅" in result["status"])
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests} ✅")
    print(f"Failed: {failed_tests} ❌")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Categorize results
    critical_failures = []
    minor_failures = []
    
    for result in TEST_RESULTS:
        if "❌" in result["status"]:
            if any(keyword in result["test"].lower() for keyword in ["main", "chat", "health", "dashboard"]):
                critical_failures.append(result)
            else:
                minor_failures.append(result)
    
    if critical_failures:
        print("\n🚨 Critical Failures (Core Features):")
        for result in critical_failures:
            print(f"   - {result['test']}: {result['details']}")
    
    if minor_failures:
        print("\n⚠️ Minor Failures (Advanced Features):")
        for result in minor_failures[:5]:  # Show first 5 only
            print(f"   - {result['test']}: {result['details']}")
        if len(minor_failures) > 5:
            print(f"   ... and {len(minor_failures) - 5} more")
    
    print("\n🎉 Feature Testing Complete!")
    print("\n📋 Application Status:")
    
    if passed_tests >= total_tests * 0.7:  # 70% pass rate
        print("✅ Application is READY for deployment")
        print("   - Core features are functional")
        print("   - Most security tools are operational")
        print("   - AI chat system is working")
    elif passed_tests >= total_tests * 0.5:  # 50% pass rate
        print("⚠️ Application needs MINOR FIXES before deployment")
        print("   - Core features mostly working")
        print("   - Some advanced features need attention")
    else:
        print("❌ Application needs MAJOR FIXES before deployment")
        print("   - Critical features are failing")
        print("   - Requires immediate attention")
    
    # Save results to file
    with open("comprehensive_test_results.json", "w") as f:
        json.dump({
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%",
                "timestamp": datetime.now().isoformat()
            },
            "results": TEST_RESULTS
        }, f, indent=2)
    print("\n📄 Detailed results saved to comprehensive_test_results.json")

if __name__ == "__main__":
    main()