<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Advanced Security Platform</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <div class="container header-content">
            <div class="logo">
                <img src="logo.svg" alt="HexStrike AI Logo">
                <h1>HexStrike AI</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="/">Home</a></li>
                    <li><a href="/penetration-testing">Penetration Testing</a></li>
                    <li><a href="/security-analysis">Security Analysis</a></li>
                    <li><a href="/chat-window">Chat Window</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h2>AI-Powered Security Testing Platform</h2>
            <p>HexStrike AI combines advanced machine learning with comprehensive security testing to protect your applications and infrastructure from emerging threats.</p>
            <a href="/penetration-testing" class="cta-button">Start Testing</a>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <div class="section-title">
                <h2>Advanced Security Features</h2>
            </div>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>Penetration Testing</h3>
                    <p>Automated penetration testing with AI-driven vulnerability discovery and exploitation capabilities.</p>
                </div>
                <div class="feature-card">
                    <h3>Security Analysis</h3>
                    <p>Deep analysis of security posture with comprehensive reporting and remediation recommendations.</p>
                </div>
                <div class="feature-card">
                    <h3>Interactive Chat</h3>
                    <p>Communicate with our AI security assistant to understand vulnerabilities and receive guidance.</p>
                </div>
                <div class="feature-card">
                    <h3>Real-time Monitoring</h3>
                    <p>Continuous monitoring of security events with instant alerts for suspicious activities.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="status-section">
        <div class="container">
            <div class="section-title">
                <h2>Platform Status</h2>
            </div>
            <div class="status-container">
                <div class="status-item">
                    <h3>Version</h3>
                    <div class="status-value" id="version">6.0</div>
                </div>
                <div class="status-item">
                    <h3>Status</h3>
                    <div class="status-value operational" id="status">Operational</div>
                </div>
                <div class="status-item">
                    <h3>Last Updated</h3>
                    <div class="status-value" id="last-updated">Loading...</div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container footer-content">
            <div class="footer-column">
                <h3>HexStrike AI</h3>
                <p>Advanced security testing platform powered by artificial intelligence.</p>
            </div>
            <div class="footer-column">
                <h3>Quick Links</h3>
                <ul>
                    <li><a href="/">Home</a></li>
                    <li><a href="/penetration-testing">Penetration Testing</a></li>
                    <li><a href="/security-analysis">Security Analysis</a></li>
                    <li><a href="/chat-window">Chat Window</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>Resources</h3>
                <ul>
                    <li><a href="#">Documentation</a></li>
                    <li><a href="#">API Reference</a></li>
                    <li><a href="#">Security Blog</a></li>
                </ul>
            </div>
        </div>
        <div class="container copyright">
            <p>&copy; 2025 HexStrike AI. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Update the last updated timestamp and platform data
        document.addEventListener('DOMContentLoaded', function() {
            // Fetch platform data from root endpoint
            fetch('/')
                .then(response => response.text())
                .then(text => {
                    try {
                        // Try to parse the JSON data
                        const data = JSON.parse(text);
                        
                        // Update platform information
                        if (data.name) {
                            document.title = data.name;
                            document.querySelector('.hero h2').textContent = data.name;
                        }
                        
                        if (data.description) {
                            document.querySelector('.hero p').textContent = data.description;
                        }
                        
                        // Update version
                        if (data.version) {
                            document.getElementById('version').textContent = data.version;
                        }
                        
                        // Update status
                        if (data.status) {
                            const statusElement = document.getElementById('status');
                            statusElement.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                            statusElement.className = data.status === 'operational' ? 
                                'status-value operational' : 'status-value';
                        }
                        
                        // Update timestamp
                        if (data.timestamp) {
                            const lastUpdated = document.getElementById('last-updated');
                            const date = new Date(data.timestamp);
                            lastUpdated.textContent = date.toLocaleString();
                        }
                        
                        // Update features if available
                        if (data.features && Array.isArray(data.features) && data.features.length > 0) {
                            const featureGrid = document.querySelector('.feature-grid');
                            featureGrid.innerHTML = ''; // Clear existing features
                            
                            // Add each feature as a card
                            data.features.forEach(feature => {
                                const featureCard = document.createElement('div');
                                featureCard.className = 'feature-card';
                                featureCard.innerHTML = `
                                    <h3>${feature}</h3>
                                    <p>Advanced security capability powered by AI.</p>
                                `;
                                featureGrid.appendChild(featureCard);
                            });
                        }
                        
                        // Update endpoints if available
                        if (data.endpoints) {
                            const footerLinks = document.querySelector('.footer-column:nth-child(3) ul');
                            footerLinks.innerHTML = ''; // Clear existing links
                            
                            // Add each endpoint as a link
                            for (const [name, path] of Object.entries(data.endpoints)) {
                                const li = document.createElement('li');
                                const a = document.createElement('a');
                                a.href = path;
                                a.textContent = name.charAt(0).toUpperCase() + name.slice(1).replace('_', ' ');
                                li.appendChild(a);
                                footerLinks.appendChild(li);
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                        // If JSON parsing fails, try to fetch from health endpoint as fallback
                        fetchHealthData();
                    }
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                    // If fetching fails, try to fetch from health endpoint as fallback
                    fetchHealthData();
                });
                
            function fetchHealthData() {
                fetch('/health')
                    .then(response => response.json())
                    .then(data => {
                        const statusElement = document.getElementById('status');
                        if (data && data.all_essential_tools_available !== undefined) {
                            statusElement.textContent = data.all_essential_tools_available ? 
                                'All Systems Operational' : 'Partially Operational';
                            statusElement.className = data.all_essential_tools_available ? 
                                'status-value operational' : 'status-value';
                        }
                        
                        const lastUpdated = document.getElementById('last-updated');
                        const now = new Date();
                        lastUpdated.textContent = now.toLocaleString();
                    })
                    .catch(error => {
                        console.error('Error fetching health data:', error);
                    });
            }
        });
    </script>
</body>
</html>