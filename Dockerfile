# Multi-stage build for HexStrike AI Chat Box Server
# Security-focused production-ready container

# Stage 1: Base image with security updates
FROM python:3.11-slim-bullseye AS base

# Security: Create non-root user
RUN groupadd -r chatbox && useradd -r -g chatbox -s /bin/false chatbox

# Security: Update system packages
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Stage 2: Dependencies installation
FROM base AS dependencies

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    python3-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Stage 3: Application
FROM base AS application

# Copy Python packages from dependencies stage
COPY --from=dependencies /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Set working directory
WORKDIR /app

# Copy application code
COPY --chown=chatbox:chatbox . .

# Security: Remove sensitive files
RUN rm -f .env* *.key *.pem *.p12 *.pfx 2>/dev/null || true

# Security: Set proper permissions
RUN chmod -R 755 /app && \
    chmod -R 644 /app/*.py && \
    chmod +x /app/chat_box_server.py

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/temp && \
    chown -R chatbox:chatbox /app/logs /app/uploads /app/temp

# Security: Switch to non-root user
USER chatbox

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${CHAT_BOX_PORT:-3000}/health || exit 1

# Environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONIOENCODING=utf-8 \
    FLASK_ENV=production \
    CHAT_BOX_PORT=3000

# Expose port
EXPOSE 3000

# Start application
CMD ["python", "chat_box_server.py"]