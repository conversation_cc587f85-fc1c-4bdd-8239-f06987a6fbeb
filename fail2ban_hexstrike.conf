
# Fail2Ban configuration for HexStrike AI
# Save as /etc/fail2ban/jail.local

[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5
backend = auto

[hexstrike-auth]
enabled = true
port = http,https
filter = hexstrike-auth
logpath = /path/to/hexstrike_ai.log
maxretry = 3
bantime = 7200

# Create filter file: /etc/fail2ban/filter.d/hexstrike-auth.conf
# [Definition]
# failregex = .*Authentication failed.*remote_addr=<HOST>
# ignoreregex =
