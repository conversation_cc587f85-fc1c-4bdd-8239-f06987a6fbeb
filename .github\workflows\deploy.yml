name: Production Deployment

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      version:
        description: 'Version to deploy'
        required: true
        default: 'latest'

env:
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: hexstrike-ai

jobs:
  # Pre-deployment validation
  validate:
    name: Pre-deployment Validation
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      environment: ${{ steps.env.outputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Determine version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "release" ]; then
            VERSION=${{ github.event.release.tag_name }}
          else
            VERSION=${{ github.event.inputs.version }}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Deploying version: $VERSION"

      - name: Determine environment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "release" ]; then
            ENVIRONMENT="production"
          else
            ENVIRONMENT="${{ github.event.inputs.environment }}"
          fi
          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "Deploying to: $ENVIRONMENT"

      - name: Validate deployment prerequisites
        run: |
          echo "Validating deployment prerequisites..."
          # Add validation logic here
          # Example: Check if required secrets are set
          if [ -z "${{ secrets.DEPLOY_KEY }}" ]; then
            echo "Error: DEPLOY_KEY secret not set"
            exit 1
          fi

  # Build and push Docker images
  build:
    name: Build & Push Images
    runs-on: ubuntu-latest
    needs: validate
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=${{ needs.validate.outputs.version }}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push main application
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Build and push worker
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.worker
          push: true
          tags: |
            ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/worker:${{ needs.validate.outputs.version }}
            ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/worker:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Sign container images
        run: |
          echo "Signing container images with cosign..."
          # Add cosign signing logic here if needed
          # cosign sign ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:${{ needs.validate.outputs.version }}

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [validate, build]
    if: needs.validate.outputs.environment == 'staging' || (github.event_name == 'release' && github.event.release.prerelease)
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup deployment environment
        run: |
          echo "Setting up staging deployment..."
          # Create staging environment configuration
          cp .env.docker .env.staging
          echo "ENVIRONMENT=staging" >> .env.staging
          echo "IMAGE_TAG=${{ needs.validate.outputs.version }}" >> .env.staging

      - name: Deploy to staging infrastructure
        run: |
          echo "Deploying to staging..."
          # Add your staging deployment commands here
          # Examples:
          # - kubectl apply -f k8s/staging/
          # - docker-compose -f docker-compose.staging.yml up -d
          # - terraform apply -var="image_tag=${{ needs.validate.outputs.version }}"
          
          # Simulate deployment
          echo "Deployment to staging completed"

      - name: Wait for deployment
        run: |
          echo "Waiting for staging deployment to be ready..."
          sleep 30

      - name: Run staging smoke tests
        run: |
          echo "Running staging smoke tests..."
          # Add smoke test commands
          # curl -f https://staging.hexstrike.ai/health
          # python tests/smoke/staging_tests.py

      - name: Run staging integration tests
        run: |
          echo "Running staging integration tests..."
          # Add integration test commands
          # pytest tests/integration/ --env=staging

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [validate, build, deploy-staging]
    if: needs.validate.outputs.environment == 'production' && (github.event_name == 'release' && !github.event.release.prerelease)
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup production environment
        run: |
          echo "Setting up production deployment..."
          cp .env.docker .env.production
          echo "ENVIRONMENT=production" >> .env.production
          echo "IMAGE_TAG=${{ needs.validate.outputs.version }}" >> .env.production

      - name: Create deployment backup
        run: |
          echo "Creating pre-deployment backup..."
          # Add backup commands
          # kubectl exec deployment/postgres -- pg_dump -U hexstrike hexstrike_ai > backup-$(date +%Y%m%d-%H%M%S).sql

      - name: Deploy to production (Blue-Green)
        run: |
          echo "Starting blue-green deployment to production..."
          
          # Blue-green deployment strategy
          echo "Step 1: Deploy to green environment"
          # Deploy new version to green environment
          
          echo "Step 2: Run health checks on green"
          # Verify green environment is healthy
          
          echo "Step 3: Switch traffic to green"
          # Update load balancer to point to green
          
          echo "Step 4: Monitor for issues"
          # Monitor metrics and logs
          
          echo "Production deployment completed"

      - name: Run production smoke tests
        run: |
          echo "Running production smoke tests..."
          # Add production smoke tests
          # curl -f https://hexstrike.ai/health

      - name: Update monitoring and alerting
        run: |
          echo "Updating monitoring dashboards..."
          # Update monitoring configurations
          # Update alerting rules

  # Post-deployment validation
  validate-deployment:
    name: Post-deployment Validation
    runs-on: ubuntu-latest
    needs: [validate, deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    
    steps:
      - name: Run comprehensive tests
        run: |
          echo "Running comprehensive post-deployment tests..."
          # Add comprehensive test suite
          # pytest tests/e2e/ --env=${{ needs.validate.outputs.environment }}

      - name: Performance validation
        run: |
          echo "Running performance validation..."
          # Add performance tests
          # k6 run tests/performance/production-load-test.js

      - name: Security validation
        run: |
          echo "Running security validation..."
          # Add security tests
          # nmap -sV production-host
          # zap-baseline.py -t https://hexstrike.ai

  # Rollback capability
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && github.event_name == 'workflow_dispatch'
    environment: ${{ needs.validate.outputs.environment }}
    needs: [validate, deploy-staging, deploy-production]
    
    steps:
      - name: Rollback deployment
        run: |
          echo "Rolling back deployment..."
          # Add rollback commands
          # kubectl rollout undo deployment/hexstrike-ai
          # docker-compose -f docker-compose.prod.yml down
          # docker-compose -f docker-compose.prod.yml up -d --scale app=0

      - name: Verify rollback
        run: |
          echo "Verifying rollback..."
          # Add rollback verification

  # Notification
  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [validate, deploy-staging, deploy-production, validate-deployment]
    if: always()
    
    steps:
      - name: Determine deployment status
        id: status
        run: |
          if [ "${{ needs.deploy-production.result }}" = "success" ]; then
            echo "status=✅ Production deployment successful" >> $GITHUB_OUTPUT
            echo "color=good" >> $GITHUB_OUTPUT
          elif [ "${{ needs.deploy-staging.result }}" = "success" ]; then
            echo "status=🟡 Staging deployment successful" >> $GITHUB_OUTPUT
            echo "color=warning" >> $GITHUB_OUTPUT
          else
            echo "status=❌ Deployment failed" >> $GITHUB_OUTPUT
            echo "color=danger" >> $GITHUB_OUTPUT
          fi

      - name: Send Slack notification
        if: always()
        run: |
          echo "Sending deployment notification..."
          # Add Slack notification
          # curl -X POST -H 'Content-type: application/json' \
          #   --data '{"text":"${{ steps.status.outputs.status }}\nVersion: ${{ needs.validate.outputs.version }}\nEnvironment: ${{ needs.validate.outputs.environment }}"}' \
          #   ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Update deployment status
        run: |
          echo "Updating deployment status in monitoring systems..."
          # Update deployment tracking
          # Update status page

      - name: Generate deployment report
        run: |
          echo "# Deployment Report" > deployment-report.md
          echo "" >> deployment-report.md
          echo "**Version:** ${{ needs.validate.outputs.version }}" >> deployment-report.md
          echo "**Environment:** ${{ needs.validate.outputs.environment }}" >> deployment-report.md
          echo "**Status:** ${{ steps.status.outputs.status }}" >> deployment-report.md
          echo "**Timestamp:** $(date)" >> deployment-report.md
          echo "**Commit:** ${{ github.sha }}" >> deployment-report.md
          echo "" >> deployment-report.md
          echo "## Deployment Steps" >> deployment-report.md
          echo "- Build: ${{ needs.build.result }}" >> deployment-report.md
          echo "- Staging: ${{ needs.deploy-staging.result }}" >> deployment-report.md
          echo "- Production: ${{ needs.deploy-production.result }}" >> deployment-report.md
          echo "- Validation: ${{ needs.validate-deployment.result }}" >> deployment-report.md

      - name: Upload deployment report
        uses: actions/upload-artifact@v3
        with:
          name: deployment-report
          path: deployment-report.md