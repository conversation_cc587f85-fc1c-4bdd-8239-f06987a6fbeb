#!/usr/bin/env python3
"""
HexStrike AI - Production Server Startup Script
Configured for production deployment with proper security settings
"""

import os
import sys
from app_factory import create_app
from models import db

def main():
    """Start the production server"""
    
    # Set production environment
    os.environ['FLASK_ENV'] = 'production'
    
    # Validate required environment variables
    required_vars = [
        'SECRET_KEY',
        'JWT_SECRET_KEY',
        'DATABASE_URL',
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'SUPABASE_SERVICE_ROLE_KEY',
        'REDIS_URL'
    ]
    
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    if missing_vars:
        print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env.production file and ensure all variables are set.")
        sys.exit(1)
    
    # Create Flask app with production configuration
    app = create_app('production')
    
    # Verify database connection
    try:
        with app.app_context():
            db.engine.execute('SELECT 1')
        print("✓ Database connection verified")
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        print("Please check your DATABASE_URL and ensure the database is accessible.")
        sys.exit(1)
    
    # Production server settings
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    workers = int(os.environ.get('WORKERS', 4))
    
    print(f"Starting HexStrike AI in production mode...")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Workers: {workers}")
    
    # Use Gunicorn for production
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        # Gunicorn configuration
        sys.argv = [
            'gunicorn',
            '--bind', f'{host}:{port}',
            '--workers', str(workers),
            '--worker-class', 'sync',
            '--worker-connections', '1000',
            '--max-requests', '1000',
            '--max-requests-jitter', '100',
            '--timeout', '30',
            '--keep-alive', '2',
            '--preload',
            '--access-logfile', 'logs/access.log',
            '--error-logfile', 'logs/error.log',
            '--log-level', 'info',
            '--capture-output',
            'app_factory:create_app()'
        ]
        
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
        # Start Gunicorn
        wsgi.run()
        
    except ImportError:
        print("Warning: Gunicorn not available, falling back to Flask development server")
        print("For production deployment, please install Gunicorn: pip install gunicorn")
        
        # Fallback to Flask development server (not recommended for production)
        app.run(
            host=host,
            port=port,
            debug=False,
            threaded=True
        )

if __name__ == '__main__':
    main()