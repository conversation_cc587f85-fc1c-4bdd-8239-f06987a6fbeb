#!/usr/bin/env python3
"""
HexStrike AI - Database Seed Script
Creates initial admin user and sample data
"""

import os
import sys
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# Add project root to path
sys.path.insert(0, os.path.dirname(__file__))

from app_factory import create_app
from models import db, User, Job, Report, AuditLog, ChatSession

def create_admin_user():
    """Create default admin user"""
    admin = User.query.filter_by(username='admin').first()
    if admin:
        print("Admin user already exists")
        return admin
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        role='admin',
        first_name='System',
        last_name='Administrator',
        organization='HexStrike AI',
        is_active=True,
        is_verified=True
    )
    admin.set_password('HexStrike2024!')
    
    db.session.add(admin)
    db.session.commit()
    
    print(f"Created admin user: {admin.username} (ID: {admin.id})")
    return admin

def create_test_users():
    """Create test users for different roles"""
    users = []
    
    # Security Analyst
    analyst = User.query.filter_by(username='analyst').first()
    if not analyst:
        analyst = User(
            username='analyst',
            email='<EMAIL>',
            role='analyst',
            first_name='Security',
            last_name='Analyst',
            organization='HexStrike AI',
            is_active=True,
            is_verified=True
        )
        analyst.set_password('Analyst2024!')
        db.session.add(analyst)
        users.append(analyst)
    
    # Guest User
    guest = User.query.filter_by(username='guest').first()
    if not guest:
        guest = User(
            username='guest',
            email='<EMAIL>',
            role='guest',
            first_name='Guest',
            last_name='User',
            organization='External',
            is_active=True,
            is_verified=False
        )
        guest.set_password('Guest2024!')
        db.session.add(guest)
        users.append(guest)
    
    if users:
        db.session.commit()
        print(f"Created {len(users)} test users")
    
    return users

def create_sample_jobs(admin_user):
    """Create sample jobs for testing"""
    sample_jobs = [
        {
            'job_type': 'security_scan',
            'tool_name': 'nmap',
            'parameters': {
                'target': '***********',
                'scan_type': 'tcp_syn',
                'ports': '1-1000'
            },
            'status': 'completed',
            'priority': 'high'
        },
        {
            'job_type': 'vulnerability_scan',
            'tool_name': 'nikto',
            'parameters': {
                'target': 'https://example.com',
                'options': ['-ssl']
            },
            'status': 'running',
            'priority': 'medium'
        },
        {
            'job_type': 'malware_analysis',
            'tool_name': 'yara',
            'parameters': {
                'file_path': '/tmp/sample.exe',
                'rules': 'malware_rules.yar'
            },
            'status': 'pending',
            'priority': 'high'
        }
    ]
    
    created_jobs = []
    for job_data in sample_jobs:
        job = Job(
            user_id=admin_user.id,
            **job_data
        )
        
        # Set timestamps based on status
        if job.status in ['running', 'completed']:
            job.started_at = datetime.utcnow() - timedelta(minutes=30)
        
        if job.status == 'completed':
            job.completed_at = datetime.utcnow() - timedelta(minutes=5)
            job.result = {
                'findings': ['Open port 22/tcp', 'Open port 80/tcp'],
                'summary': 'Scan completed successfully'
            }
        
        db.session.add(job)
        created_jobs.append(job)
    
    db.session.commit()
    print(f"Created {len(created_jobs)} sample jobs")
    return created_jobs

def create_sample_reports(admin_user, jobs):
    """Create sample security reports"""
    sample_reports = [
        {
            'title': 'Network Security Assessment - ***********/24',
            'description': 'Comprehensive network scan of internal subnet',
            'report_type': 'network_scan',
            'target': '***********/24',
            'target_type': 'network',
            'findings': [
                {
                    'id': 1,
                    'severity': 'medium',
                    'title': 'Open SSH Service',
                    'description': 'SSH service detected on port 22',
                    'recommendation': 'Ensure SSH is properly configured'
                },
                {
                    'id': 2,
                    'severity': 'low',
                    'title': 'HTTP Service Detected',
                    'description': 'Web server running on port 80',
                    'recommendation': 'Consider using HTTPS'
                }
            ],
            'severity': 'medium',
            'confidence': 'high',
            'status': 'published'
        },
        {
            'title': 'Web Application Security Test - example.com',
            'description': 'Security assessment of web application',
            'report_type': 'web_scan',
            'target': 'https://example.com',
            'target_type': 'url',
            'findings': [
                {
                    'id': 1,
                    'severity': 'high',
                    'title': 'Missing Security Headers',
                    'description': 'Critical security headers not implemented',
                    'recommendation': 'Implement CSP, HSTS, and other security headers'
                }
            ],
            'severity': 'high',
            'confidence': 'high',
            'status': 'draft'
        }
    ]
    
    created_reports = []
    for i, report_data in enumerate(sample_reports):
        job_id = jobs[i].id if i < len(jobs) else None
        
        report = Report(
            user_id=admin_user.id,
            job_id=job_id,
            **report_data
        )
        
        if report.status == 'published':
            report.published_at = datetime.utcnow() - timedelta(hours=1)
        
        db.session.add(report)
        created_reports.append(report)
    
    db.session.commit()
    print(f"Created {len(created_reports)} sample reports")
    return created_reports

def create_sample_audit_logs(admin_user):
    """Create sample audit log entries"""
    sample_logs = [
        {
            'action': 'user_login',
            'resource_type': 'authentication',
            'ip_address': '*************',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'outcome': 'success',
            'severity': 'low',
            'category': 'authentication',
            'details': {'login_method': 'password'}
        },
        {
            'action': 'job_created',
            'resource_type': 'job',
            'resource_id': 'job_123',
            'ip_address': '*************',
            'outcome': 'success',
            'severity': 'medium',
            'category': 'data_access',
            'details': {'tool_name': 'nmap', 'target': '***********'}
        },
        {
            'action': 'report_published',
            'resource_type': 'report',
            'resource_id': 'report_456',
            'ip_address': '*************',
            'outcome': 'success',
            'severity': 'medium',
            'category': 'data_modification',
            'details': {'report_type': 'network_scan'}
        }
    ]
    
    created_logs = []
    for log_data in sample_logs:
        log_entry = AuditLog(
            user_id=admin_user.id,
            correlation_id=f"corr_{datetime.utcnow().timestamp()}",
            **log_data
        )
        db.session.add(log_entry)
        created_logs.append(log_entry)
    
    db.session.commit()
    print(f"Created {len(created_logs)} sample audit log entries")
    return created_logs

def create_sample_chat_session(admin_user):
    """Create sample chat session"""
    session = ChatSession(
        user_id=admin_user.id,
        title='Security Assessment Planning',
        description='Discussion about upcoming security assessment'
    )
    
    # Add sample messages
    session.add_message(
        role='user',
        content='I need to perform a security assessment of our web application.',
        metadata={'timestamp': datetime.utcnow().isoformat()}
    )
    
    session.add_message(
        role='assistant',
        content='I can help you with that. What type of assessment are you looking to perform? We have tools for vulnerability scanning, penetration testing, and code analysis.',
        metadata={'timestamp': datetime.utcnow().isoformat()}
    )
    
    session.add_message(
        role='user',
        content='Let\'s start with a basic vulnerability scan using Nikto.',
        metadata={'timestamp': datetime.utcnow().isoformat()}
    )
    
    db.session.add(session)
    db.session.commit()
    
    print(f"Created sample chat session: {session.id}")
    return session

def main():
    """Main seeding function"""
    print("Starting database seeding...")
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database tables created")
        
        # Create users
        admin_user = create_admin_user()
        test_users = create_test_users()
        
        # Create sample data
        jobs = create_sample_jobs(admin_user)
        reports = create_sample_reports(admin_user, jobs)
        audit_logs = create_sample_audit_logs(admin_user)
        chat_session = create_sample_chat_session(admin_user)
        
        print("\n=== Database Seeding Complete ===")
        print(f"Admin credentials:")
        print(f"  Username: admin")
        print(f"  Password: HexStrike2024!")
        print(f"  Email: <EMAIL>")
        print(f"\nTest users created:")
        print(f"  Analyst - analyst:Analyst2024!")
        print(f"  Guest - guest:Guest2024!")
        print(f"\nSample data:")
        print(f"  Jobs: {len(jobs)}")
        print(f"  Reports: {len(reports)}")
        print(f"  Audit logs: {len(audit_logs)}")
        print(f"  Chat sessions: 1")

if __name__ == '__main__':
    main()