{"summary": {"total_tests": 10, "passed": 4, "failed": 6, "success_rate": 40.0, "avg_response_time_ms": 1239.0, "test_timestamp": "2025-09-11T08:03:28.990828"}, "results": [{"test": "Health Check", "success": false, "details": "Error: HTTPConnectionPool(host='localhost', port=8888): Read timed out. (read timeout=10)", "response_time_ms": 0, "timestamp": "2025-09-11T08:02:55.427375"}, {"test": "Root Documentation", "success": false, "details": "Error: Expecting value: line 1 column 1 (char 0)", "response_time_ms": 0, "timestamp": "2025-09-11T08:02:57.466213"}, {"test": "Authentication Login", "success": true, "details": "<PERSON><PERSON> received", "response_time_ms": 2067.25, "timestamp": "2025-09-11T08:02:59.573079"}, {"test": "Token Verification", "success": true, "details": "User: admin", "response_time_ms": 2059.21, "timestamp": "2025-09-11T08:03:01.633213"}, {"test": "Public Test Endpoint", "success": false, "details": "HTTP 404", "response_time_ms": 2068.4, "timestamp": "2025-09-11T08:03:03.702448"}, {"test": "Features Endpoint", "success": true, "details": "Response received", "response_time_ms": 2090.33, "timestamp": "2025-09-11T08:03:05.793581"}, {"test": "Protected Test Endpoint", "success": false, "details": "HTTP 404", "response_time_ms": 2068.6, "timestamp": "2025-09-11T08:03:07.862554"}, {"test": "CORS Headers", "success": true, "details": "Headers present: ['Access-Control-Allow-Origin', 'Access-Control-Allow-Methods', 'Access-Control-Allow-Headers']", "response_time_ms": 2036.3, "timestamp": "2025-09-11T08:03:09.899194"}, {"test": "Security Headers", "success": false, "details": "Error: HTTPConnectionPool(host='localhost', port=8888): Read timed out. (read timeout=10)", "response_time_ms": 0, "timestamp": "2025-09-11T08:03:21.949648"}, {"test": "Rate Limiting", "success": false, "details": "Error: HTTPConnectionPool(host='localhost', port=8888): Read timed out. (read timeout=5)", "response_time_ms": 0, "timestamp": "2025-09-11T08:03:28.988541"}]}