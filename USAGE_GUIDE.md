# HexStrike AI - Usage Guide

## 🚀 Quick Start

Your HexStrike AI application is now running locally and ready for use!

### Access Points
- **Main Application**: http://localhost:8888
- **Chat Interface**: http://localhost:8888/chat-interface
- **Settings**: http://localhost:8888/settings

### Default Credentials
- **Username**: `admin`
- **Password**: `hexstrike2024`

## 🎯 Core Features

### 1. Security Tools Integration (100+ Tools)
The application integrates with over 100 security tools across multiple categories:

#### Network Security
- **Nmap**: Network discovery and security auditing
- **Masscan**: High-speed port scanner
- **Rustscan**: Fast port scanner
- **Autorecon**: Automated reconnaissance

#### Web Security
- **Gobuster**: Directory/file brute-forcer
- **Nuclei**: Vulnerability scanner
- **Nikto**: Web server scanner
- **SQLMap**: SQL injection testing
- **FFUF**: Fast web fuzzer

#### Password Security
- **Hydra**: Login brute-forcer
- **<PERSON> the Ripper**: Password cracker
- **Hashcat**: Advanced password recovery

#### Binary Analysis
- **Ghidra**: Reverse engineering
- **Radare2**: Binary analysis
- **GDB**: Debugger
- **Binwalk**: Firmware analysis

### 2. AI-Powered Features

#### Payload Generation
```python
# Example API call
POST /api/ai/generate_payload
{
    "target_type": "web",
    "vulnerability": "xss",
    "context": "input field"
}
```

#### Target Analysis
```python
# Example API call
POST /api/intelligence/analyze-target
{
    "target": "example.com",
    "scan_type": "comprehensive"
}
```

### 3. Automated Workflows

#### Bug Bounty Workflows
- **Reconnaissance**: Automated subdomain discovery and enumeration
- **Vulnerability Hunting**: Systematic vulnerability assessment
- **Business Logic Testing**: Application logic flaw detection

#### CTF Workflows
- **Tool Suggestion**: AI-powered tool recommendations
- **Challenge Solving**: Automated challenge analysis
- **Cryptography Solving**: Crypto challenge assistance

### 4. File Operations
- Create, modify, and delete files
- File listing and management
- Secure file handling

### 5. Database Integration
- Supabase integration for data persistence
- Real-time data synchronization
- Secure data storage

## 🌐 Web Interface Usage

### Main Interface
1. Navigate to http://localhost:8888
2. Log in with admin credentials
3. Access the main dashboard

### Chat Interface
1. Go to http://localhost:8888/chat-interface
2. Interactive chat with AI assistant
3. Natural language tool execution

### Settings
1. Visit http://localhost:8888/settings
2. Configure application preferences
3. Manage user settings

## 🔧 API Usage Examples

### Authentication
```bash
curl -X POST http://localhost:8888/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "hexstrike2024"}'
```

### Run Nmap Scan
```bash
curl -X POST http://localhost:8888/api/tools/nmap \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"target": "127.0.0.1", "options": ["-sn"]}'
```

### Get Available Features
```bash
curl -X GET http://localhost:8888/api/features \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🛡️ Security Best Practices

### 1. Change Default Credentials
- Update the default admin password
- Use strong, unique passwords

### 2. Network Security
- Run on localhost for testing
- Use HTTPS in production
- Configure firewall rules

### 3. Tool Usage
- Only scan targets you own or have permission to test
- Follow responsible disclosure practices
- Respect rate limits and target resources

## 🎯 Common Use Cases

### 1. Penetration Testing
```python
# Reconnaissance workflow
POST /api/bugbounty/reconnaissance-workflow
{
    "target": "target.com",
    "scope": "subdomain"
}
```

### 2. Vulnerability Assessment
```python
# Nuclei scan
POST /api/tools/nuclei
{
    "target": "https://target.com",
    "templates": ["cves", "vulnerabilities"]
}
```

### 3. CTF Challenges
```python
# Get tool suggestions
POST /api/ctf/suggest-tools
{
    "challenge_type": "web",
    "difficulty": "medium"
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify credentials: admin/hexstrike2024
   - Check if server is running

2. **Tool Not Found**
   - Install required security tools
   - Check tool availability in system PATH

3. **Timeout Errors**
   - Increase timeout values
   - Check network connectivity

4. **Permission Denied**
   - Run with appropriate permissions
   - Check file/directory permissions

### Health Check
Visit http://localhost:8888/health to check application status (note: this endpoint may be slow due to comprehensive tool checking).

## 📊 Monitoring and Logs

### Application Logs
- Check terminal output for real-time logs
- Monitor for errors and warnings

### Performance Monitoring
- Use `/api/telemetry` endpoint for stats
- Monitor resource usage

## 🚀 Advanced Usage

### Custom Tool Integration
1. Add new tools to the tool registry
2. Implement tool-specific handlers
3. Update API endpoints

### Workflow Customization
1. Modify existing workflows
2. Create custom automation scripts
3. Integrate with external systems

### AI Model Configuration
1. Configure AI model parameters
2. Train custom models
3. Implement custom AI features

## 📚 Additional Resources

- **API Documentation**: Available at the root endpoint
- **Tool Documentation**: Check individual tool help pages
- **Community**: Join security communities for best practices

## 🎉 Getting Started Checklist

- [ ] Application is running on http://localhost:8888
- [ ] Successfully logged in with admin credentials
- [ ] Explored the web interface
- [ ] Tested basic API endpoints
- [ ] Ran a simple security tool (e.g., Nmap)
- [ ] Tried AI payload generation
- [ ] Explored workflow features
- [ ] Configured settings as needed

---

**Happy Hacking! 🎯**

Remember to always use these tools ethically and only on systems you own or have explicit permission to test.