#!/usr/bin/env python3
"""
HexStrike AI - Production Logging Configuration
Configures structured logging, monitoring, and alerting
"""

import os
import sys
import json
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

import structlog
from pythonjsonlogger import jsonlogger

class ProductionLoggingConfig:
    """Production logging configuration"""
    
    def __init__(self, app_name: str = "hexstrike-ai"):
        self.app_name = app_name
        self.log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.log_format = os.getenv('LOG_FORMAT', 'json').lower()
        self.log_dir = Path(os.getenv('LOG_DIR', 'logs'))
        self.max_file_size = int(os.getenv('LOG_MAX_FILE_SIZE', '10485760'))  # 10MB
        self.backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))
        
        # Create log directory
        self.log_dir.mkdir(exist_ok=True)
    
    def setup_structured_logging(self):
        """Configure structured logging with structlog"""
        
        # Configure structlog processors
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
        ]
        
        if self.log_format == 'json':
            processors.append(structlog.processors.JSONRenderer())
        else:
            processors.append(structlog.dev.ConsoleRenderer())
        
        structlog.configure(
            processors=processors,
            wrapper_class=structlog.stdlib.BoundLogger,
            logger_factory=structlog.stdlib.LoggerFactory(),
            context_class=dict,
            cache_logger_on_first_use=True,
        )
    
    def setup_file_handlers(self) -> Dict[str, logging.Handler]:
        """Setup rotating file handlers for different log levels"""
        handlers = {}
        
        # Application log (all levels)
        app_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.app_name}.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        
        # Error log (ERROR and CRITICAL only)
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.app_name}-error.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        error_handler.setLevel(logging.ERROR)
        
        # Access log (for web requests)
        access_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.app_name}-access.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        
        # Security log (for security events)
        security_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.app_name}-security.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        
        # Configure formatters
        if self.log_format == 'json':
            formatter = jsonlogger.JsonFormatter(
                '%(asctime)s %(name)s %(levelname)s %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        for handler in [app_handler, error_handler, access_handler, security_handler]:
            handler.setFormatter(formatter)
        
        handlers.update({
            'app': app_handler,
            'error': error_handler,
            'access': access_handler,
            'security': security_handler
        })
        
        return handlers
    
    def setup_console_handler(self) -> logging.Handler:
        """Setup console handler for development"""
        console_handler = logging.StreamHandler(sys.stdout)
        
        if self.log_format == 'json':
            formatter = jsonlogger.JsonFormatter(
                '%(asctime)s %(name)s %(levelname)s %(message)s'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        console_handler.setFormatter(formatter)
        return console_handler
    
    def configure_flask_logging(self, app):
        """Configure Flask application logging"""
        
        # Remove default Flask handlers
        app.logger.handlers.clear()
        
        # Setup handlers
        handlers = self.setup_file_handlers()
        console_handler = self.setup_console_handler()
        
        # Add handlers to Flask logger
        app.logger.addHandler(handlers['app'])
        app.logger.addHandler(handlers['error'])
        app.logger.addHandler(console_handler)
        
        # Set log level
        app.logger.setLevel(getattr(logging, self.log_level))
        
        # Configure Werkzeug (Flask's WSGI server) logging
        werkzeug_logger = logging.getLogger('werkzeug')
        werkzeug_logger.addHandler(handlers['access'])
        werkzeug_logger.setLevel(logging.INFO)
        
        # Configure security logger
        security_logger = logging.getLogger('security')
        security_logger.addHandler(handlers['security'])
        security_logger.setLevel(logging.INFO)
        
        return {
            'app': app.logger,
            'security': security_logger,
            'access': werkzeug_logger
        }

class SecurityLogger:
    """Security event logger"""
    
    def __init__(self):
        self.logger = logging.getLogger('security')
    
    def log_auth_attempt(self, username: str, success: bool, ip_address: str, user_agent: str = None):
        """Log authentication attempt"""
        event = {
            'event_type': 'auth_attempt',
            'username': username,
            'success': success,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if success:
            self.logger.info("Authentication successful", extra=event)
        else:
            self.logger.warning("Authentication failed", extra=event)
    
    def log_permission_denied(self, username: str, resource: str, ip_address: str):
        """Log permission denied event"""
        event = {
            'event_type': 'permission_denied',
            'username': username,
            'resource': resource,
            'ip_address': ip_address,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        self.logger.warning("Permission denied", extra=event)
    
    def log_suspicious_activity(self, description: str, ip_address: str, details: Dict[str, Any] = None):
        """Log suspicious activity"""
        event = {
            'event_type': 'suspicious_activity',
            'description': description,
            'ip_address': ip_address,
            'details': details or {},
            'timestamp': datetime.utcnow().isoformat()
        }
        
        self.logger.error("Suspicious activity detected", extra=event)

class PerformanceLogger:
    """Performance monitoring logger"""
    
    def __init__(self):
        self.logger = logging.getLogger('performance')
    
    def log_request_metrics(self, endpoint: str, method: str, response_time: float, 
                          status_code: int, user_id: str = None):
        """Log request performance metrics"""
        metrics = {
            'event_type': 'request_metrics',
            'endpoint': endpoint,
            'method': method,
            'response_time_ms': round(response_time * 1000, 2),
            'status_code': status_code,
            'user_id': user_id,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        self.logger.info("Request completed", extra=metrics)
    
    def log_database_query(self, query_type: str, execution_time: float, table: str = None):
        """Log database query performance"""
        metrics = {
            'event_type': 'database_query',
            'query_type': query_type,
            'execution_time_ms': round(execution_time * 1000, 2),
            'table': table,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if execution_time > 1.0:  # Log slow queries
            self.logger.warning("Slow database query", extra=metrics)
        else:
            self.logger.debug("Database query completed", extra=metrics)

def setup_production_logging(app, app_name: str = "hexstrike-ai"):
    """Setup production logging for Flask application"""
    
    config = ProductionLoggingConfig(app_name)
    
    # Setup structured logging
    config.setup_structured_logging()
    
    # Configure Flask logging
    loggers = config.configure_flask_logging(app)
    
    # Log startup
    app.logger.info(f"Production logging configured for {app_name}", extra={
        'log_level': config.log_level,
        'log_format': config.log_format,
        'log_dir': str(config.log_dir)
    })
    
    return {
        'config': config,
        'loggers': loggers,
        'security_logger': SecurityLogger(),
        'performance_logger': PerformanceLogger()
    }

# Example usage in Flask app
def configure_app_logging(app):
    """Configure logging for the Flask application"""
    
    # Setup production logging
    logging_setup = setup_production_logging(app)
    
    # Store loggers in app context for easy access
    app.security_logger = logging_setup['security_logger']
    app.performance_logger = logging_setup['performance_logger']
    
    return logging_setup

if __name__ == '__main__':
    # Test logging configuration
    from flask import Flask
    
    app = Flask(__name__)
    logging_setup = configure_app_logging(app)
    
    # Test different log levels
    app.logger.debug("Debug message")
    app.logger.info("Info message")
    app.logger.warning("Warning message")
    app.logger.error("Error message")
    
    # Test security logging
    app.security_logger.log_auth_attempt("admin", True, "127.0.0.1", "Test User Agent")
    app.security_logger.log_permission_denied("user", "/admin", "127.0.0.1")
    
    # Test performance logging
    app.performance_logger.log_request_metrics("/api/test", "GET", 0.123, 200, "user123")
    
    print("Logging configuration test completed. Check logs/ directory for output.")