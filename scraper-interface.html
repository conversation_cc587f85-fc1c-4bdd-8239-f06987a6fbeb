<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Scraper Interface - HexStrike AI</title>
    <style>
        :root {
            --primary-color: #e63946;
            --secondary-color: #457b9d;
            --dark-bg: #1d3557;
            --light-bg: #f1faee;
            --accent-color: #a8dadc;
            --text-light: #f1faee;
            --text-dark: #1d3557;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 20px 0;
            text-align: center;
        }
        
        header h1 {
            margin-bottom: 10px;
        }
        
        .scraper-form {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="url"],
        select,
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #c1121f;
        }
        
        .results {
            margin-top: 30px;
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .results pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        .back-button {
            display: inline-block;
            margin-top: 20px;
            background-color: var(--secondary-color);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
        }
        
        .back-button:hover {
            background-color: #3d6990;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>HexStrike AI - Web Scraper Interface</h1>
            <p>Extract data from websites with advanced scraping capabilities</p>
        </div>
    </header>
    
    <div class="container">
        <div class="scraper-form">
            <h2>Configure Scraper</h2>
            <form id="scraperForm">
                <div class="form-group">
                    <label for="targetUrl">Target URL:</label>
                    <input type="url" id="targetUrl" name="targetUrl" placeholder="https://example.com" required>
                </div>
                
                <div class="form-group">
                    <label for="scraperType">Scraper Type:</label>
                    <select id="scraperType" name="scraperType">
                        <option value="static">Static HTML</option>
                        <option value="dynamic">Dynamic (JavaScript Rendered)</option>
                        <option value="authenticated">Authenticated Session</option>
                        <option value="api">API Endpoint</option>
                        <option value="headless">Headless Browser</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Data Extraction Options:</label>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-top: 10px;">
                        <div>
                            <input type="checkbox" id="extractText" name="extractOptions" value="text" checked>
                            <label for="extractText" style="display: inline; font-weight: normal;">Text Content</label>
                        </div>
                        <div>
                            <input type="checkbox" id="extractLinks" name="extractOptions" value="links">
                            <label for="extractLinks" style="display: inline; font-weight: normal;">Links</label>
                        </div>
                        <div>
                            <input type="checkbox" id="extractImages" name="extractOptions" value="images">
                            <label for="extractImages" style="display: inline; font-weight: normal;">Images</label>
                        </div>
                        <div>
                            <input type="checkbox" id="extractTables" name="extractOptions" value="tables">
                            <label for="extractTables" style="display: inline; font-weight: normal;">Tables</label>
                        </div>
                        <div>
                            <input type="checkbox" id="extractMetadata" name="extractOptions" value="metadata">
                            <label for="extractMetadata" style="display: inline; font-weight: normal;">Page Metadata</label>
                        </div>
                        <div>
                            <input type="checkbox" id="extractStructured" name="extractOptions" value="structured">
                            <label for="extractStructured" style="display: inline; font-weight: normal;">Structured Data</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="selectors">CSS Selectors (one per line):</label>
                    <textarea id="selectors" name="selectors" rows="5" placeholder=".article-title
.article-content
.price
img.product-image[src]
table.data-table"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="scrapingFrequency">Scraping Frequency:</label>
                    <select id="scrapingFrequency" name="scrapingFrequency">
                        <option value="once">One-time</option>
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="custom">Custom Schedule</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="dataFiltering">Data Filtering (JSON format):</label>
                    <textarea id="dataFiltering" name="dataFiltering" rows="3" placeholder='{"min_price": 10, "max_price": 100, "include_keywords": ["sale", "discount"], "exclude_keywords": ["sold out"]}'></textarea>
                </div>
                
                <div class="form-group">
                    <label for="outputFormat">Output Format:</label>
                    <select id="outputFormat" name="outputFormat">
                        <option value="json">JSON</option>
                        <option value="csv">CSV</option>
                        <option value="xml">XML</option>
                        <option value="excel">Excel</option>
                        <option value="database">Database</option>
                    </select>
                </div>
                
                <button type="submit">Start Scraping</button>
            </form>
        </div>
        
        <div class="results" style="display: none;">
            <h2>Scraping Results</h2>
            <pre id="resultsOutput">Results will appear here...</pre>
        </div>
        
        <a href="/" class="back-button">Back to Main Menu</a>
    </div>
    
    <script>
        // Show/hide custom schedule option based on frequency selection
        document.getElementById('scrapingFrequency').addEventListener('change', function() {
            const customScheduleDiv = document.getElementById('customScheduleDiv');
            if (this.value === 'custom' && !customScheduleDiv) {
                // Create custom schedule input if it doesn't exist
                const formGroup = document.createElement('div');
                formGroup.className = 'form-group';
                formGroup.id = 'customScheduleDiv';
                
                const label = document.createElement('label');
                label.setAttribute('for', 'customSchedule');
                label.textContent = 'Custom Schedule (cron format):';
                
                const input = document.createElement('input');
                input.type = 'text';
                input.id = 'customSchedule';
                input.name = 'customSchedule';
                input.placeholder = '0 */6 * * *';
                
                formGroup.appendChild(label);
                formGroup.appendChild(input);
                
                // Insert after the frequency dropdown
                this.parentNode.after(formGroup);
            } else if (this.value !== 'custom' && customScheduleDiv) {
                customScheduleDiv.remove();
            }
        });

        document.getElementById('scraperForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get selected extraction options
            const extractionOptions = [];
            document.querySelectorAll('input[name="extractOptions"]:checked').forEach(checkbox => {
                extractionOptions.push(checkbox.value);
            });
            
            // Show loading state
            const resultsSection = document.querySelector('.results');
            resultsSection.style.display = 'block';
            document.getElementById('resultsOutput').textContent = 'Scraping in progress...';
            
            // In a real implementation, this would send the data to a backend
            // For demo purposes, we'll simulate a response after a delay
            setTimeout(() => {
                // Create demo results based on selected options
                const demoResult = {
                    "status": "success",
                    "url": document.getElementById('targetUrl').value,
                    "scraper_type": document.getElementById('scraperType').value,
                    "extraction_options": extractionOptions,
                    "frequency": document.getElementById('scrapingFrequency').value,
                    "timestamp": new Date().toISOString(),
                    "data": []
                };
                
                // Add different types of data based on extraction options
                if (extractionOptions.includes('text')) {
                    demoResult.data.push({
                        "type": "text",
                        "selector": ".article-title",
                        "matches": 3,
                        "values": [
                            "Sample Article Title 1",
                            "Sample Article Title 2",
                            "Sample Article Title 3"
                        ]
                    });
                }
                
                if (extractionOptions.includes('links')) {
                    demoResult.data.push({
                        "type": "links",
                        "selector": "a.product-link",
                        "matches": 4,
                        "values": [
                            {"text": "Product 1", "href": "/product/1"},
                            {"text": "Product 2", "href": "/product/2"},
                            {"text": "Product 3", "href": "/product/3"},
                            {"text": "Product 4", "href": "/product/4"}
                        ]
                    });
                }
                
                if (extractionOptions.includes('images')) {
                    demoResult.data.push({
                        "type": "images",
                        "selector": "img.product-image",
                        "matches": 2,
                        "values": [
                            {"alt": "Product 1 Image", "src": "/images/product1.jpg", "width": "300", "height": "200"},
                            {"alt": "Product 2 Image", "src": "/images/product2.jpg", "width": "300", "height": "200"}
                        ]
                    });
                }
                
                if (extractionOptions.includes('tables')) {
                    demoResult.data.push({
                        "type": "table",
                        "selector": "table.data-table",
                        "matches": 1,
                        "values": [
                            {
                                "headers": ["Product", "Price", "Stock"],
                                "rows": [
                                    ["Product A", "$19.99", "In Stock"],
                                    ["Product B", "$24.99", "Low Stock"],
                                    ["Product C", "$15.50", "Out of Stock"]
                                ]
                            }
                        ]
                    });
                }
                
                if (extractionOptions.includes('metadata')) {
                    demoResult.data.push({
                        "type": "metadata",
                        "matches": 1,
                        "values": {
                            "title": "Sample Product Page",
                            "description": "This is a sample product page for demonstration",
                            "keywords": "sample, product, demo",
                            "og:image": "https://example.com/og-image.jpg"
                        }
                    });
                }
                
                // Add filtering information if provided
                const filteringText = document.getElementById('dataFiltering').value.trim();
                if (filteringText) {
                    try {
                        const filteringJson = JSON.parse(filteringText);
                        demoResult.filtering = filteringJson;
                        demoResult.filtered_results = Math.floor(Math.random() * 10) + 1;
                    } catch (e) {
                        demoResult.filtering_error = "Invalid JSON format";
                    }
                }
                
                document.getElementById('resultsOutput').textContent = JSON.stringify(demoResult, null, 2);
            }, 2000);
        });
    </script>
</body>
</html>