#!/usr/bin/env python3
"""
HexStrike AI - Secure Flask Application Factory
Implements security best practices with OWASP compliance
"""

import os
import uuid
import logging
from datetime import timed<PERSON><PERSON>
from typing import Optional

from flask import Flask, request, g
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import structlog
from pythonjsonlogger import jsonlogger
from supabase_client import supabase_manager

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["60 per minute"],
    storage_uri="redis://localhost:6379"
)

def create_app(config_name: str = 'development') -> Flask:
    """
    Application factory pattern with security hardening
    
    Args:
        config_name: Configuration environment (development, staging, production)
        
    Returns:
        Configured Flask application instance
    """
    app = Flask(__name__)
    
    # Load configuration
    configure_app(app, config_name)
    
    # Configure structured logging
    configure_logging(app)
    
    # Initialize extensions
    initialize_extensions(app)
    
    # Configure security headers
    configure_security_headers(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Add request middleware
    configure_middleware(app)
    
    return app

def configure_app(app: Flask, config_name: str) -> None:
    """
    Configure Flask application with security-focused settings
    """
    # Base configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or os.urandom(32)
    app.config['JSON_SORT_KEYS'] = False
    app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB limit
    
    # Database configuration - Supabase PostgreSQL
    supabase_url = os.environ.get('SUPABASE_URL', 'https://lcwgdbheihfccapmnbsp.supabase.co')
    supabase_anon_key = os.environ.get('SUPABASE_ANON_KEY')
    
    # Construct Supabase PostgreSQL connection string
    # Note: You'll need to set the actual password in the DATABASE_URL
    database_url = os.environ.get('DATABASE_URL', 'postgresql://postgres:[YOUR-PASSWORD]@db.lcwgdbheihfccapmnbsp.supabase.co:5432/postgres')
    
    app.config['SQLALCHEMY_DATABASE_URI'] = database_url
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {'sslmode': 'require'}  # Supabase requires SSL
    }
    
    # Supabase configuration
    app.config['SUPABASE_URL'] = supabase_url
    app.config['SUPABASE_ANON_KEY'] = supabase_anon_key
    
    # Redis configuration
    app.config['REDIS_URL'] = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
    
    # JWT Configuration with 30-minute expiry
    app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY') or os.urandom(32)
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(minutes=30)
    app.config['JWT_ALGORITHM'] = 'HS256'
    app.config['JWT_BLACKLIST_ENABLED'] = True
    app.config['JWT_BLACKLIST_TOKEN_CHECKS'] = ['access']
    
    # Environment-specific configuration
    if config_name == 'production':
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
        app.config['SSL_REDIRECT'] = True
    elif config_name == 'staging':
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
    else:  # development
        app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
        app.config['TESTING'] = False

def configure_logging(app: Flask) -> None:
    """
    Configure structured JSON logging with correlation IDs
    """
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure Flask logging
    if not app.debug:
        # JSON formatter for production
        json_formatter = jsonlogger.JsonFormatter(
            '%(asctime)s %(name)s %(levelname)s %(correlation_id)s %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler('hexstrike_ai.log')
        file_handler.setFormatter(json_formatter)
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        # Set log level
        app.logger.setLevel(logging.INFO)
        app.logger.info('HexStrike AI startup')

def initialize_extensions(app: Flask) -> None:
    """
    Initialize Flask extensions with security configurations
    """
    # Database
    db.init_app(app)
    migrate.init_app(app, db)
    
    # JWT
    jwt.init_app(app)
    
    # Rate limiting
    limiter.init_app(app)
    
    # Supabase
    supabase_manager.init_app(app)
    
    # CORS with strict configuration
    allowed_origins = [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'http://localhost:8080',
        'http://127.0.0.1:8080'
    ]
    
    # Add production origins if configured
    if os.environ.get('ALLOWED_ORIGINS'):
        allowed_origins.extend(os.environ.get('ALLOWED_ORIGINS').split(','))
    
    CORS(app, 
         origins=allowed_origins,
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
         allow_headers=['Content-Type', 'Authorization'],
         supports_credentials=True,
         max_age=86400  # 24 hours
    )

def configure_security_headers(app: Flask) -> None:
    """
    Configure OWASP security headers
    """
    @app.after_request
    def set_security_headers(response):
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self' ws: wss:; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
        response.headers['Content-Security-Policy'] = csp
        
        # Other security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # HSTS for HTTPS
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response

def configure_middleware(app: Flask) -> None:
    """
    Configure request middleware for correlation IDs and security
    """
    @app.before_request
    def before_request():
        # Generate correlation ID for request tracing
        g.correlation_id = request.headers.get('X-Correlation-ID', str(uuid.uuid4()))
        
        # Add correlation ID to logs
        app.logger = structlog.get_logger().bind(correlation_id=g.correlation_id)
        
        # Log request details (excluding sensitive data)
        app.logger.info(
            "Request received",
            method=request.method,
            path=request.path,
            remote_addr=request.remote_addr,
            user_agent=request.headers.get('User-Agent', 'Unknown')
        )
    
    @app.after_request
    def after_request(response):
        # Add correlation ID to response headers
        response.headers['X-Correlation-ID'] = g.correlation_id
        
        # Log response
        app.logger.info(
            "Request completed",
            status_code=response.status_code,
            content_length=response.content_length
        )
        
        return response

def register_blueprints(app: Flask) -> None:
    """
    Register application blueprints
    """
    from auth_routes import auth_bp, register_jwt_callbacks
    
    # Register authentication blueprint
    app.register_blueprint(auth_bp)
    
    # Register JWT callbacks
    register_jwt_callbacks(jwt)
    
    # Additional blueprints will be registered here as they are created
    pass

# JWT token blacklist (in production, use Redis)
blacklisted_tokens = set()

@jwt.token_in_blocklist_loader
def check_if_token_revoked(jwt_header, jwt_payload):
    """Check if JWT token is blacklisted"""
    jti = jwt_payload['jti']
    return jti in blacklisted_tokens

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    """Handle expired tokens"""
    return {'error': 'Token has expired'}, 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    """Handle invalid tokens"""
    return {'error': 'Invalid token'}, 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    """Handle missing tokens"""
    return {'error': 'Authorization token required'}, 401