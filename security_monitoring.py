#!/usr/bin/env python3
"""
Security Monitoring and Audit System
Provides comprehensive security monitoring, logging, and alerting
"""

import logging
import json
import time
import os
from datetime import datetime, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading

# Configure logging
logger = logging.getLogger(__name__)

class ThreatLevel(Enum):
    """Threat severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class SecurityEvent:
    """Security event data structure"""
    timestamp: datetime
    event_type: str
    threat_level: ThreatLevel
    source_ip: str
    user_agent: str
    endpoint: str
    description: str
    details: Dict[str, Any]
    blocked: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['threat_level'] = self.threat_level.value
        return data

class SecurityMonitor:
    """Main security monitoring class"""
    
    def __init__(self, log_file: str = "security_events.log", alert_threshold: int = 10):
        self.log_file = log_file
        self.alert_threshold = alert_threshold
        self.events = deque(maxlen=10000)  # Keep last 10k events in memory
        self.ip_activity = defaultdict(list)
        self.threat_counts = defaultdict(int)
        self.blocked_ips = set()
        self.alert_cooldown = defaultdict(float)
        self.setup_logging()
        
        # Start background monitoring thread
        self.monitoring_thread = threading.Thread(target=self._background_monitor, daemon=True)
        self.monitoring_thread.start()
    
    def setup_logging(self):
        """Setup security event logging"""
        # Create security logger
        self.security_logger = logging.getLogger('security_events')
        self.security_logger.setLevel(logging.INFO)
        
        # Create file handler
        handler = logging.FileHandler(self.log_file)
        handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        # Add handler to logger
        if not self.security_logger.handlers:
            self.security_logger.addHandler(handler)
    
    def log_security_event(self, event: SecurityEvent):
        """Log a security event"""
        # Add to memory store
        self.events.append(event)
        
        # Log to file
        self.security_logger.info(json.dumps(event.to_dict()))
        
        # Update IP activity tracking
        self.ip_activity[event.source_ip].append(event.timestamp)
        
        # Update threat counts
        self.threat_counts[event.threat_level] += 1
        
        # Check for alerts
        self._check_alerts(event)
        
        logger.info(f"Security event logged: {event.event_type} from {event.source_ip}")
    
    def _check_alerts(self, event: SecurityEvent):
        """Check if event should trigger alerts"""
        current_time = time.time()
        
        # Critical events always trigger alerts
        if event.threat_level == ThreatLevel.CRITICAL:
            self._send_alert(event, "Critical security threat detected")
            return
        
        # Check for repeated attacks from same IP
        recent_events = [
            e for e in self.events 
            if e.source_ip == event.source_ip and 
            (datetime.now() - e.timestamp).seconds < 300  # Last 5 minutes
        ]
        
        if len(recent_events) >= self.alert_threshold:
            # Check cooldown
            cooldown_key = f"ip_{event.source_ip}"
            if current_time - self.alert_cooldown.get(cooldown_key, 0) > 3600:  # 1 hour cooldown
                self._send_alert(event, f"Multiple security events from IP {event.source_ip}")
                self.alert_cooldown[cooldown_key] = current_time
                
                # Auto-block IP
                self.blocked_ips.add(event.source_ip)
                logger.warning(f"Auto-blocked IP due to repeated attacks: {event.source_ip}")
    
    def _send_alert(self, event: SecurityEvent, message: str):
        """Send security alert"""
        alert_data = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'event': event.to_dict(),
            'system': 'HexStrike AI Security Monitor'
        }
        
        # Log alert
        logger.critical(f"SECURITY ALERT: {message}")
        
        # In production, send email/SMS/webhook notifications
        self._log_alert(alert_data)
    
    def _log_alert(self, alert_data: Dict[str, Any]):
        """Log alert to separate alert file"""
        alert_file = "security_alerts.log"
        with open(alert_file, 'a') as f:
            f.write(json.dumps(alert_data) + '\n')
    
    def _background_monitor(self):
        """Background monitoring tasks"""
        while True:
            try:
                self._cleanup_old_events()
                self._generate_hourly_report()
                time.sleep(3600)  # Run every hour
            except Exception as e:
                logger.error(f"Background monitoring error: {e}")
                time.sleep(60)  # Wait 1 minute before retry
    
    def _cleanup_old_events(self):
        """Clean up old events from IP activity tracking"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for ip in list(self.ip_activity.keys()):
            self.ip_activity[ip] = [
                timestamp for timestamp in self.ip_activity[ip]
                if timestamp > cutoff_time
            ]
            
            # Remove empty entries
            if not self.ip_activity[ip]:
                del self.ip_activity[ip]
    
    def _generate_hourly_report(self):
        """Generate hourly security report"""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        
        recent_events = [
            event for event in self.events
            if event.timestamp > hour_ago
        ]
        
        if not recent_events:
            return
        
        report = {
            'timestamp': now.isoformat(),
            'period': 'last_hour',
            'total_events': len(recent_events),
            'threat_levels': {},
            'top_source_ips': {},
            'top_event_types': {},
            'blocked_events': sum(1 for e in recent_events if e.blocked)
        }
        
        # Count by threat level
        for event in recent_events:
            level = event.threat_level.value
            report['threat_levels'][level] = report['threat_levels'].get(level, 0) + 1
        
        # Count by source IP
        for event in recent_events:
            ip = event.source_ip
            report['top_source_ips'][ip] = report['top_source_ips'].get(ip, 0) + 1
        
        # Count by event type
        for event in recent_events:
            event_type = event.event_type
            report['top_event_types'][event_type] = report['top_event_types'].get(event_type, 0) + 1
        
        # Sort top items
        report['top_source_ips'] = dict(sorted(
            report['top_source_ips'].items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10])
        
        report['top_event_types'] = dict(sorted(
            report['top_event_types'].items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10])
        
        # Log report
        report_file = "security_reports.log"
        with open(report_file, 'a') as f:
            f.write(json.dumps(report) + '\n')
        
        logger.info(f"Hourly security report generated: {len(recent_events)} events")
    
    def get_security_dashboard(self) -> Dict[str, Any]:
        """Get security dashboard data"""
        now = datetime.now()
        last_24h = now - timedelta(hours=24)
        
        recent_events = [
            event for event in self.events
            if event.timestamp > last_24h
        ]
        
        dashboard = {
            'timestamp': now.isoformat(),
            'total_events_24h': len(recent_events),
            'blocked_events_24h': sum(1 for e in recent_events if e.blocked),
            'threat_levels_24h': {},
            'active_blocked_ips': len(self.blocked_ips),
            'top_threats_24h': {},
            'system_status': 'active'
        }
        
        # Count by threat level
        for event in recent_events:
            level = event.threat_level.value
            dashboard['threat_levels_24h'][level] = dashboard['threat_levels_24h'].get(level, 0) + 1
        
        # Count by event type
        for event in recent_events:
            event_type = event.event_type
            dashboard['top_threats_24h'][event_type] = dashboard['top_threats_24h'].get(event_type, 0) + 1
        
        # Sort top threats
        dashboard['top_threats_24h'] = dict(sorted(
            dashboard['top_threats_24h'].items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:5])
        
        return dashboard
    
    def is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is blocked"""
        return ip in self.blocked_ips
    
    def block_ip(self, ip: str, reason: str = "Manual block"):
        """Manually block an IP"""
        self.blocked_ips.add(ip)
        
        # Log the block event
        event = SecurityEvent(
            timestamp=datetime.now(),
            event_type="ip_blocked",
            threat_level=ThreatLevel.HIGH,
            source_ip=ip,
            user_agent="system",
            endpoint="system",
            description=f"IP manually blocked: {reason}",
            details={'reason': reason, 'action': 'manual_block'},
            blocked=True
        )
        
        self.log_security_event(event)
        logger.warning(f"IP manually blocked: {ip} - {reason}")
    
    def unblock_ip(self, ip: str):
        """Unblock an IP"""
        if ip in self.blocked_ips:
            self.blocked_ips.remove(ip)
            logger.info(f"IP unblocked: {ip}")

# Global security monitor instance
security_monitor = SecurityMonitor()

def log_security_event(event_type: str, threat_level: ThreatLevel, 
                      source_ip: str, user_agent: str, endpoint: str, 
                      description: str, details: Dict[str, Any] = None, 
                      blocked: bool = False):
    """Convenience function to log security events"""
    event = SecurityEvent(
        timestamp=datetime.now(),
        event_type=event_type,
        threat_level=threat_level,
        source_ip=source_ip,
        user_agent=user_agent or "unknown",
        endpoint=endpoint,
        description=description,
        details=details or {},
        blocked=blocked
    )
    
    security_monitor.log_security_event(event)

def security_monitoring_decorator(f):
    """Decorator to add security monitoring to endpoints"""
    from functools import wraps
    from flask import request
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if client_ip:
            client_ip = client_ip.split(',')[0].strip()
        
        user_agent = request.headers.get('User-Agent', '')
        endpoint = request.endpoint or request.path
        
        try:
            # Execute the function
            result = f(*args, **kwargs)
            
            # Log successful request
            log_security_event(
                event_type="api_access",
                threat_level=ThreatLevel.LOW,
                source_ip=client_ip,
                user_agent=user_agent,
                endpoint=endpoint,
                description=f"Successful API access to {endpoint}",
                details={
                    'method': request.method,
                    'status': 'success',
                    'response_time': time.time() - start_time
                }
            )
            
            return result
            
        except Exception as e:
            # Log failed request
            log_security_event(
                event_type="api_error",
                threat_level=ThreatLevel.MEDIUM,
                source_ip=client_ip,
                user_agent=user_agent,
                endpoint=endpoint,
                description=f"API error in {endpoint}: {str(e)}",
                details={
                    'method': request.method,
                    'error': str(e),
                    'response_time': time.time() - start_time
                }
            )
            
            raise
    
    return decorated_function