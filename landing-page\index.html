<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Premium Security Testing Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    borderRadius: {
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px'
                    },
                    boxShadow: {
                        'rest': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)',
                        'hover': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)',
                        'pressed': 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)'
                    },
                    colors: {
                        'brand': '#0B1220',
                        'bgDark': '#0B0F1A',
                        'fgDark': '#E5E7EB',
                        'mutedDark': '#9CA3AF',
                        'ringDark': '#334155'
                    },
                    transitionTimingFunction: {
                        'premium': 'cubic-bezier(0.2,0.8,0.2,1)'
                    },
                    transitionDuration: {
                        '220': '220ms'
                    }
                }
            }
        }
    </script>
    <style>
        .raised-card {
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            border: 1px solid #e5e7eb;
            position: relative;
        }
        .dark .raised-card {
            background: linear-gradient(145deg, #1f2937, #111827);
            border: 1px solid #374151;
        }
        .raised-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
            border-radius: inherit;
        }
        .dark .raised-card::before {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        }
        .tool-icon {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            transition: all 220ms cubic-bezier(0.2,0.8,0.2,1);
        }
        .dark .tool-icon {
            background: linear-gradient(135deg, #1e3a8a, #1d4ed8);
        }
        .carousel-container {
            scroll-snap-type: x mandatory;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        .carousel-container::-webkit-scrollbar {
            display: none;
        }
        .carousel-item {
            scroll-snap-align: center;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-slide-in {
            animation: slideIn 220ms cubic-bezier(0.2,0.8,0.2,1) forwards;
        }
        .skeleton {
            background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        .dark .skeleton {
            background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
            background-size: 200% 100%;
        }
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body class="bg-gradient-to-b from-white to-slate-50 dark:from-bgDark dark:to-bgDark transition-colors duration-220">
    <!-- Header -->
    <header class="sticky top-0 z-40 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="font-bold text-xl text-gray-900 dark:text-white">HexStrike AI</div>
                <nav class="hidden md:flex space-x-8">
                    <a href="#features" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">Features</a>
                    <a href="#tools" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">Tools</a>
                    <a href="/all-features/" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">All Features</a>
                    <a href="/chat-interface" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">Chat</a>
                </nav>
                <div class="flex items-center space-x-4">
                    <!-- Picker Variant Selector -->
                    <div class="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                        <button id="grid-btn" class="px-3 py-1 rounded text-sm transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm">
                            Grid
                        </button>
                        <button id="carousel-btn" class="px-3 py-1 rounded text-sm transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                            Carousel
                        </button>
                        <button id="palette-btn" class="px-3 py-1 rounded text-sm transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                            Palette ⌘K
                        </button>
                    </div>
                    <!-- Dark Mode Toggle -->
                    <button id="dark-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors" aria-label="Toggle dark mode">
                        <svg class="w-5 h-5 dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        <svg class="w-5 h-5 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>
                    <a href="/chat-interface" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors font-medium">
                        Get Started
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 animate-slide-in">
                Choose your tool. Get instant results.
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 animate-slide-in" style="animation-delay: 100ms">
                A unified workspace for scraping, testing, crawling, and AI insights.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-in" style="animation-delay: 200ms">
                <button id="primary-cta" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-220 hover:shadow-hover hover:-translate-y-0.5 active:shadow-pressed active:translate-y-0">
                    Start Free
                </button>
                <button class="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-220 hover:shadow-hover hover:-translate-y-0.5">
                    Watch Demo
                </button>
            </div>
            <!-- Preset Picker -->
            <div class="inline-flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1 animate-slide-in" style="animation-delay: 300ms">
                <button class="preset-btn px-4 py-2 rounded text-sm transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm" data-preset="default">
                    Default
                </button>
                <button class="preset-btn px-4 py-2 rounded text-sm transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" data-preset="scrape">
                    Scrape Suite
                </button>
                <button class="preset-btn px-4 py-2 rounded text-sm transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" data-preset="security">
                    Security Sweep
                </button>
                <button class="preset-btn px-4 py-2 rounded text-sm transition-colors text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" data-preset="insights">
                    Insights Pack
                </button>
            </div>
        </div>
    </section>

    <!-- Tool Picker Section -->
    <section id="tools" class="py-16 px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">
            <!-- Grid View -->
            <div id="grid-view" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" role="tablist">
                <!-- Tool cards will be populated by JavaScript -->
            </div>

            <!-- Carousel View -->
            <div id="carousel-view" class="hidden relative">
                <div class="carousel-container overflow-hidden rounded-2xl">
                    <div id="carousel-track" class="flex transition-transform duration-500 ease-premium">
                        <!-- Carousel items will be populated by JavaScript -->
                    </div>
                </div>
                <button id="carousel-prev" class="absolute left-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white dark:bg-gray-800 shadow-rest hover:shadow-hover transition-all duration-220 hover:-translate-y-1" aria-label="Previous tool">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button id="carousel-next" class="absolute right-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white dark:bg-gray-800 shadow-rest hover:shadow-hover transition-all duration-220 hover:-translate-y-1" aria-label="Next tool">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                <!-- Carousel indicators -->
                <div class="flex justify-center mt-6 space-x-2" id="carousel-indicators">
                    <!-- Indicators will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </section>

    <!-- Features Panel -->
    <section id="features" class="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div class="max-w-4xl mx-auto">
            <h2 id="features-title" class="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
                Scrape Features
            </h2>
            <div id="features-grid" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Features will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 px-4 sm:px-6 lg:px-8 border-t border-gray-200 dark:border-gray-700">
        <div class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center">
            <div class="font-bold text-xl text-gray-900 dark:text-white mb-4 md:mb-0">HexStrike AI</div>
            <div class="flex space-x-6">
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Privacy</a>
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Terms</a>
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Contact</a>
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">Status</a>
            </div>
        </div>
    </footer>

    <!-- Command Palette -->
    <div id="command-palette" class="fixed inset-0 z-50 hidden">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" id="palette-backdrop"></div>
        <div class="flex items-start justify-center pt-20 px-4 relative z-10">
            <div class="w-full max-w-lg bg-white dark:bg-gray-800 rounded-xl shadow-hover border border-gray-200 dark:border-gray-700 animate-slide-in">
                <div class="p-4">
                    <div class="flex items-center space-x-3 mb-4">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input
                            id="palette-search"
                            type="text"
                            placeholder="Search tools..."
                            class="flex-1 bg-transparent border-none outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                        />
                        <kbd class="px-2 py-1 text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded">ESC</kbd>
                    </div>
                    <div id="palette-results" class="space-y-2 max-h-64 overflow-y-auto">
                        <!-- Search results will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Application State
        const state = {
            pickerVariant: 'grid',
            activeTool: 'scrape',
            paletteOpen: false,
            darkMode: false,
            preset: 'default',
            currentSlide: 0,
            isAutoPlaying: true
        };

        // Tools Data
        const tools = [
            { key: 'scrape', title: 'Scrape', blurb: 'Extract clean data from pages & files.', icon: '⊞' },
            { key: 'test', title: 'Test', blurb: 'Run safe, allowlisted security checks.', icon: '🛡' },
            { key: 'crawl', title: 'Crawl', blurb: 'Map sites with depth & rules.', icon: '🕸' },
            { key: 'analyze', title: 'Analyze', blurb: 'Turn raw data into insights.', icon: '📊' }
        ];

        // Features Data
        const features = {
            scrape: ['Auto-selectors', 'PDF/DOCX/XLSX', 'Export CSV/JSON', 'Auth user/pass'],
            test: ['Allowlisted', 'Rate limits', 'OWASP checks', 'Remediation tips'],
            crawl: ['Depth aware', 'Queue+retry', 'Include/Exclude', 'Live pause/resume'],
            analyze: ['Insight cards', 'Entity extraction', 'Charts', 'Reports']
        };

        // Telemetry Function
        function trackEvent(event, data = {}) {
            if (window.analytics?.track) {
                window.analytics.track(event, data);
            }
            console.log('📊 Telemetry:', event, data);
        }

        // Dark Mode Detection and Toggle
        function initDarkMode() {
            const isDark = localStorage.getItem('darkMode') === 'true' || 
                          (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches);
            state.darkMode = isDark;
            document.documentElement.classList.toggle('dark', isDark);
        }

        function toggleDarkMode() {
            state.darkMode = !state.darkMode;
            document.documentElement.classList.toggle('dark', state.darkMode);
            localStorage.setItem('darkMode', state.darkMode);
            trackEvent('dark_mode_toggle', { enabled: state.darkMode });
        }

        // Tool Selection
        function selectTool(tool) {
            state.activeTool = tool.key;
            updateFeatures();
            updatePrimaryCTA(tool.title);
            trackEvent('tool_select', { key: tool.key, variant: state.pickerVariant });
            
            // Update active states
            document.querySelectorAll('.tool-card').forEach(card => {
                card.classList.toggle('ring-2', card.dataset.tool === tool.key);
                card.classList.toggle('ring-blue-500', card.dataset.tool === tool.key);
                card.classList.toggle('shadow-hover', card.dataset.tool === tool.key);
            });
        }

        // Update Features Panel
        function updateFeatures() {
            const tool = tools.find(t => t.key === state.activeTool);
            const featuresTitle = document.getElementById('features-title');
            const featuresGrid = document.getElementById('features-grid');
            
            featuresTitle.textContent = `${tool.title} Features`;
            
            const toolFeatures = features[state.activeTool] || [];
            featuresGrid.innerHTML = toolFeatures.map(feature => `
                <div class="flex items-center space-x-3 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-rest hover:shadow-hover transition-all duration-220 animate-slide-in">
                    <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span class="text-gray-900 dark:text-white">${feature}</span>
                </div>
            `).join('');
        }

        // Update Primary CTA
        function updatePrimaryCTA(toolTitle) {
            const primaryCTA = document.getElementById('primary-cta');
            primaryCTA.textContent = `Start ${toolTitle}`;
        }

        // Create Tool Card
        function createToolCard(tool, isActive = false) {
            return `
                <div class="tool-card raised-card p-6 rounded-2xl cursor-pointer group transition-all duration-220 ease-premium hover:shadow-hover hover:-translate-y-1 focus-visible:ring-2 focus-visible:ring-blue-500 active:shadow-pressed active:translate-y-0 ${isActive ? 'ring-2 ring-blue-500 shadow-hover' : 'shadow-rest'}" 
                     data-tool="${tool.key}" 
                     tabindex="0" 
                     role="tab" 
                     aria-selected="${isActive}">
                    <div class="flex flex-col items-center text-center space-y-3">
                        <div class="tool-icon w-12 h-12 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-220">
                            <span class="text-blue-600 dark:text-blue-400 text-xl font-semibold">${tool.icon}</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-gray-100">${tool.title}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">${tool.blurb}</p>
                    </div>
                </div>
            `;
        }

        // Render Grid View
        function renderGridView() {
            const gridView = document.getElementById('grid-view');
            gridView.innerHTML = tools.map(tool => 
                createToolCard(tool, tool.key === state.activeTool)
            ).join('');
            
            // Add event listeners
            gridView.querySelectorAll('.tool-card').forEach(card => {
                card.addEventListener('click', () => {
                    const tool = tools.find(t => t.key === card.dataset.tool);
                    selectTool(tool);
                });
                
                card.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        const tool = tools.find(t => t.key === card.dataset.tool);
                        selectTool(tool);
                    }
                });
            });
        }

        // Render Carousel View
        function renderCarouselView() {
            const carouselTrack = document.getElementById('carousel-track');
            const indicators = document.getElementById('carousel-indicators');
            
            carouselTrack.innerHTML = tools.map(tool => `
                <div class="carousel-item w-full flex-shrink-0 px-4">
                    <div class="max-w-md mx-auto">
                        ${createToolCard(tool, false)}
                    </div>
                </div>
            `).join('');
            
            indicators.innerHTML = tools.map((_, index) => `
                <button class="w-2 h-2 rounded-full transition-colors duration-220 ${
                    index === state.currentSlide ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                }" data-slide="${index}"></button>
            `).join('');
            
            // Add event listeners
            carouselTrack.querySelectorAll('.tool-card').forEach((card, index) => {
                card.addEventListener('click', () => {
                    const tool = tools[index];
                    selectTool(tool);
                    state.currentSlide = index;
                    updateCarousel();
                });
            });
            
            indicators.querySelectorAll('button').forEach((btn, index) => {
                btn.addEventListener('click', () => {
                    state.currentSlide = index;
                    updateCarousel();
                });
            });
        }

        // Update Carousel Position
        function updateCarousel() {
            const carouselTrack = document.getElementById('carousel-track');
            const indicators = document.getElementById('carousel-indicators');
            
            carouselTrack.style.transform = `translateX(-${state.currentSlide * 100}%)`;
            
            indicators.querySelectorAll('button').forEach((btn, index) => {
                btn.classList.toggle('bg-blue-600', index === state.currentSlide);
                btn.classList.toggle('bg-gray-300', index !== state.currentSlide);
                btn.classList.toggle('dark:bg-gray-600', index !== state.currentSlide);
            });
        }

        // Carousel Auto-play
        function startAutoPlay() {
            if (state.pickerVariant === 'carousel' && state.isAutoPlaying) {
                setTimeout(() => {
                    if (state.isAutoPlaying && state.pickerVariant === 'carousel') {
                        state.currentSlide = (state.currentSlide + 1) % tools.length;
                        updateCarousel();
                        startAutoPlay();
                    }
                }, 4200);
            }
        }

        // Switch Picker Variant
        function switchPickerVariant(variant) {
            state.pickerVariant = variant;
            
            // Update button states
            document.querySelectorAll('[id$="-btn"]').forEach(btn => {
                const isActive = btn.id === `${variant}-btn`;
                btn.classList.toggle('bg-white', isActive);
                btn.classList.toggle('dark:bg-gray-700', isActive);
                btn.classList.toggle('text-gray-900', isActive);
                btn.classList.toggle('dark:text-white', isActive);
                btn.classList.toggle('shadow-sm', isActive);
                btn.classList.toggle('text-gray-600', !isActive);
                btn.classList.toggle('dark:text-gray-400', !isActive);
            });
            
            // Show/hide views
            document.getElementById('grid-view').classList.toggle('hidden', variant !== 'grid');
            document.getElementById('carousel-view').classList.toggle('hidden', variant !== 'carousel');
            
            if (variant === 'carousel') {
                renderCarouselView();
                updateCarousel();
                startAutoPlay();
            } else if (variant === 'grid') {
                renderGridView();
            } else if (variant === 'palette') {
                openCommandPalette();
            }
            
            trackEvent('picker_variant_change', { variant });
        }

        // Command Palette
        function openCommandPalette() {
            state.paletteOpen = true;
            document.getElementById('command-palette').classList.remove('hidden');
            document.getElementById('palette-search').focus();
            renderPaletteResults(tools);
            trackEvent('palette_open');
        }

        function closeCommandPalette() {
            state.paletteOpen = false;
            document.getElementById('command-palette').classList.add('hidden');
            document.getElementById('palette-search').value = '';
        }

        function renderPaletteResults(results) {
            const paletteResults = document.getElementById('palette-results');
            paletteResults.innerHTML = results.map(tool => `
                <button class="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-220 palette-item" data-tool="${tool.key}">
                    <div class="flex items-center space-x-3">
                        <span class="text-lg">${tool.icon}</span>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-gray-100">${tool.title}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">${tool.blurb}</div>
                        </div>
                    </div>
                </button>
            `).join('');
            
            // Add event listeners
            paletteResults.querySelectorAll('.palette-item').forEach(item => {
                item.addEventListener('click', () => {
                    const tool = tools.find(t => t.key === item.dataset.tool);
                    selectTool(tool);
                    closeCommandPalette();
                });
            });
        }

        // Preset Management
        function applyPreset(preset) {
            state.preset = preset;
            
            // Update preset button states
            document.querySelectorAll('.preset-btn').forEach(btn => {
                const isActive = btn.dataset.preset === preset;
                btn.classList.toggle('bg-white', isActive);
                btn.classList.toggle('dark:bg-gray-700', isActive);
                btn.classList.toggle('text-gray-900', isActive);
                btn.classList.toggle('dark:text-white', isActive);
                btn.classList.toggle('shadow-sm', isActive);
                btn.classList.toggle('text-gray-600', !isActive);
                btn.classList.toggle('dark:text-gray-400', !isActive);
            });
            
            // Apply preset logic
            const presetMappings = {
                'default': 'scrape',
                'scrape': 'scrape',
                'security': 'test',
                'insights': 'analyze'
            };
            
            const tool = tools.find(t => t.key === presetMappings[preset]);
            if (tool) {
                selectTool(tool);
            }
            
            // Save to session storage
            sessionStorage.setItem('hexstrike_preset', preset);
            trackEvent('preset_apply', { preset });
        }

        // Initialize Application
        function init() {
            // Initialize dark mode
            initDarkMode();
            
            // Load saved preset
            const savedPreset = sessionStorage.getItem('hexstrike_preset') || 'default';
            applyPreset(savedPreset);
            
            // Render initial view
            renderGridView();
            updateFeatures();
            
            // Event Listeners
            document.getElementById('dark-toggle').addEventListener('click', toggleDarkMode);
            
            // Picker variant buttons
            document.getElementById('grid-btn').addEventListener('click', () => switchPickerVariant('grid'));
            document.getElementById('carousel-btn').addEventListener('click', () => switchPickerVariant('carousel'));
            document.getElementById('palette-btn').addEventListener('click', () => switchPickerVariant('palette'));
            
            // Carousel controls
            document.getElementById('carousel-prev').addEventListener('click', () => {
                state.currentSlide = (state.currentSlide - 1 + tools.length) % tools.length;
                updateCarousel();
            });
            
            document.getElementById('carousel-next').addEventListener('click', () => {
                state.currentSlide = (state.currentSlide + 1) % tools.length;
                updateCarousel();
            });
            
            // Preset buttons
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.addEventListener('click', () => applyPreset(btn.dataset.preset));
            });
            
            // Command palette
            document.getElementById('palette-backdrop').addEventListener('click', closeCommandPalette);
            document.getElementById('palette-search').addEventListener('input', (e) => {
                const query = e.target.value.toLowerCase();
                const filtered = tools.filter(tool => 
                    tool.title.toLowerCase().includes(query) || 
                    tool.blurb.toLowerCase().includes(query)
                );
                renderPaletteResults(filtered);
            });
            
            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                    e.preventDefault();
                    if (state.paletteOpen) {
                        closeCommandPalette();
                    } else {
                        openCommandPalette();
                    }
                }
                
                if (e.key === 'Escape' && state.paletteOpen) {
                    closeCommandPalette();
                }
            });
            
            // Pause carousel on hover
            document.getElementById('carousel-view').addEventListener('mouseenter', () => {
                state.isAutoPlaying = false;
            });
            
            document.getElementById('carousel-view').addEventListener('mouseleave', () => {
                state.isAutoPlaying = true;
                startAutoPlay();
            });
            
            // Start auto-play
            startAutoPlay();
            
            console.log('🚀 HexStrike AI Premium Landing Page Initialized');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
    </script>
</body>
</html>