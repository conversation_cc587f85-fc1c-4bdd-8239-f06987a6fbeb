assessment_info:
  session_id: hexstrike_assessment_20250912_220748
  timestamp: '2025-09-12T22:10:47.221804'
  total_findings: 2
  total_chains: 0
findings:
- id: SEC-HEADERS-001
  severity: Critical
  title: Missing Security Headers
  description: 'Missing security headers: Strict-Transport-Security'
  endpoint: /
  method: GET
  cwe: CWE-693
  cvss_score: 10.0
  evidence: 'Headers missing: [''Strict-Transport-Security'']'
  reproduction_steps:
  - 1. Send GET request to application root
  - 2. Check response headers for security headers
  remediation: Add missing security headers to prevent various attacks
  timestamp: '2025-09-12T22:07:50.992974'
  attack_vector: Network
  business_impact: Low
  exploitability: Medium
  correlation_group: info_disclosure_chain
  related_cves:
  - CVE-2021-26855
  attack_chain_position: 0
- id: RATE-LIMIT-002
  severity: Critical
  title: Missing Rate Limiting
  description: No rate limiting detected on /api/auth/login
  endpoint: /api/auth/login
  method: POST
  cwe: CWE-770
  cvss_score: 10.0
  evidence: Sent 20 requests without rate limiting
  reproduction_steps:
  - 1. Send 20 rapid requests to /api/auth/login
  - 2. Observe no rate limiting response (HTTP 429)
  remediation: Implement rate limiting to prevent abuse
  timestamp: '2025-09-12T22:09:01.792662'
  attack_vector: Network
  business_impact: High
  exploitability: Medium
  correlation_group: auth_bypass_chain
  related_cves:
  - CVE-2021-44228
  attack_chain_position: 0
attack_chains: []
