# HexStrike AI - Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying HexStrike AI to production environments. Follow these steps carefully to ensure a secure, scalable, and monitored deployment.

## Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended) or Windows Server
- **Python**: 3.8 or higher
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: Minimum 20GB free space
- **Network**: HTTPS-capable domain with SSL certificate

### Required Services
- **Database**: Supabase (PostgreSQL)
- **Cache**: Redis (optional but recommended)
- **Web Server**: Nginx (for reverse proxy)
- **Process Manager**: PM2 or systemd

## Pre-Deployment Checklist

### 1. Environment Configuration
- [ ] Copy `.env.example` to `.env`
- [ ] Configure all environment variables:
  ```bash
  # Database
  SUPABASE_URL=your_supabase_url
  SUPABASE_KEY=your_supabase_anon_key
  SUPABASE_SERVICE_KEY=your_service_key
  
  # Security
  SECRET_KEY=your_secret_key_here
  JWT_SECRET_KEY=your_jwt_secret_here
  
  # Application
  FLASK_ENV=production
  CHAT_BOX_PORT=8888
  
  # Redis (if using)
  REDIS_URL=redis://localhost:6379
  
  # Monitoring
  APP_URL=https://yourdomain.com
  ```

### 2. Security Hardening
- [ ] Generate strong secret keys
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up firewall rules
- [ ] Enable security headers
- [ ] Configure rate limiting
- [ ] Set up CORS policies

### 3. Database Setup
- [ ] Create Supabase project
- [ ] Run database migrations
- [ ] Set up database backups
- [ ] Configure connection pooling

### 4. Dependencies
- [ ] Install Python dependencies: `pip install -r requirements.txt`
- [ ] Install system dependencies
- [ ] Set up virtual environment

## Deployment Steps

### Step 1: Server Preparation

#### Ubuntu/Linux
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv nginx redis-server -y

# Install Node.js and PM2 (for process management)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y
sudo npm install -g pm2
```

#### Windows Server
```powershell
# Install Python from python.org
# Install Redis from GitHub releases
# Install Node.js and PM2
npm install -g pm2
```

### Step 2: Application Setup

```bash
# Clone repository
git clone <your-repo-url>
cd hexstrike-ai

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Linux
# or
venv\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your production values
```

### Step 3: Database Migration

```bash
# Run database setup
python setup_database.py

# Verify database connection
python -c "from config import Config; print('Database connection successful')"
```

### Step 4: Security Configuration

```bash
# Generate secure keys
python -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(32))"
python -c "import secrets; print('JWT_SECRET_KEY=' + secrets.token_urlsafe(32))"

# Set file permissions
chmod 600 .env
chmod 755 *.py
```

### Step 5: Web Server Configuration

#### Nginx Configuration
Create `/etc/nginx/sites-available/hexstrike-ai`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://127.0.0.1:8888;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Static files (if any)
    location /static {
        alias /path/to/hexstrike-ai/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Health check endpoint
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:8888/health;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/hexstrike-ai /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Step 6: Process Management

#### Using PM2
Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'hexstrike-ai',
    script: 'chat_box_server.py',
    interpreter: 'python3',
    args: '--port 8888',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      FLASK_ENV: 'production',
      PYTHONPATH: '.',
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

Start the application:
```bash
# Create logs directory
mkdir -p logs

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
```

#### Using systemd (Alternative)
Create `/etc/systemd/system/hexstrike-ai.service`:

```ini
[Unit]
Description=HexStrike AI Application
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/hexstrike-ai
Environment=PATH=/path/to/hexstrike-ai/venv/bin
ExecStart=/path/to/hexstrike-ai/venv/bin/python chat_box_server.py --port 8888
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl daemon-reload
sudo systemctl enable hexstrike-ai
sudo systemctl start hexstrike-ai
```

### Step 7: Monitoring Setup

```bash
# Start monitoring service
python monitoring_system.py &

# Or add to PM2
pm2 start monitoring_system.py --name hexstrike-monitoring
```

### Step 8: Logging Configuration

```bash
# Create log directories
sudo mkdir -p /var/log/hexstrike-ai
sudo chown $USER:$USER /var/log/hexstrike-ai

# Configure log rotation
sudo tee /etc/logrotate.d/hexstrike-ai << EOF
/var/log/hexstrike-ai/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reload hexstrike-ai
    endscript
}
EOF
```

## Post-Deployment Verification

### 1. Health Checks
```bash
# Test application health
curl -f https://yourdomain.com/health

# Test API endpoints
curl -f https://yourdomain.com/api/features

# Check SSL certificate
ssl-cert-check -c yourdomain.com
```

### 2. Performance Testing
```bash
# Install testing tools
pip install locust

# Run load test
locust -f load_test.py --host=https://yourdomain.com
```

### 3. Security Verification
```bash
# Run security tests
python test_security.py

# Check for vulnerabilities
pip audit

# Test rate limiting
for i in {1..50}; do curl https://yourdomain.com/api/test; done
```

## Monitoring and Maintenance

### Daily Tasks
- [ ] Check application logs
- [ ] Monitor system resources
- [ ] Verify backup completion
- [ ] Review security alerts

### Weekly Tasks
- [ ] Update dependencies
- [ ] Review performance metrics
- [ ] Test disaster recovery
- [ ] Security scan

### Monthly Tasks
- [ ] Update system packages
- [ ] Review and rotate logs
- [ ] Performance optimization
- [ ] Security audit

## Backup and Recovery

### Database Backup
```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/hexstrike-ai"
mkdir -p $BACKUP_DIR

# Backup database (adjust for your setup)
pg_dump $DATABASE_URL > $BACKUP_DIR/db_backup_$DATE.sql

# Backup application files
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /path/to/hexstrike-ai

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### Recovery Procedure
1. Stop the application
2. Restore database from backup
3. Restore application files
4. Update configuration if needed
5. Start the application
6. Verify functionality

## Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check logs
pm2 logs hexstrike-ai

# Check environment variables
python -c "from config import Config; print(vars(Config))"

# Test database connection
python -c "import psycopg2; print('DB connection OK')"
```

#### High Memory Usage
```bash
# Check memory usage
pm2 monit

# Restart application
pm2 restart hexstrike-ai

# Check for memory leaks
python -m memory_profiler chat_box_server.py
```

#### SSL Certificate Issues
```bash
# Check certificate expiry
openssl x509 -in /path/to/cert.crt -text -noout | grep "Not After"

# Renew Let's Encrypt certificate
certbot renew

# Test SSL configuration
ssl-cert-check -c yourdomain.com
```

### Emergency Contacts
- **System Administrator**: [contact info]
- **Database Administrator**: [contact info]
- **Security Team**: [contact info]

## Performance Optimization

### Application Level
- Enable Redis caching
- Optimize database queries
- Use connection pooling
- Implement request caching

### System Level
- Tune Nginx configuration
- Optimize Python settings
- Configure system limits
- Monitor resource usage

### Database Level
- Index optimization
- Query performance tuning
- Connection pooling
- Regular maintenance

## Security Best Practices

### Application Security
- Regular dependency updates
- Input validation
- Output encoding
- Session management
- Authentication/authorization

### Infrastructure Security
- Firewall configuration
- Regular security updates
- Access control
- Audit logging
- Intrusion detection

### Data Security
- Encryption at rest
- Encryption in transit
- Regular backups
- Access controls
- Data retention policies

## Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Multiple application instances
- Database read replicas
- CDN for static content

### Vertical Scaling
- Increase server resources
- Optimize application performance
- Database tuning
- Caching strategies

---

**Last Updated**: [Current Date]
**Version**: 1.0
**Maintainer**: HexStrike AI Team

For questions or issues, please contact the development team or create an issue in the project repository.