#!/usr/bin/env python3
"""
Safe Penetration Testing Module
Provides non-intrusive security testing: DNS resolution, TCP connectivity, HTTP HEAD requests.
Respects timeouts, rate limits, and security boundaries.
"""

import socket
import time
import logging
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
from dataclasses import dataclass
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PentestResult:
    """Result of a penetration test check"""
    ok: bool
    host: str
    checks: Dict[str, Any]
    elapsed_ms: int
    timestamp: str
    errors: List[str]

class SafePentestEngine:
    """Safe penetration testing engine with security controls"""
    
    def __init__(self):
        self.timeout_seconds = 10
        self.max_task_duration = 20  # Hard cap per task
        self.tcp_ports = [80, 443]  # Only test standard web ports
        
    async def dns_check(self, host: str) -> Dict[str, Any]:
        """Perform DNS resolution check"""
        start_time = time.time()
        result = {
            'ok': False,
            'resolved': False,
            'ip_addresses': [],
            'elapsed_ms': 0,
            'error': None
        }
        
        try:
            # Use asyncio's DNS resolution with timeout
            loop = asyncio.get_event_loop()
            
            # Resolve hostname to IP addresses
            addr_info = await asyncio.wait_for(
                loop.getaddrinfo(host, None, family=socket.AF_INET),
                timeout=self.timeout_seconds
            )
            
            ip_addresses = list(set(info[4][0] for info in addr_info))
            
            result.update({
                'ok': True,
                'resolved': True,
                'ip_addresses': ip_addresses
            })
            
            logger.info(f"DNS check for {host}: resolved to {ip_addresses}")
            
        except asyncio.TimeoutError:
            result['error'] = f'DNS resolution timeout after {self.timeout_seconds}s'
            logger.warning(f"DNS timeout for {host}")
        except socket.gaierror as e:
            result['error'] = f'DNS resolution failed: {str(e)}'
            logger.warning(f"DNS resolution failed for {host}: {e}")
        except Exception as e:
            result['error'] = f'DNS check error: {str(e)}'
            logger.error(f"DNS check error for {host}: {e}")
        
        result['elapsed_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    async def tcp_check(self, host: str, port: int) -> Dict[str, Any]:
        """Perform TCP connectivity check"""
        start_time = time.time()
        result = {
            'ok': False,
            'port': port,
            'connected': False,
            'elapsed_ms': 0,
            'error': None
        }
        
        try:
            # Create connection with timeout
            future = asyncio.open_connection(host, port)
            reader, writer = await asyncio.wait_for(future, timeout=self.timeout_seconds)
            
            # Close connection immediately (non-intrusive)
            writer.close()
            await writer.wait_closed()
            
            result.update({
                'ok': True,
                'connected': True
            })
            
            logger.info(f"TCP check for {host}:{port}: connected successfully")
            
        except asyncio.TimeoutError:
            result['error'] = f'TCP connection timeout after {self.timeout_seconds}s'
            logger.warning(f"TCP timeout for {host}:{port}")
        except ConnectionRefusedError:
            result['error'] = 'Connection refused'
            logger.info(f"TCP connection refused for {host}:{port}")
        except Exception as e:
            result['error'] = f'TCP check error: {str(e)}'
            logger.error(f"TCP check error for {host}:{port}: {e}")
        
        result['elapsed_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    async def http_head_check(self, url: str) -> Dict[str, Any]:
        """Perform HTTP HEAD request check"""
        start_time = time.time()
        result = {
            'ok': False,
            'url': url,
            'status_code': None,
            'headers': {},
            'server': None,
            'content_type': None,
            'elapsed_ms': 0,
            'error': None
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Perform HEAD request (non-intrusive)
                async with session.head(url, allow_redirects=True) as response:
                    result.update({
                        'ok': True,
                        'status_code': response.status,
                        'headers': dict(response.headers),
                        'server': response.headers.get('Server'),
                        'content_type': response.headers.get('Content-Type')
                    })
                    
                    logger.info(f"HTTP HEAD check for {url}: {response.status}")
        
        except asyncio.TimeoutError:
            result['error'] = f'HTTP request timeout after {self.timeout_seconds}s'
            logger.warning(f"HTTP timeout for {url}")
        except aiohttp.ClientError as e:
            result['error'] = f'HTTP client error: {str(e)}'
            logger.warning(f"HTTP client error for {url}: {e}")
        except Exception as e:
            result['error'] = f'HTTP check error: {str(e)}'
            logger.error(f"HTTP check error for {url}: {e}")
        
        result['elapsed_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    async def comprehensive_check(self, target: str) -> PentestResult:
        """Perform comprehensive safe penetration test"""
        start_time = time.time()
        errors = []
        
        # Parse target to get host
        if target.startswith(('http://', 'https://')):
            parsed = urlparse(target)
            host = parsed.hostname
            url = target
        else:
            host = target
            url = f'https://{host}'  # Default to HTTPS
        
        if not host:
            return PentestResult(
                ok=False,
                host=target,
                checks={},
                elapsed_ms=0,
                timestamp=datetime.utcnow().isoformat() + 'Z',
                errors=['Invalid target format']
            )
        
        checks = {}
        
        try:
            # Apply hard timeout to entire operation
            async with asyncio.timeout(self.max_task_duration):
                # 1. DNS Check
                logger.info(f"Starting DNS check for {host}")
                checks['dns'] = await self.dns_check(host)
                if not checks['dns']['ok']:
                    errors.append(f"DNS: {checks['dns']['error']}")
                
                # 2. TCP Checks (only if DNS resolved)
                if checks['dns']['ok']:
                    for port in self.tcp_ports:
                        logger.info(f"Starting TCP check for {host}:{port}")
                        checks[f'tcp{port}'] = await self.tcp_check(host, port)
                        if not checks[f'tcp{port}']['ok']:
                            errors.append(f"TCP{port}: {checks[f'tcp{port}']['error']}")
                
                # 3. HTTP HEAD Check (only if TCP 80 or 443 is open)
                tcp80_ok = checks.get('tcp80', {}).get('ok', False)
                tcp443_ok = checks.get('tcp443', {}).get('ok', False)
                
                if tcp80_ok or tcp443_ok:
                    # Try HTTPS first, then HTTP
                    if tcp443_ok:
                        https_url = f'https://{host}'
                        logger.info(f"Starting HTTPS HEAD check for {https_url}")
                        checks['httpHEAD'] = await self.http_head_check(https_url)
                    
                    # If HTTPS failed and HTTP port is open, try HTTP
                    if (not checks.get('httpHEAD', {}).get('ok', False) and tcp80_ok):
                        http_url = f'http://{host}'
                        logger.info(f"Starting HTTP HEAD check for {http_url}")
                        checks['httpHEAD'] = await self.http_head_check(http_url)
                    
                    if not checks.get('httpHEAD', {}).get('ok', False):
                        errors.append(f"HTTP HEAD: {checks.get('httpHEAD', {}).get('error', 'Failed')}")
                else:
                    checks['httpHEAD'] = {
                        'ok': False,
                        'error': 'No HTTP/HTTPS ports accessible',
                        'elapsed_ms': 0
                    }
                    errors.append('HTTP HEAD: No accessible web ports')
        
        except asyncio.TimeoutError:
            error_msg = f'Comprehensive check timeout after {self.max_task_duration}s'
            errors.append(error_msg)
            logger.warning(f"Comprehensive check timeout for {host}")
        except Exception as e:
            error_msg = f'Comprehensive check error: {str(e)}'
            errors.append(error_msg)
            logger.error(f"Comprehensive check error for {host}: {e}")
        
        elapsed_ms = int((time.time() - start_time) * 1000)
        
        # Determine overall success
        overall_ok = (
            checks.get('dns', {}).get('ok', False) and
            (checks.get('tcp80', {}).get('ok', False) or checks.get('tcp443', {}).get('ok', False))
        )
        
        return PentestResult(
            ok=overall_ok,
            host=host,
            checks=checks,
            elapsed_ms=elapsed_ms,
            timestamp=datetime.utcnow().isoformat() + 'Z',
            errors=errors
        )
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine configuration and status"""
        return {
            'timeout_seconds': self.timeout_seconds,
            'max_task_duration': self.max_task_duration,
            'tcp_ports': self.tcp_ports,
            'checks_available': ['dns', 'tcp80', 'tcp443', 'httpHEAD'],
            'safety_features': [
                'Non-intrusive HEAD requests only',
                'Immediate connection closure',
                'Timeout protection',
                'Hard task duration limits',
                'Standard ports only'
            ]
        }

# Global safe pentest engine
safe_pentest_engine = SafePentestEngine()

async def run_safe_pentest(target: str) -> Dict[str, Any]:
    """Run safe penetration test and return JSON-serializable result"""
    result = await safe_pentest_engine.comprehensive_check(target)
    
    return {
        'ok': result.ok,
        'host': result.host,
        'checks': result.checks,
        'elapsed_ms': result.elapsed_ms,
        'timestamp': result.timestamp,
        'errors': result.errors,
        'summary': {
            'dns_resolved': result.checks.get('dns', {}).get('ok', False),
            'tcp80_open': result.checks.get('tcp80', {}).get('ok', False),
            'tcp443_open': result.checks.get('tcp443', {}).get('ok', False),
            'http_accessible': result.checks.get('httpHEAD', {}).get('ok', False),
            'total_checks': len(result.checks),
            'successful_checks': sum(1 for check in result.checks.values() if check.get('ok', False))
        }
    }

if __name__ == '__main__':
    # Test the safe pentest engine
    async def test_engine():
        print("Safe Penetration Testing Engine Test")
        print("Engine status:")
        import json
        print(json.dumps(safe_pentest_engine.get_engine_status(), indent=2))
        
        # Test targets
        test_targets = [
            'example.com',
            'https://httpbin.org',
            'nonexistent-domain-12345.com'
        ]
        
        for target in test_targets:
            print(f"\nTesting: {target}")
            result = await run_safe_pentest(target)
            print(f"Result: {result['ok']}, Checks: {result['summary']['successful_checks']}/{result['summary']['total_checks']}")
            if result['errors']:
                print(f"Errors: {result['errors']}")
    
    # Run test
    asyncio.run(test_engine())