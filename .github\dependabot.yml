# Dependabot configuration for automated dependency updates
# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  # Python dependencies
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "@security-team"
    assignees:
      - "@maintainers"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
    # Group related updates
    groups:
      security-updates:
        patterns:
          - "cryptography*"
          - "pyjwt*"
          - "requests*"
          - "urllib3*"
          - "certifi*"
        update-types:
          - "security"
      flask-ecosystem:
        patterns:
          - "flask*"
          - "werkzeug*"
          - "jinja2*"
          - "itsdangerous*"
        update-types:
          - "minor"
          - "patch"
      database-drivers:
        patterns:
          - "psycopg*"
          - "sqlalchemy*"
          - "alembic*"
        update-types:
          - "minor"
          - "patch"
      testing-tools:
        patterns:
          - "pytest*"
          - "coverage*"
          - "mock*"
        update-types:
          - "minor"
          - "patch"
    # Security updates get higher priority
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
        update-types: ["security"]
    # Ignore specific packages that require manual updates
    ignore:
      - dependency-name: "numpy"
        versions: ["2.x"]
        reason: "Breaking changes require manual migration"
      - dependency-name: "pandas"
        versions: ["3.x"]
        reason: "Major version requires testing"

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "@security-team"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
    # Group Docker base image updates
    groups:
      base-images:
        patterns:
          - "python*"
          - "alpine*"
          - "ubuntu*"
        update-types:
          - "minor"
          - "patch"

  # GitHub Actions dependencies
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "@devops-team"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
    # Group related GitHub Actions
    groups:
      docker-actions:
        patterns:
          - "docker/*"
        update-types:
          - "minor"
          - "patch"
      security-actions:
        patterns:
          - "github/codeql-action*"
          - "securecodewarrior/github-action-add-sarif*"
        update-types:
          - "minor"
          - "patch"

  # NPM dependencies (if any frontend assets)
  - package-ecosystem: "npm"
    directory: "/static"
    schedule:
      interval: "weekly"
      day: "thursday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "@frontend-team"
    commit-message:
      prefix: "npm"
      prefix-development: "npm-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "javascript"
    # Group frontend dependencies
    groups:
      security-updates:
        patterns:
          - "*"
        update-types:
          - "security"
      dev-dependencies:
        dependency-type: "development"
        update-types:
          - "minor"
          - "patch"
    # Ignore packages that require manual updates
    ignore:
      - dependency-name: "webpack"
        versions: ["6.x"]
        reason: "Major version requires configuration updates"

# Global settings for all ecosystems
# These apply to all package ecosystems unless overridden
registries:
  # Private registry configuration (if needed)
  # python-private:
  #   type: python-index
  #   url: https://private-pypi.company.com
  #   username: ${{secrets.PRIVATE_PYPI_USERNAME}}
  #   password: ${{secrets.PRIVATE_PYPI_PASSWORD}}

# Custom update schedule for critical security patches
# This runs daily to catch security updates quickly
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "daily"
      time: "06:00"
      timezone: "UTC"
    open-pull-requests-limit: 3
    reviewers:
      - "@security-team"
    commit-message:
      prefix: "security"
      include: "scope"
    labels:
      - "security"
      - "critical"
    # Only security updates in daily runs
    allow:
      - dependency-type: "direct"
        update-types: ["security"]
      - dependency-type: "indirect"
        update-types: ["security"]

  # Separate configuration for development dependencies
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "monthly"
      day: "1"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "@maintainers"
    commit-message:
      prefix: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "development"
    # Only development dependencies
    allow:
      - dependency-type: "development"
    # Group all dev dependencies together
    groups:
      dev-tools:
        patterns:
          - "*"
        dependency-type: "development"
        update-types:
          - "minor"
          - "patch"

# Additional configuration for handling updates
# Note: These settings help manage the update process more effectively

# Pull request settings:
# - open-pull-requests-limit: Controls how many PRs Dependabot can have open
# - reviewers: Automatically request reviews from specified users/teams
# - assignees: Automatically assign PRs to specified users/teams
# - labels: Add labels to categorize and filter PRs

# Grouping strategy:
# - Groups related dependencies to reduce PR noise
# - Security updates are prioritized and grouped separately
# - Development dependencies are updated less frequently
# - Major version updates are handled separately from minor/patch

# Schedule strategy:
# - Daily: Critical security updates only
# - Weekly: Regular dependency updates (staggered by day)
# - Monthly: Development dependencies
# - Different days for different ecosystems to spread the load

# Ignore strategy:
# - Major version updates that require manual intervention
# - Packages with known breaking changes
# - Dependencies that need coordinated updates

# Security considerations:
# - Security updates get highest priority (daily schedule)
# - Security team is automatically notified
# - Both direct and indirect security updates are allowed
# - Security updates are labeled for easy identification