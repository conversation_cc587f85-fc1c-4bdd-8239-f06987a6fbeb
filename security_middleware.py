#!/usr/bin/env python3
"""
HexStrike AI - Security Middleware
Implements RBAC, validation, and security controls
"""

import functools
import hashlib
import time
from typing import List, Dict, Any, Optional, Set
from enum import Enum

from flask import request, jsonify, g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt
from pydantic import BaseModel, Field, validator
import structlog

logger = structlog.get_logger(__name__)

# ============================================================================
# ROLE-BASED ACCESS CONTROL
# ============================================================================

class UserRole(Enum):
    """User roles with hierarchical permissions"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class Permission(Enum):
    """System permissions"""
    # Tool execution permissions
    EXECUTE_SAFE_TOOLS = "execute_safe_tools"
    EXECUTE_OFFENSIVE_TOOLS = "execute_offensive_tools"
    EXECUTE_DESTRUCTIVE_TOOLS = "execute_destructive_tools"
    
    # Data access permissions
    READ_REPORTS = "read_reports"
    WRITE_REPORTS = "write_reports"
    DELETE_REPORTS = "delete_reports"
    
    # System administration
    MANAGE_USERS = "manage_users"
    VIEW_AUDIT_LOGS = "view_audit_logs"
    SYSTEM_CONFIG = "system_config"

# Role-Permission mapping
ROLE_PERMISSIONS = {
    UserRole.ADMIN: {
        Permission.EXECUTE_SAFE_TOOLS,
        Permission.EXECUTE_OFFENSIVE_TOOLS,
        Permission.EXECUTE_DESTRUCTIVE_TOOLS,
        Permission.READ_REPORTS,
        Permission.WRITE_REPORTS,
        Permission.DELETE_REPORTS,
        Permission.MANAGE_USERS,
        Permission.VIEW_AUDIT_LOGS,
        Permission.SYSTEM_CONFIG
    },
    UserRole.USER: {
        Permission.EXECUTE_SAFE_TOOLS,
        Permission.EXECUTE_OFFENSIVE_TOOLS,
        Permission.READ_REPORTS,
        Permission.WRITE_REPORTS
    },
    UserRole.GUEST: {
        Permission.EXECUTE_SAFE_TOOLS,
        Permission.READ_REPORTS
    }
}

# Tool allowlists by role
SAFE_TOOLS = {
    "nmap_basic", "whois", "dig", "ping", "traceroute", "curl", "wget",
    "ssl_check", "port_scan_basic", "dns_enum", "subdomain_enum"
}

OFFENSIVE_TOOLS = {
    "nmap_aggressive", "nikto", "dirb", "gobuster", "sqlmap", "burp_scan",
    "xss_hunter", "csrf_check", "lfi_scan", "rfi_scan", "xxe_scan"
}

DESTRUCTIVE_TOOLS = {
    "metasploit", "exploit_db", "payload_gen", "reverse_shell", "bind_shell",
    "privilege_escalation", "lateral_movement", "persistence"
}

TOOL_ALLOWLISTS = {
    UserRole.ADMIN: SAFE_TOOLS | OFFENSIVE_TOOLS | DESTRUCTIVE_TOOLS,
    UserRole.USER: SAFE_TOOLS | OFFENSIVE_TOOLS,
    UserRole.GUEST: SAFE_TOOLS
}

# ============================================================================
# PYDANTIC SCHEMAS FOR MCP PAYLOAD VALIDATION
# ============================================================================

class MCPToolRequest(BaseModel):
    """Schema for MCP tool execution requests"""
    tool_name: str = Field(..., min_length=1, max_length=100)
    parameters: Dict[str, Any] = Field(default_factory=dict)
    timeout: Optional[int] = Field(default=120, ge=1, le=300)
    priority: Optional[str] = Field(default="normal", regex="^(low|normal|high|critical)$")
    
    @validator('tool_name')
    def validate_tool_name(cls, v):
        # Only allow alphanumeric, underscore, and hyphen
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Tool name contains invalid characters')
        return v.lower()
    
    @validator('parameters')
    def validate_parameters(cls, v):
        # Limit parameter complexity
        if len(str(v)) > 10000:  # 10KB limit
            raise ValueError('Parameters too large')
        return v

class MCPChatRequest(BaseModel):
    """Schema for MCP chat requests"""
    message: str = Field(..., min_length=1, max_length=10000)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    session_id: Optional[str] = Field(default=None, max_length=100)
    
    @validator('message')
    def validate_message(cls, v):
        # Basic content filtering
        forbidden_patterns = ['<script', 'javascript:', 'data:text/html']
        v_lower = v.lower()
        for pattern in forbidden_patterns:
            if pattern in v_lower:
                raise ValueError(f'Message contains forbidden pattern: {pattern}')
        return v

class MCPJobRequest(BaseModel):
    """Schema for background job requests"""
    job_type: str = Field(..., regex="^(scan|analysis|report|export)$")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    priority: str = Field(default="normal", regex="^(low|normal|high)$")
    callback_url: Optional[str] = Field(default=None, max_length=500)
    
    @validator('callback_url')
    def validate_callback_url(cls, v):
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Invalid callback URL scheme')
        return v

# ============================================================================
# SECURITY DECORATORS
# ============================================================================

def require_roles(*allowed_roles: UserRole):
    """
    Decorator to enforce role-based access control
    
    Args:
        *allowed_roles: Roles that are allowed to access the endpoint
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Verify JWT token
                verify_jwt_in_request()
                
                # Get user identity and role
                current_user = get_jwt_identity()
                claims = get_jwt()
                user_role_str = claims.get('role', 'guest')
                
                try:
                    user_role = UserRole(user_role_str)
                except ValueError:
                    logger.warning(f"Invalid role in JWT: {user_role_str}", user=current_user)
                    return jsonify({'error': 'Invalid user role'}), 403
                
                # Check if user role is allowed
                if user_role not in allowed_roles:
                    logger.warning(
                        f"Access denied for role {user_role.value}",
                        user=current_user,
                        endpoint=request.endpoint,
                        allowed_roles=[r.value for r in allowed_roles]
                    )
                    return jsonify({
                        'error': 'Insufficient permissions',
                        'required_roles': [r.value for r in allowed_roles],
                        'user_role': user_role.value
                    }), 403
                
                # Add user context to request
                g.current_user = current_user
                g.user_role = user_role
                
                logger.info(
                    f"Access granted",
                    user=current_user,
                    role=user_role.value,
                    endpoint=request.endpoint
                )
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Authorization error: {str(e)}")
                return jsonify({'error': 'Authorization failed'}), 401
                
        return decorated_function
    return decorator

def require_permissions(*required_permissions: Permission):
    """
    Decorator to enforce permission-based access control
    
    Args:
        *required_permissions: Permissions required to access the endpoint
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Ensure we have user context (should be set by require_roles)
                if not hasattr(g, 'user_role'):
                    return jsonify({'error': 'User context not found'}), 401
                
                user_role = g.user_role
                user_permissions = ROLE_PERMISSIONS.get(user_role, set())
                
                # Check if user has all required permissions
                missing_permissions = set(required_permissions) - user_permissions
                if missing_permissions:
                    logger.warning(
                        f"Missing permissions",
                        user=g.current_user,
                        role=user_role.value,
                        missing=[p.value for p in missing_permissions]
                    )
                    return jsonify({
                        'error': 'Insufficient permissions',
                        'missing_permissions': [p.value for p in missing_permissions]
                    }), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Permission check error: {str(e)}")
                return jsonify({'error': 'Permission check failed'}), 500
                
        return decorated_function
    return decorator

def validate_tool_access(f):
    """
    Decorator to validate tool access based on user role and tool allowlist
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Get tool name from request
            data = request.get_json() or {}
            tool_name = data.get('tool_name', '').lower()
            
            if not tool_name:
                return jsonify({'error': 'Tool name required'}), 400
            
            # Check tool allowlist
            user_role = g.user_role
            allowed_tools = TOOL_ALLOWLISTS.get(user_role, set())
            
            if tool_name not in allowed_tools:
                logger.warning(
                    f"Tool access denied",
                    user=g.current_user,
                    role=user_role.value,
                    tool=tool_name
                )
                return jsonify({
                    'error': 'Tool not allowed for your role',
                    'tool': tool_name,
                    'role': user_role.value
                }), 403
            
            # Add tool validation to request context
            g.validated_tool = tool_name
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Tool validation error: {str(e)}")
            return jsonify({'error': 'Tool validation failed'}), 500
            
    return decorated_function

def idempotent():
    """
    Decorator to ensure idempotent operations using request fingerprinting
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Create request fingerprint
                request_data = {
                    'method': request.method,
                    'path': request.path,
                    'args': dict(request.args),
                    'json': request.get_json() if request.is_json else None,
                    'user': getattr(g, 'current_user', 'anonymous')
                }
                
                # Generate fingerprint hash
                fingerprint = hashlib.sha256(
                    str(sorted(request_data.items())).encode()
                ).hexdigest()
                
                # Check for duplicate requests (simple in-memory cache)
                # In production, use Redis with TTL
                cache_key = f"idempotent:{fingerprint}"
                
                # For now, just log the fingerprint
                logger.info(
                    f"Idempotent request",
                    fingerprint=fingerprint,
                    user=getattr(g, 'current_user', 'anonymous')
                )
                
                g.request_fingerprint = fingerprint
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Idempotency check error: {str(e)}")
                return f(*args, **kwargs)  # Continue on error
                
        return decorated_function
    return decorator

def validate_mcp_payload(schema_class):
    """
    Decorator to validate MCP payloads using Pydantic schemas
    
    Args:
        schema_class: Pydantic model class for validation
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get request data
                if not request.is_json:
                    return jsonify({'error': 'JSON payload required'}), 400
                
                data = request.get_json()
                
                # Validate using Pydantic schema
                try:
                    validated_data = schema_class(**data)
                    g.validated_payload = validated_data
                    
                    logger.info(
                        f"Payload validated",
                        schema=schema_class.__name__,
                        user=getattr(g, 'current_user', 'anonymous')
                    )
                    
                except Exception as validation_error:
                    logger.warning(
                        f"Payload validation failed",
                        schema=schema_class.__name__,
                        error=str(validation_error),
                        user=getattr(g, 'current_user', 'anonymous')
                    )
                    return jsonify({
                        'error': 'Invalid payload',
                        'details': str(validation_error)
                    }), 400
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Payload validation error: {str(e)}")
                return jsonify({'error': 'Validation failed'}), 500
                
        return decorated_function
    return decorator

# ============================================================================
# CONSENT AND AUTHORIZATION UTILITIES
# ============================================================================

def require_consent_for_offensive_tools(f):
    """
    Decorator to require explicit consent for offensive tool usage
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            tool_name = getattr(g, 'validated_tool', '')
            
            # Check if tool requires consent
            if tool_name in OFFENSIVE_TOOLS or tool_name in DESTRUCTIVE_TOOLS:
                data = request.get_json() or {}
                consent_token = data.get('consent_token')
                
                if not consent_token:
                    return jsonify({
                        'error': 'Consent required for offensive tools',
                        'tool': tool_name,
                        'consent_required': True
                    }), 403
                
                # Validate consent token (implement proper validation)
                # For now, just check if it's present
                logger.info(
                    f"Consent provided for offensive tool",
                    tool=tool_name,
                    user=g.current_user,
                    consent_token=consent_token[:8] + "..."
                )
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Consent check error: {str(e)}")
            return jsonify({'error': 'Consent validation failed'}), 500
            
    return decorated_function

# ============================================================================
# AUDIT LOGGING
# ============================================================================

def audit_log(action: str, **kwargs):
    """
    Log security-relevant actions for audit trail
    
    Args:
        action: Action being performed
        **kwargs: Additional context data
    """
    audit_data = {
        'action': action,
        'timestamp': time.time(),
        'user': getattr(g, 'current_user', 'anonymous'),
        'role': getattr(g, 'user_role', UserRole.GUEST).value if hasattr(g, 'user_role') else 'guest',
        'ip_address': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', 'Unknown'),
        'correlation_id': getattr(g, 'correlation_id', 'unknown'),
        **kwargs
    }
    
    logger.info("Audit log", **audit_data)
    
    # In production, also store in database audit_logs table
    # db.session.add(AuditLog(**audit_data))
    # db.session.commit()