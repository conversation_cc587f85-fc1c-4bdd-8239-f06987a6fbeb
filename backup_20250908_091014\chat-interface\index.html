<!DOCTYPE html>
<html lang="en" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Box AI Chat Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Tailwind Config */
        :root {
            --color-primary-50: #fef2f2;
            --color-primary-100: #fee2e2;
            --color-primary-200: #fecaca;
            --color-primary-300: #fca5a5;
            --color-primary-400: #f87171;
            --color-primary-500: #ef4444;
            --color-primary-600: #dc2626;
            --color-primary-700: #b91c1c;
            --color-primary-800: #991b1b;
            --color-primary-900: #7f1d1d;
        }
    <style>
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }
        .dark ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        .dark ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
        }
        .dark ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideIn {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        .animate-slide-in {
            animation: slideIn 0.3s ease-in-out;
        }
        .animate-pulse {
            animation: pulse 1.5s infinite;
        }
        
        /* Enhanced Loading Animations */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0, -8px, 0); }
            70% { transform: translate3d(0, -4px, 0); }
            90% { transform: translate3d(0, -2px, 0); }
        }
        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }
        
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        .animate-bounce {
            animation: bounce 1s infinite;
        }
        .animate-shimmer {
            animation: shimmer 2s infinite;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200px 100%;
        }
        
        /* Enhanced Function Card Animations */
        .function-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .function-card:hover::before {
            left: 100%;
        }
        .function-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .function-card:active {
            transform: translateY(-2px) scale(0.98);
        }
        
        /* Loading Spinner Component */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        /* Loading Overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            text-align: center;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .dark .loading-content {
            background: #1f2937;
            color: white;
        }

        /* Resizable panels */
        .gutter {
            background-color: #edf2f7;
            background-repeat: no-repeat;
            background-position: 50%;
        }
        .dark .gutter {
            background-color: #2d3748;
        }
        .gutter.gutter-horizontal {
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==');
            cursor: col-resize;
        }
        .typing-indicator span {
            animation: blink 1.4s infinite;
            animation-fill-mode: both;
            height: 5px;
            width: 5px;
            background-color: #b71c1c;
            display: inline-block;
            border-radius: 50%;
            margin: 0 1px;
        }
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes blink {
            0% { opacity: 0.1; }
            20% { opacity: 1; }
            100% { opacity: 0.1; }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
    <div class="flex h-screen">
        <!-- Function Selection Modal (initially visible) -->
        <div id="functionSelectionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-2xl animate-slide-in">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-700 dark:text-primary-400">Choose Your AI Assistant</h2>
                    <div>
                        <button id="functionThemeToggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <i class="fas fa-moon dark:hidden"></i>
                            <i class="fas fa-sun hidden dark:block"></i>
                        </button>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-6 text-center">Select the type of assistance you need to get started with your specialized AI chat experience.</p>
                
                <!-- Function Selection Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <!-- Security Scan Function -->
                    <div class="function-card bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-2 border-red-200 dark:border-red-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="security-scan">
                        <div class="text-center">
                            <div class="bg-red-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-shield-alt text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-red-700 dark:text-red-300 mb-2">Security Scan</h3>
                            <p class="text-sm text-red-600 dark:text-red-400">Vulnerability assessment, penetration testing, and security analysis</p>
                        </div>
                    </div>
                    
                    <!-- Code Review Function -->
                    <div class="function-card bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-2 border-blue-200 dark:border-blue-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="code-review">
                        <div class="text-center">
                            <div class="bg-blue-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-code text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-blue-700 dark:text-blue-300 mb-2">Code Review</h3>
                            <p class="text-sm text-blue-600 dark:text-blue-400">Code analysis, best practices, and security code review</p>
                        </div>
                    </div>
                    
                    <!-- Threat Analysis Function -->
                    <div class="function-card bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-2 border-purple-200 dark:border-purple-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="threat-analysis">
                        <div class="text-center">
                            <div class="bg-purple-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-search text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-purple-700 dark:text-purple-300 mb-2">Threat Analysis</h3>
                            <p class="text-sm text-purple-600 dark:text-purple-400">Threat intelligence, risk assessment, and incident analysis</p>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Functions Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <!-- Network Analysis Function -->
                    <div class="function-card bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-2 border-green-200 dark:border-green-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="network-analysis">
                        <div class="text-center">
                            <div class="bg-green-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-network-wired text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-green-700 dark:text-green-300 mb-2">Network Analysis</h3>
                            <p class="text-sm text-green-600 dark:text-green-400">Network security, traffic analysis, and infrastructure assessment</p>
                        </div>
                    </div>
                    
                    <!-- General Security Function -->
                    <div class="function-card bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-2 border-orange-200 dark:border-orange-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="general-security">
                        <div class="text-center">
                            <div class="bg-orange-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-lock text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-orange-700 dark:text-orange-300 mb-2">General Security</h3>
                            <p class="text-sm text-orange-600 dark:text-orange-400">General cybersecurity questions and consultation</p>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Functions Row 2 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <!-- Report Generator Function -->
                    <div class="function-card bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 border-2 border-indigo-200 dark:border-indigo-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="report-generator">
                        <div class="text-center">
                            <div class="bg-indigo-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-file-alt text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-indigo-700 dark:text-indigo-300 mb-2">Report Generator</h3>
                            <p class="text-sm text-indigo-600 dark:text-indigo-400">Generate structured reports in PDF, Markdown, or HTML format</p>
                        </div>
                    </div>
                    
                    <!-- Data Import Function -->
                    <div class="function-card bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20 border-2 border-teal-200 dark:border-teal-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="data-import">
                        <div class="text-center">
                            <div class="bg-teal-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-upload text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-teal-700 dark:text-teal-300 mb-2">Data Import</h3>
                            <p class="text-sm text-teal-600 dark:text-teal-400">Upload and analyze code files, logs, and configuration files</p>
                        </div>
                    </div>
                    
                    <!-- Alerts Function -->
                    <div class="function-card bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-2 border-yellow-200 dark:border-yellow-700 rounded-lg p-4 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105" data-function="alerts">
                        <div class="text-center">
                            <div class="bg-yellow-500 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-bell text-xl"></i>
                            </div>
                            <h3 class="font-bold text-lg text-yellow-700 dark:text-yellow-300 mb-2">Alerts</h3>
                            <p class="text-sm text-yellow-600 dark:text-yellow-400">Monitor and receive notifications for critical security findings</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center text-sm text-gray-500 dark:text-gray-400">
                    Click on any function above to start your specialized AI chat session
                </div>
            </div>
        </div>

        <!-- Sidebar (Chat) -->
        <div id="chatPanel" class="w-1/2 flex flex-col border-r border-gray-200 dark:border-gray-700 transition-all duration-300">
            <!-- Header -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800">
                <div class="flex items-center">
                    <img src="/assets/chat-box-logo.png" alt="Chat Box AI Logo" class="h-8 w-8 mr-2">
                <h1 class="text-lg font-bold">Chat Box AI Chat</h1>
                    <div id="securityIndicator" class="ml-2 w-2 h-2 rounded-full bg-yellow-500" title="Standard Security"></div>
                </div>
                <div class="flex space-x-2">
                    <button id="newChatBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" title="New Chat">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" title="Toggle Theme">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                    <div class="relative">
                        <button id="userMenuBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" title="User Menu">
                            <i class="fas fa-user"></i>
                        </button>
                        <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 hidden z-10 animate-fade-in">
                            <!-- User Info -->
                            <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                                <div class="text-sm font-medium" id="usernameDisplay">User</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400" id="roleDisplay">Role</div>
                            </div>
                            
                            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>
                            
                            <!-- Admin Only: User Management -->
                            <a href="#" class="admin-only block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 hidden">
                                <i class="fas fa-users-cog mr-2"></i> User Management
                            </a>
                            
                            <!-- Admin Only: System Settings -->
                            <a href="#" class="admin-only block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 hidden">
                                <i class="fas fa-cogs mr-2"></i> System Settings
                            </a>
                            
                            <!-- Registered Only: Account Upgrade -->
                            <a href="#" class="free-only block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 hidden">
                                <i class="fas fa-arrow-circle-up mr-2"></i> Upgrade Account
                            </a>
                            
                            <!-- Role Selector (Admin Only) -->
                            <div class="admin-only px-4 py-2 border-t border-gray-200 dark:border-gray-700 hidden">
                                <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">Switch Role</label>
                                <select id="roleSelector" class="w-full text-sm bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded px-2 py-1">
                                    <option value="admin">Admin</option>
                                    <option value="registered">Registered User</option>
                                    <option value="free">Free User</option>
                                </select>
                            </div>
                            
                            <a href="#" id="logoutBtn" class="block px-4 py-2 text-sm text-red-500 hover:bg-gray-100 dark:hover:bg-gray-700 border-t border-gray-200 dark:border-gray-700">Logout</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div class="flex-1 flex overflow-x-auto" id="chatTabsContainer">
                    <button id="securityScanTab" class="chat-tab-btn px-4 py-2 border-b-2 border-primary-500 text-primary-600 dark:text-primary-400 font-medium whitespace-nowrap active">Security Scan</button>
                    <button id="codeReviewTab" class="chat-tab-btn px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 whitespace-nowrap">Code Review</button>
                    <button id="threatAnalysisTab" class="chat-tab-btn px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 whitespace-nowrap">Threat Analysis</button>
                </div>
                <button id="newTabBtn" class="px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 whitespace-nowrap">+ New Tab</button>
            </div>
            
            <!-- Environment Selector -->
            <div class="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500 dark:text-gray-400">Environment:</span>
                    <select id="environmentSelector" class="text-xs bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded px-2 py-1">
                        <option value="production">Production</option>
                        <option value="staging">Staging</option>
                        <option value="development">Development</option>
                        <option value="testing">Testing</option>
                    </select>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500 dark:text-gray-400">API Version:</span>
                    <select id="apiVersionSelector" class="text-xs bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded px-2 py-1">
                        <option value="v2">v2 (Latest)</option>
                        <option value="v1">v1 (Legacy)</option>
                    </select>
                </div>
            </div>

            <!-- Chat Content Container -->
            <div id="chatContentContainer" class="flex-1 overflow-hidden bg-white dark:bg-gray-800">
                <!-- Security Scan Tab Content -->
                <div id="securityScanContent" class="chat-tab-content flex-1 overflow-y-auto p-4 space-y-4 h-full">
                    <!-- Chat Messages -->
                <!-- AI Message -->
                <div class="flex items-start space-x-3 animate-fade-in">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
                        <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                            <p class="text-sm">Hello! I'm Chat Box AI, your cybersecurity assistant. How can I help you today?</p>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <span>10:30 AM</span>
                            <div class="flex space-x-2 ml-2">
                                <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Copy to clipboard">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Save response">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Process Controls (initially hidden) -->
                <div id="processControls" class="flex justify-center space-x-2 py-2 hidden">
                    <button id="pauseBtn" class="bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md text-sm flex items-center">
                        <i class="fas fa-pause mr-1"></i> Pause
                    </button>
                    <button id="stopBtn" class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-sm flex items-center">
                        <i class="fas fa-stop mr-1"></i> Stop
                    </button>
                    <button id="changeBtn" class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-md text-sm flex items-center">
                        <i class="fas fa-exchange-alt mr-1"></i> Change Request
                    </button>
                </div>
                
                <!-- Process Status (initially hidden) -->
                <div id="processStatus" class="flex justify-center items-center py-2 text-sm text-gray-600 dark:text-gray-400 hidden">
                    <div class="animate-spin mr-2">
                        <i class="fas fa-circle-notch"></i>
                    </div>
                    <span>Processing request...</span>
                </div>
                
                <!-- Error Message (initially hidden) -->
                <div id="errorMessage" class="bg-red-100 dark:bg-red-900 border-l-4 border-red-500 text-red-700 dark:text-red-300 p-4 hidden">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium">Error processing your request</p>
                            <p class="text-sm mt-1" id="errorDetails">Details about the error will appear here.</p>
                        </div>
                    </div>
                </div>
                </div>
                
                <!-- Code Review Tab Content -->
                <div id="codeReviewContent" class="chat-tab-content flex-1 overflow-y-auto p-4 space-y-4 h-full hidden">
                    <!-- AI Message -->
                    <div class="flex items-start space-x-3 animate-fade-in">
                        <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
                            <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
                        </div>
                        <div class="flex-1">
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                                <p class="text-sm">Welcome to Code Review! Upload your code or provide a repository URL to begin a security-focused code review.</p>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                <span>10:30 AM</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Threat Analysis Tab Content -->
                <div id="threatAnalysisContent" class="chat-tab-content flex-1 overflow-y-auto p-4 space-y-4 h-full hidden">
                    <!-- AI Message -->
                    <div class="flex items-start space-x-3 animate-fade-in">
                        <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
                            <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
                        </div>
                        <div class="flex-1">
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                                <p class="text-sm">Welcome to Threat Analysis! Describe a security scenario or provide IOCs to analyze potential threats.</p>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                <span>10:30 AM</span>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                </div>

                <!-- User Message -->
                <div class="flex items-start space-x-3 animate-fade-in">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-2">
                        <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-blue-100 dark:bg-blue-900 rounded-lg p-3">
                            <p class="text-sm">I need to scan a website for vulnerabilities. Can you help me set up a comprehensive scan?</p>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            <span>10:31 AM</span>
                        </div>
                    </div>
                </div>

                <!-- AI Message with Typing Indicator -->
                <div class="flex items-start space-x-3 animate-fade-in">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
                        <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                            <p class="text-sm">I can definitely help you set up a comprehensive vulnerability scan. Let me guide you through the process:</p>
                            <ol class="list-decimal ml-5 mt-2 text-sm space-y-1">
                                <li>First, we'll need to identify the target website and scope.</li>
                                <li>Then, we'll select the appropriate scanning tools based on your requirements.</li>
                                <li>We'll configure the scan parameters for thoroughness and to avoid false positives.</li>
                                <li>Finally, we'll execute the scan and analyze the results.</li>
                            </ol>
                            <p class="text-sm mt-2">Would you like to proceed with this approach? Or do you have specific requirements for the scan?</p>
                            <div class="typing-indicator mt-2">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <span>10:32 AM</span>
                            <div class="flex space-x-2 ml-2">
                                <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Copy to clipboard">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Save response">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Process Controls -->
            <div class="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div class="flex space-x-2 mb-2">
                    <button id="pauseBtn" class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md text-sm transition-colors">
                        <i class="fas fa-pause mr-1"></i> Pause
                    </button>
                    <button id="stopBtn" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-sm transition-colors">
                        <i class="fas fa-stop mr-1"></i> Stop
                    </button>
                    <button id="changeBtn" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-md text-sm transition-colors">
                        <i class="fas fa-exchange-alt mr-1"></i> Change
                    </button>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="relative flex-1">
                        <input type="text" id="messageInput" class="w-full pl-3 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" placeholder="Type your message...">
                        <button id="uploadBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <i class="fas fa-paperclip"></i>
                        </button>
                    </div>
                    <button id="sendBtn" class="bg-primary-600 hover:bg-primary-700 text-white p-2 rounded-md transition-colors">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>

            <!-- Additional Parameters (Collapsible) -->
            <div id="paramsSection" class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 overflow-hidden transition-all duration-300" style="max-height: 0;">
                <div class="p-4 space-y-3">
                    <h3 class="font-medium text-sm">Additional Parameters</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label for="targetUrl" class="block text-xs font-medium mb-1">Target URL</label>
                            <input type="text" id="targetUrl" class="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700">
                        </div>
                        <div>
                            <label for="scanType" class="block text-xs font-medium mb-1">Scan Type</label>
                            <select id="scanType" class="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700">
                                <option>Quick Scan</option>
                                <option>Full Scan</option>
                                <option>Custom</option>
                            </select>
                        </div>
                        <div>
                            <label for="apiKey" class="block text-xs font-medium mb-1">API Key</label>
                            <input type="password" id="apiKey" class="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700">
                        </div>
                        <div>
                            <label for="timeout" class="block text-xs font-medium mb-1">Timeout (seconds)</label>
                            <input type="number" id="timeout" class="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" value="30">
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button id="saveParamsBtn" class="bg-primary-600 hover:bg-primary-700 text-white py-1 px-3 rounded-md text-sm transition-colors">
                            Save Parameters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Toggle for Additional Parameters -->
            <div class="p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-center">
                <button id="toggleParamsBtn" class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                    <i class="fas fa-chevron-down" id="paramsIcon"></i> <span id="paramsText">Show Additional Parameters</span>
                </button>
            </div>
        </div>

        <!-- Main Content (Logs & Process) -->
        <div id="logsPanel" class="w-1/2 flex flex-col">
            <!-- Header -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800">
                <h2 class="text-lg font-bold">Process Viewer</h2>
                <div class="flex space-x-2">
                    <button id="exportLogsBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" title="Export Logs">
                        <i class="fas fa-download"></i>
                    </button>
                    <button id="clearLogsBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" title="Clear Logs">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="relative">
                        <button id="viewOptionsBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700" title="View Options">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div id="viewOptionsMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 hidden z-10 animate-fade-in">
                            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">Show Timestamps</a>
                            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">Filter by Level</a>
                            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700">Word Wrap</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <button id="logsTabBtn" class="px-4 py-2 border-b-2 border-primary-500 text-primary-600 dark:text-primary-400 font-medium">Logs</button>
                <button id="todoTabBtn" class="px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">To-Do</button>
                <button id="filesTabBtn" class="px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">Files</button>
                <button id="metricsTabBtn" class="px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">Metrics</button>
                <button id="securityTabBtn" class="px-4 py-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">Security</button>
            </div>
            
            <!-- Tab Content -->
            <div id="tabContent" class="flex-1 flex flex-col">
                <!-- Logs Tab (Default Visible) -->
                <div id="logsTabContent" class="flex-1 flex flex-col">

            <!-- Process Status -->
            <div class="p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="animate-pulse h-3 w-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-sm font-medium">Process Running: Vulnerability Scan</span>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        <span>Started: 10:31 AM</span>
                        <span class="mx-2">|</span>
                        <span>Duration: 00:01:42</span>
                    </div>
                </div>
                <div class="mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 45%"></div>
                </div>
            </div>

            <!-- Logs Output -->
            <div id="logsOutput" class="flex-1 overflow-y-auto p-4 font-mono text-sm bg-gray-100 dark:bg-gray-900">
                <div class="space-y-1">
                    <div class="text-gray-500 dark:text-gray-400">[10:31:05] Starting vulnerability scan process...</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:06] INFO: Initializing scanner with target: example.com</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:07] INFO: Loading scan configuration</div>
                    <div class="text-green-600 dark:text-green-400">[10:31:08] SUCCESS: Configuration loaded successfully</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:10] INFO: Starting port scan (TCP)</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:15] INFO: Discovered open ports: 80, 443, 22</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:20] INFO: Starting service detection</div>
                    <div class="text-green-600 dark:text-green-400">[10:31:25] SUCCESS: Service detection complete</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:30] INFO: Starting web application scan</div>
                    <div class="text-yellow-600 dark:text-yellow-400">[10:31:40] WARNING: Rate limiting detected, slowing down requests</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:45] INFO: Scanning for common vulnerabilities</div>
                    <div class="text-red-600 dark:text-red-400">[10:31:50] ERROR: Connection timeout on endpoint /admin</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:31:55] INFO: Retrying connection...</div>
                    <div class="text-green-600 dark:text-green-400">[10:32:00] SUCCESS: Connection re-established</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:32:05] INFO: Testing for XSS vulnerabilities</div>
                    <div class="text-yellow-600 dark:text-yellow-400">[10:32:10] WARNING: Potential XSS vulnerability found in search parameter</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:32:15] INFO: Testing for SQL injection</div>
                    <div class="text-red-600 dark:text-red-400">[10:32:20] CRITICAL: SQL injection vulnerability detected in login form</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:32:25] INFO: Testing for CSRF vulnerabilities</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:32:30] INFO: Scanning authentication mechanisms</div>
                    <div class="text-yellow-600 dark:text-yellow-400">[10:32:35] WARNING: Weak password policy detected</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:32:40] INFO: Checking for sensitive data exposure</div>
                    <div class="text-yellow-600 dark:text-yellow-400">[10:32:45] WARNING: Potential information disclosure in error messages</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:32:50] INFO: Scanning for outdated components</div>
                    <div class="text-red-600 dark:text-red-400">[10:32:55] ERROR: Failed to determine version of component X</div>
                    <div class="text-blue-600 dark:text-blue-400">[10:33:00] INFO: Continuing with remaining checks...</div>
                </div>
            </div>

                </div>
                </div>
                
                <!-- Todo Tab (Hidden by default) -->
                <div id="todoTabContent" class="hidden flex-1 flex flex-col">
                    <div class="flex-1 overflow-y-auto p-4 bg-gray-100 dark:bg-gray-900">
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-white dark:bg-gray-800 rounded-md shadow-sm">
                                <input type="checkbox" class="mr-3 h-4 w-4 text-primary-600">
                                <div class="flex-1">
                                    <p class="text-sm font-medium">Scan target domain for vulnerabilities</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">High Priority</p>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">10:30 AM</span>
                            </div>
                            <div class="flex items-center p-3 bg-white dark:bg-gray-800 rounded-md shadow-sm">
                                <input type="checkbox" class="mr-3 h-4 w-4 text-primary-600">
                                <div class="flex-1">
                                    <p class="text-sm font-medium">Analyze scan results and identify critical issues</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Medium Priority</p>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">10:45 AM</span>
                            </div>
                            <div class="flex items-center p-3 bg-white dark:bg-gray-800 rounded-md shadow-sm">
                                <input type="checkbox" class="mr-3 h-4 w-4 text-primary-600" checked>
                                <div class="flex-1">
                                    <p class="text-sm font-medium line-through">Initialize security scanner</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Completed</p>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">10:15 AM</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Files Tab (Hidden by default) -->
                <div id="filesTabContent" class="hidden flex-1 flex flex-col">
                    <div class="flex-1 overflow-y-auto p-4 bg-gray-100 dark:bg-gray-900">
                        <div class="space-y-2">
                            <div class="flex items-center p-2 bg-white dark:bg-gray-800 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                                <i class="fas fa-file-alt text-blue-500 mr-3"></i>
                                <div class="flex-1">
                                    <p class="text-sm font-medium">scan_results.json</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">23KB - 10:35 AM</p>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="flex items-center p-2 bg-white dark:bg-gray-800 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                                <i class="fas fa-file-code text-green-500 mr-3"></i>
                                <div class="flex-1">
                                    <p class="text-sm font-medium">vulnerability_report.html</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">45KB - 10:40 AM</p>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="flex items-center p-2 bg-white dark:bg-gray-800 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                                <i class="fas fa-file-csv text-yellow-500 mr-3"></i>
                                <div class="flex-1">
                                    <p class="text-sm font-medium">port_scan_data.csv</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">12KB - 10:20 AM</p>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Security Tab (Hidden by default) -->
                <div id="securityTabContent" class="hidden flex-1 flex flex-col">
                    <div class="flex-1 overflow-y-auto p-4 bg-gray-100 dark:bg-gray-900">
                        <div id="securitySettings" class="space-y-4">
                            <!-- Security settings will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Metrics Tab (Hidden by default) -->
                <div id="metricsTabContent" class="hidden flex-1 flex flex-col">
                    <div class="flex-1 overflow-y-auto p-4 bg-gray-100 dark:bg-gray-900">
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                                <h3 class="text-sm font-medium mb-2">Vulnerabilities by Severity</h3>
                                <div class="h-40 flex items-end space-x-2">
                                    <div class="flex-1 flex flex-col items-center">
                                        <div class="w-full bg-red-500 rounded-t-sm" style="height: 60%"></div>
                                        <span class="text-xs mt-1">Critical</span>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div class="w-full bg-orange-500 rounded-t-sm" style="height: 40%"></div>
                                        <span class="text-xs mt-1">High</span>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div class="w-full bg-yellow-500 rounded-t-sm" style="height: 80%"></div>
                                        <span class="text-xs mt-1">Medium</span>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div class="w-full bg-blue-500 rounded-t-sm" style="height: 30%"></div>
                                        <span class="text-xs mt-1">Low</span>
                                    </div>
                                    <div class="flex-1 flex flex-col items-center">
                                        <div class="w-full bg-green-500 rounded-t-sm" style="height: 20%"></div>
                                        <span class="text-xs mt-1">Info</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                                <h3 class="text-sm font-medium mb-2">Scan Performance</h3>
                                <div class="h-40 relative">
                                    <!-- Simple line chart simulation -->
                                    <div class="absolute bottom-0 left-0 right-0 h-px bg-gray-300 dark:bg-gray-600"></div>
                                    <div class="absolute bottom-1/4 left-0 right-0 h-px bg-gray-200 dark:bg-gray-700"></div>
                                    <div class="absolute bottom-2/4 left-0 right-0 h-px bg-gray-200 dark:bg-gray-700"></div>
                                    <div class="absolute bottom-3/4 left-0 right-0 h-px bg-gray-200 dark:bg-gray-700"></div>
                                    <div class="absolute bottom-0 left-0 right-0 h-full flex items-end">
                                        <svg class="w-full h-full" viewBox="0 0 100 40">
                                            <path d="M0,35 L10,30 L20,32 L30,25 L40,28 L50,20 L60,15 L70,18 L80,10 L90,12 L100,5" fill="none" stroke="#4f46e5" stroke-width="2"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                            <h3 class="text-sm font-medium mb-3">Scan Statistics</h3>
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="text-2xl font-bold text-primary-600">15</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Total Vulnerabilities</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-green-600">85%</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Scan Coverage</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-blue-600">1:42</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Scan Duration</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Request History -->
            <div class="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-3">
                <h3 class="text-sm font-medium mb-2">Request History</h3>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                    <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md text-xs">
                        <div>
                            <span class="font-medium">Vulnerability Scan</span>
                            <span class="text-gray-500 dark:text-gray-400 ml-2">10:31 AM</span>
                        </div>
                        <div class="flex space-x-1">
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="Re-run">
                                <i class="fas fa-redo-alt"></i>
                            </button>
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="View Details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md text-xs">
                        <div>
                            <span class="font-medium">Port Scan</span>
                            <span class="text-gray-500 dark:text-gray-400 ml-2">10:15 AM</span>
                        </div>
                        <div class="flex space-x-1">
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="Re-run">
                                <i class="fas fa-redo-alt"></i>
                            </button>
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="View Details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md text-xs">
                        <div>
                            <span class="font-medium">Directory Enumeration</span>
                            <span class="text-gray-500 dark:text-gray-400 ml-2">Yesterday</span>
                        </div>
                        <div class="flex space-x-1">
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="Re-run">
                                <i class="fas fa-redo-alt"></i>
                            </button>
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="View Details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Toast -->
        <div id="notificationToast" class="fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 flex items-start space-x-3 max-w-sm animate-slide-in hidden">
            <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-full p-2">
                <i class="fas fa-check text-green-600 dark:text-green-400"></i>
            </div>
            <div class="flex-1">
                <h4 class="font-medium">Process Complete</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300">Vulnerability scan has completed successfully.</p>
                <div class="mt-2 flex justify-end">
                    <button id="closeToastBtn" class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">Dismiss</button>
                </div>
            </div>
        </div>

        <!-- Error Modal -->
        <div id="errorModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden animate-fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md animate-slide-in">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-red-600">Error Occurred</h2>
                    <button id="closeErrorBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="bg-red-100 dark:bg-red-900 p-4 rounded-md mb-4">
                    <p id="errorMessage" class="text-red-800 dark:text-red-200">Connection timeout occurred during the vulnerability scan. The target server is not responding.</p>
                </div>
                <div class="flex justify-end space-x-2">
                    <button id="retryErrorBtn" class="bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md transition-colors">Retry</button>
                    <button id="ignoreErrorBtn" class="bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 py-2 px-4 rounded-md transition-colors">Ignore</button>
                </div>
            </div>
        </div>

        <!-- Confirm Modal -->
        <div id="confirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden animate-fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md animate-slide-in">
                <div class="flex justify-between items-center mb-4">
                    <h2 id="confirmModalTitle" class="text-xl font-bold text-yellow-600">Confirm Action</h2>
                    <button id="closeConfirmBtn" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-md mb-4">
                    <p id="confirmModalMessage" class="text-yellow-800 dark:text-yellow-200">Are you sure you want to proceed?</p>
                </div>
                <div class="flex justify-end space-x-2">
                    <button id="confirmStop" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors">Yes, Proceed</button>
                    <button id="cancelStop" class="bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 py-2 px-4 rounded-md transition-colors">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js for metrics visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const functionThemeToggle = document.getElementById('functionThemeToggle');
        
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
            
            // Update chart colors if charts exist
            if (window.severityChart) updateChartColors();
            if (window.scanPerformanceChart) updateChartColors();
        }
        
        // Process Viewer Tab Switching
        const logsTabBtn = document.getElementById('logsTabBtn');
        const todoTabBtn = document.getElementById('todoTabBtn');
        const filesTabBtn = document.getElementById('filesTabBtn');
        const metricsTabBtn = document.getElementById('metricsTabBtn');
        
        const logsTabContent = document.getElementById('logsTabContent');
        const todoTabContent = document.getElementById('todoTabContent');
        const filesTabContent = document.getElementById('filesTabContent');
        const metricsTabContent = document.getElementById('metricsTabContent');
        
        function switchTab(activeTab, activeContent) {
            // Reset all tabs
            [logsTabBtn, todoTabBtn, filesTabBtn, metricsTabBtn].forEach(tab => {
                tab.classList.remove('border-b-2', 'border-primary-500', 'text-primary-600', 'dark:text-primary-400', 'font-medium');
                tab.classList.add('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');
            });
            
            // Hide all content
            [logsTabContent, todoTabContent, filesTabContent, metricsTabContent].forEach(content => {
                content.classList.add('hidden');
            });
            
            // Activate selected tab
            activeTab.classList.remove('text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300');
            activeTab.classList.add('border-b-2', 'border-primary-500', 'text-primary-600', 'dark:text-primary-400', 'font-medium');
            
            // Show selected content
            activeContent.classList.remove('hidden');
        }
        
        logsTabBtn.addEventListener('click', () => switchTab(logsTabBtn, logsTabContent));
        todoTabBtn.addEventListener('click', () => switchTab(todoTabBtn, todoTabContent));
        filesTabBtn.addEventListener('click', () => switchTab(filesTabBtn, filesTabContent));
        metricsTabBtn.addEventListener('click', () => switchTab(metricsTabBtn, metricsTabContent));
        
        // Initialize tabs - set Logs tab as active by default
        document.addEventListener('DOMContentLoaded', () => {
            switchTab(logsTabBtn, logsTabContent);
            initializeCharts();
        });
        
        // Initialize and render charts for metrics dashboard
        function initializeCharts() {
            // Vulnerability Severity Chart
            const severityCtx = document.getElementById('severityChart');
            if (severityCtx) {
                window.severityChart = new Chart(severityCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Critical', 'High', 'Medium', 'Low', 'Info'],
                        datasets: [{
                            data: [4, 7, 12, 8, 15],
                            backgroundColor: [
                                '#dc2626', // Critical - Red
                                '#ea580c', // High - Orange
                                '#eab308', // Medium - Yellow
                                '#2563eb', // Low - Blue
                                '#6b7280'  // Info - Gray
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                                }
                            },
                            title: {
                                display: true,
                                text: 'Vulnerability Severity Distribution',
                                color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                            }
                        }
                    }
                });
            }
            
            // Scan Performance Chart
            const performanceCtx = document.getElementById('scanPerformanceChart');
            if (performanceCtx) {
                window.scanPerformanceChart = new Chart(performanceCtx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Scan Duration (min)',
                            data: [12, 15, 10, 8, 9, 7],
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.2)',
                            tension: 0.3,
                            fill: true
                        }, {
                            label: 'Issues Found',
                            data: [25, 32, 28, 35, 30, 40],
                            borderColor: '#dc2626',
                            backgroundColor: 'rgba(220, 38, 38, 0.2)',
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                                },
                                grid: {
                                    color: document.documentElement.classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                                },
                                grid: {
                                    color: document.documentElement.classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                                }
                            },
                            title: {
                                display: true,
                                text: 'Scan Performance Trends',
                                color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                            }
                        }
                    }
                });
            }
        }
        
        // Update chart colors when theme changes
        function updateChartColors() {
            const isDark = document.documentElement.classList.contains('dark');
            const textColor = isDark ? '#e5e7eb' : '#374151';
            const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
            
            // Update severity chart
            if (window.severityChart) {
                window.severityChart.options.plugins.legend.labels.color = textColor;
                window.severityChart.options.plugins.title.color = textColor;
                window.severityChart.update();
            }
            
            // Update performance chart
            if (window.scanPerformanceChart) {
                window.scanPerformanceChart.options.plugins.legend.labels.color = textColor;
                window.scanPerformanceChart.options.plugins.title.color = textColor;
                window.scanPerformanceChart.options.scales.y.ticks.color = textColor;
                window.scanPerformanceChart.options.scales.x.ticks.color = textColor;
                window.scanPerformanceChart.options.scales.y.grid.color = gridColor;
                window.scanPerformanceChart.options.scales.x.grid.color = gridColor;
                window.scanPerformanceChart.update();
            }
        }
        
        // To-Do List Management
        const todoList = document.getElementById('todoList');
        const newTaskInput = document.getElementById('newTaskInput');
        const addTaskBtn = document.getElementById('addTaskBtn');
        
        // Sample tasks
        const initialTasks = [
            { id: 1, text: 'Review critical vulnerabilities', completed: false },
            { id: 2, text: 'Update security patches', completed: true },
            { id: 3, text: 'Document findings', completed: false }
        ];
        
        let tasks = [...initialTasks];
        
        function renderTasks() {
            if (!todoList) return;
            
            todoList.innerHTML = '';
            
            tasks.forEach(task => {
                const li = document.createElement('li');
                li.className = 'flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700';
                li.innerHTML = `
                    <div class="flex items-center">
                        <input type="checkbox" id="task-${task.id}" class="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" ${task.completed ? 'checked' : ''}>
                        <label for="task-${task.id}" class="${task.completed ? 'line-through text-gray-500 dark:text-gray-400' : 'text-gray-800 dark:text-gray-200'}">${task.text}</label>
                    </div>
                    <button class="delete-task text-red-500 hover:text-red-700" data-id="${task.id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                `;
                
                const checkbox = li.querySelector(`#task-${task.id}`);
                checkbox.addEventListener('change', () => toggleTaskComplete(task.id));
                
                const deleteBtn = li.querySelector('.delete-task');
                deleteBtn.addEventListener('click', () => deleteTask(task.id));
                
                todoList.appendChild(li);
            });
        }
        
        function addTask(text) {
            if (!text.trim()) return;
            
            const newTask = {
                id: Date.now(),
                text: text.trim(),
                completed: false
            };
            
            tasks.push(newTask);
            renderTasks();
            newTaskInput.value = '';
        }
        
        function toggleTaskComplete(id) {
            tasks = tasks.map(task => 
                task.id === id ? { ...task, completed: !task.completed } : task
            );
            renderTasks();
        }
        
        function deleteTask(id) {
            tasks = tasks.filter(task => task.id !== id);
            renderTasks();
        }
        
        // Initialize To-Do list
        document.addEventListener('DOMContentLoaded', () => {
            renderTasks();
            
            if (addTaskBtn && newTaskInput) {
                addTaskBtn.addEventListener('click', () => addTask(newTaskInput.value));
                newTaskInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') addTask(newTaskInput.value);
                });
            }
            
            // Initialize Files tab functionality
            initializeFilesTab();
        });
        
        // Files Tab Management
        function initializeFilesTab() {
            const filesList = document.getElementById('filesList');
            const uploadFileBtn = document.getElementById('uploadFileBtn');
            const fileInput = document.getElementById('fileInput');
            
            if (!filesList || !uploadFileBtn || !fileInput) return;
            
            // Sample files
            const sampleFiles = [
                { name: 'scan_results.json', size: '24.5 KB', date: '2023-06-15', type: 'json' },
                { name: 'vulnerability_report.pdf', size: '1.2 MB', date: '2023-06-14', type: 'pdf' },
                { name: 'network_map.png', size: '340 KB', date: '2023-06-13', type: 'image' }
            ];
            
            // Render files list
            function renderFiles() {
                filesList.innerHTML = '';
                
                sampleFiles.forEach(file => {
                    const li = document.createElement('li');
                    li.className = 'flex items-center justify-between p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md';
                    
                    // Determine icon based on file type
                    let icon = 'fa-file';
                    if (file.type === 'pdf') icon = 'fa-file-pdf';
                    else if (file.type === 'json') icon = 'fa-file-code';
                    else if (file.type === 'image') icon = 'fa-file-image';
                    
                    li.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas ${icon} text-primary-500 mr-3 text-lg"></i>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-gray-200">${file.name}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">${file.size} • ${file.date}</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="download-file text-primary-500 hover:text-primary-700" title="Download">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="delete-file text-red-500 hover:text-red-700" title="Delete">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    `;
                    
                    // Add event listeners
                    const downloadBtn = li.querySelector('.download-file');
                    downloadBtn.addEventListener('click', () => downloadFile(file));
                    
                    const deleteBtn = li.querySelector('.delete-file');
                    deleteBtn.addEventListener('click', () => deleteFile(file));
                    
                    filesList.appendChild(li);
                });
            }
            
            // Simulate file download
            function downloadFile(file) {
                // In a real app, this would trigger an actual download
                console.log(`Downloading ${file.name}`);
                
                // Show notification toast
                showNotification(`Downloading ${file.name}...`, 'success');
            }
            
            // Delete file
            function deleteFile(file) {
                const index = sampleFiles.findIndex(f => f.name === file.name);
                if (index !== -1) {
                    sampleFiles.splice(index, 1);
                    renderFiles();
                    showNotification(`${file.name} deleted`, 'info');
                }
            }
            
            // Handle file upload
            uploadFileBtn.addEventListener('click', () => {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                if (files.length > 0) {
                    // In a real app, this would upload the file to a server
                    // For demo purposes, we'll just add it to our sample files
                    Array.from(files).forEach(file => {
                        const fileType = file.name.split('.').pop().toLowerCase();
                        let type = 'file';
                        
                        if (['pdf'].includes(fileType)) type = 'pdf';
                        else if (['json', 'js', 'html', 'css', 'txt'].includes(fileType)) type = 'json';
                        else if (['png', 'jpg', 'jpeg', 'gif', 'svg'].includes(fileType)) type = 'image';
                        
                        const newFile = {
                            name: file.name,
                            size: `${(file.size / 1024).toFixed(1)} KB`,
                            date: new Date().toISOString().split('T')[0],
                            type: type
                        };
                        
                        sampleFiles.unshift(newFile);
                    });
                    
                    renderFiles();
                    showNotification(`${files.length} file(s) uploaded successfully`, 'success');
                    fileInput.value = '';
                }
            });
            
            // Initial render
            renderFiles();
        }
        
        // Handle chat tab switching
        function initChatTabs() {
            const chatTabButtons = document.querySelectorAll('.chat-tab-btn');
            const chatTabContents = document.querySelectorAll('.chat-tab-content');
            
            chatTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    chatTabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('border-b-2');
                        btn.classList.remove('border-primary-500');
                        btn.classList.remove('text-primary-600');
                        btn.classList.remove('dark:text-primary-400');
                        btn.classList.remove('font-medium');
                        btn.classList.add('text-gray-500');
                        btn.classList.add('dark:text-gray-400');
                    });
                    
                    // Add active class to clicked button
                    button.classList.add('active');
                    button.classList.add('border-b-2');
                    button.classList.add('border-primary-500');
                    button.classList.add('text-primary-600');
                    button.classList.add('dark:text-primary-400');
                    button.classList.add('font-medium');
                    button.classList.remove('text-gray-500');
                    button.classList.remove('dark:text-gray-400');
                    
                    // Hide all tab contents
                    chatTabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // Show corresponding tab content
                    const tabId = button.id;
                    let contentId;
                    
                    if (tabId === 'securityScanTab') {
                        contentId = 'securityScanContent';
                    } else if (tabId === 'codeReviewTab') {
                        contentId = 'codeReviewContent';
                    } else if (tabId === 'threatAnalysisTab') {
                        contentId = 'threatAnalysisContent';
                    }
                    
                    if (contentId) {
                        const contentElement = document.getElementById(contentId);
                        if (contentElement) {
                            contentElement.classList.remove('hidden');
                        }
                    }
                    
                    // Show notification
                    showNotification(`Switched to ${button.textContent.trim()} tab`, 'info');
                });
            });
            
            // Handle new tab button
            const newTabBtn = document.getElementById('newTabBtn');
            if (newTabBtn) {
                newTabBtn.addEventListener('click', () => {
                    // In a real app, this would create a new tab
                    showNotification('New tab functionality coming soon', 'info');
                });
            }
        }
        
        // Show notification toast
        function showNotification(message, type = 'info') {
            const notificationToast = document.getElementById('notificationToast');
            const notificationMessage = document.getElementById('notificationMessage');
            const notificationIcon = document.getElementById('notificationIcon');
            
            if (!notificationToast || !notificationMessage || !notificationIcon) return;
            
            // Set message
            notificationMessage.textContent = message;
            
            // Set icon based on type
            notificationIcon.className = 'fas mr-2';
            switch (type) {
                case 'success':
                    notificationIcon.classList.add('fa-check-circle', 'text-green-500');
                    break;
                case 'warning':
                    notificationIcon.classList.add('fa-exclamation-triangle', 'text-yellow-500');
                    break;
                case 'error':
                    notificationIcon.classList.add('fa-times-circle', 'text-red-500');
                    break;
                default: // info
                    notificationIcon.classList.add('fa-info-circle', 'text-primary-500');
                    break;
            }
            
            // Show notification
            notificationToast.classList.remove('hidden');
            notificationToast.classList.add('animate-fadeIn');
            
            // Hide after 3 seconds
            setTimeout(() => {
                notificationToast.classList.add('animate-fadeOut');
                setTimeout(() => {
                    notificationToast.classList.add('hidden');
                    notificationToast.classList.remove('animate-fadeOut');
                }, 300);
            }, 3000);
        }
        
        // Check for saved theme preference
        if (localStorage.getItem('darkMode') === 'true' || 
            (localStorage.getItem('darkMode') === null && 
             window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }
        
        themeToggle.addEventListener('click', toggleDarkMode);
        functionThemeToggle.addEventListener('click', toggleDarkMode);
        
        // User Menu Toggle
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userMenu = document.getElementById('userMenu');
        
        userMenuBtn.addEventListener('click', () => {
            userMenu.classList.toggle('hidden');
        });
        
        // Role Selector Functionality
        const roleSelector = document.getElementById('roleSelector');
        const currentRoleDisplay = document.getElementById('currentRole');
        const roleOptions = document.querySelectorAll('.role-option');
        
        if (roleSelector && currentRoleDisplay && roleOptions.length > 0) {
            // Set default role
            let currentRole = 'Security Analyst';
            currentRoleDisplay.textContent = currentRole;
            
            // Handle role selection
            roleOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const newRole = option.textContent.trim();
                    currentRole = newRole;
                    currentRoleDisplay.textContent = newRole;
                    
                    // Close the menu
                    userMenu.classList.add('hidden');
                    
                    // Show notification
                    showNotification(`Role changed to ${newRole}`, 'success');
                    
                    // In a real app, this would update the user's permissions and available features
                    console.log(`Role changed to: ${newRole}`);
                });
            });
        }
        
        // View Options Menu Toggle
        const viewOptionsBtn = document.getElementById('viewOptionsBtn');
        const viewOptionsMenu = document.getElementById('viewOptionsMenu');
        
        viewOptionsBtn.addEventListener('click', () => {
            viewOptionsMenu.classList.toggle('hidden');
        });
        
        // Environment Selector Functionality
        const envSelector = document.getElementById('environmentSelector');
        const currentEnvDisplay = document.getElementById('currentEnvironment');
        const envOptions = document.querySelectorAll('.env-option');
        const apiVersionSelector = document.getElementById('apiVersionSelector');
        const apiVersionOptions = document.querySelectorAll('.api-version-option');
        
        if (envSelector && currentEnvDisplay && envOptions.length > 0) {
            // Set default environment
            let currentEnv = 'Production';
            currentEnvDisplay.textContent = currentEnv;
            
            // Handle environment selection
            envOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const newEnv = option.textContent.trim();
                    currentEnv = newEnv;
                    currentEnvDisplay.textContent = newEnv;
                    
                    // Close the menu
                    envSelector.classList.add('hidden');
                    
                    // Show notification
                    showNotification(`Environment changed to ${newEnv}`, 'info');
                    
                    // In a real app, this would update the API endpoints and available features
                    console.log(`Environment changed to: ${newEnv}`);
                });
            });
            
            // Toggle environment selector
            const envSelectorBtn = document.getElementById('environmentSelectorBtn');
            if (envSelectorBtn) {
                envSelectorBtn.addEventListener('click', () => {
                    envSelector.classList.toggle('hidden');
                });
                
                // Close when clicking outside
                document.addEventListener('click', (e) => {
                    if (!envSelectorBtn.contains(e.target) && !envSelector.contains(e.target)) {
                        envSelector.classList.add('hidden');
                    }
                });
            }
        }
        
        // API Version Selector Functionality
        if (apiVersionSelector && apiVersionOptions.length > 0) {
            // Set default API version
            let currentApiVersion = 'v1';
            
            // Handle API version selection
            apiVersionOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const newVersion = option.textContent.trim();
                    currentApiVersion = newVersion;
                    
                    // Update visual selection
                    apiVersionOptions.forEach(opt => {
                        opt.classList.remove('bg-primary-100', 'dark:bg-primary-900', 'text-primary-800', 'dark:text-primary-200');
                        opt.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-700');
                    });
                    
                    option.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-700');
                    option.classList.add('bg-primary-100', 'dark:bg-primary-900', 'text-primary-800', 'dark:text-primary-200');
                    
                    // Close the menu
                    apiVersionSelector.classList.add('hidden');
                    
                    // Show notification
                    showNotification(`API Version changed to ${newVersion}`, 'info');
                    
                    // In a real app, this would update the API endpoints
                    console.log(`API Version changed to: ${newVersion}`);
                });
            });
            
            // Toggle API version selector
            const apiVersionBtn = document.getElementById('apiVersionBtn');
            if (apiVersionBtn) {
                apiVersionBtn.addEventListener('click', () => {
                    apiVersionSelector.classList.toggle('hidden');
                });
                
                // Close when clicking outside
                document.addEventListener('click', (e) => {
                    if (!apiVersionBtn.contains(e.target) && !apiVersionSelector.contains(e.target)) {
                        apiVersionSelector.classList.add('hidden');
                    }
                });
            }
        }
        
        // Close menus when clicking outside
        document.addEventListener('click', (e) => {
            if (!userMenuBtn.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
            if (!viewOptionsBtn.contains(e.target) && !viewOptionsMenu.contains(e.target)) {
                viewOptionsMenu.classList.add('hidden');
            }
            
            // These checks are redundant with the individual handlers above,
            // but we keep them here as a fallback to ensure all menus close properly
            const envSelectorBtn = document.getElementById('environmentSelectorBtn');
            const envSelector = document.getElementById('environmentSelector');
            if (envSelectorBtn && envSelector && !envSelectorBtn.contains(e.target) && !envSelector.contains(e.target)) {
                envSelector.classList.add('hidden');
            }
            
            const apiVersionBtn = document.getElementById('apiVersionBtn');
            const apiVersionSelector = document.getElementById('apiVersionSelector');
            if (apiVersionBtn && apiVersionSelector && !apiVersionBtn.contains(e.target) && !apiVersionSelector.contains(e.target)) {
                apiVersionSelector.classList.add('hidden');
            }
        });
        
        // Parameters Section Toggle
        const toggleParamsBtn = document.getElementById('toggleParamsBtn');
        const paramsSection = document.getElementById('paramsSection');
        
        toggleParamsBtn.addEventListener('click', () => {
            const isExpanded = paramsSection.style.maxHeight !== '0px';
            paramsSection.style.maxHeight = isExpanded ? '0px' : '250px';
            toggleParamsBtn.innerHTML = isExpanded ? 
                '<i class="fas fa-chevron-down"></i> Show Additional Parameters' : 
                '<i class="fas fa-chevron-up"></i> Hide Additional Parameters';
        });
        
        // Function Selection Handling
        const functionSelectionModal = document.getElementById('functionSelectionModal');
        const functionCards = document.querySelectorAll('.function-card');
        
        // Handle function card clicks
        functionCards.forEach(card => {
            card.addEventListener('click', () => {
                try {
                    const selectedFunction = card.getAttribute('data-function');
                    
                    // Validate function availability
                    if (!isValidFunction(selectedFunction)) {
                        showErrorNotification(`Function "${selectedFunction}" is currently unavailable. Redirecting to General Security.`);
                        handleFunctionFallback();
                        return;
                    }
                    
                    // Add visual feedback
                    card.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        card.style.transform = 'scale(1)';
                    }, 150);
                    
                    // Hide the function selection modal
                    functionSelectionModal.classList.add('hidden');
                    
                    // Set the active tab based on selected function
                    setActiveFunction(selectedFunction);
                    
                    // Show welcome message for selected function
                    showFunctionWelcomeMessage(selectedFunction);
                    
                    // Start auto-logout timer
                    startAutoLogoutTimer();
                    
                } catch (error) {
                    console.error('Error in function selection:', error);
                    showErrorNotification('An error occurred while selecting the function. Redirecting to General Security.');
                    handleFunctionFallback();
                }
            });
        });
        
        // Function to set active tab and content
        function setActiveFunction(functionType) {
            // Set current function in session storage
            sessionStorage.currentFunction = functionType;
            
            // Remove active class from all tabs
            document.querySelectorAll('.chat-tab-btn').forEach(tab => {
                tab.classList.remove('border-primary-500', 'text-primary-600', 'dark:text-primary-400', 'font-medium', 'active');
                tab.classList.add('text-gray-500', 'dark:text-gray-400');
            });
            
            // Hide all tab contents
            document.querySelectorAll('.chat-tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Clear existing chat messages
            const chatMessages = document.getElementById('chatMessages');
            if (chatMessages) {
                chatMessages.innerHTML = '';
            }
            
            // Activate the corresponding tab
            let activeTabId, activeContentId;
            switch(functionType) {
                case 'security-scan':
                    activeTabId = 'securityScanTab';
                    activeContentId = 'securityScanContent';
                    break;
                case 'code-review':
                    activeTabId = 'codeReviewTab';
                    activeContentId = 'codeReviewContent';
                    break;
                case 'threat-analysis':
                    activeTabId = 'threatAnalysisTab';
                    activeContentId = 'threatAnalysisContent';
                    break;
                case 'network-analysis':
                    activeTabId = 'securityScanTab';
                    activeContentId = 'securityScanContent';
                    break;
                case 'report-generator':
                    activeTabId = 'securityScanTab';
                    activeContentId = 'securityScanContent';
                    break;
                case 'data-import':
                    activeTabId = 'securityScanTab';
                    activeContentId = 'securityScanContent';
                    break;
                case 'alerts':
                    activeTabId = 'securityScanTab';
                    activeContentId = 'securityScanContent';
                    break;
                case 'general-security':
                default:
                    activeTabId = 'securityScanTab';
                    activeContentId = 'securityScanContent';
                    break;
            }
            
            const activeTab = document.getElementById(activeTabId);
            const activeContent = document.getElementById(activeContentId);
            
            if (activeTab) {
                activeTab.classList.remove('text-gray-500', 'dark:text-gray-400');
                activeTab.classList.add('border-primary-500', 'text-primary-600', 'dark:text-primary-400', 'font-medium', 'active');
            }
            
            if (activeContent) {
                activeContent.classList.remove('hidden');
            }
            
            // Initialize chat assistant for the selected function
            initializeChatAssistant(functionType);
        }
        
        // Function to show welcome message based on selected function
        function showFunctionWelcomeMessage(functionType) {
            const welcomeMessages = {
                'security-scan': 'Hello! I\'m your Security Scan AI assistant. I can help you with vulnerability assessments, penetration testing, and security analysis. What would you like to scan today?',
                'code-review': 'Hello! I\'m your Code Review AI assistant. I can help you analyze code for security vulnerabilities, best practices, and optimization opportunities. Please share your code for review.',
                'threat-analysis': 'Hello! I\'m your Threat Analysis AI assistant. I can help you with threat intelligence, risk assessment, and incident analysis. What threat would you like me to analyze?',
                'network-analysis': 'Hello! I\'m your Network Analysis AI assistant. I can help you with network security, traffic analysis, and infrastructure assessment. What network component would you like to analyze?',
                'report-generator': 'Hello! I\'m your Report Generator AI assistant. I can help you create structured security reports in PDF, Markdown, or HTML format. What type of report would you like to generate?',
                'data-import': 'Hello! I\'m your Data Import AI assistant. I can help you upload and analyze code files, logs, and configuration files for security assessment. Please drag and drop your files here or click to upload.',
                'alerts': 'Hello! I\'m your Alerts AI assistant. I can help you monitor and receive notifications for critical security findings. What type of alerts would you like to set up?',
                'general-security': 'Hello! I\'m your General Security AI assistant. I can help you with various cybersecurity questions and provide security consultation. How can I assist you today?'
            };
            
            const message = welcomeMessages[functionType] || welcomeMessages['general-security'];
            
            // Clear existing messages and show the welcome message
            const activeContent = document.querySelector('.chat-tab-content:not(.hidden)');
            if (activeContent) {
                activeContent.innerHTML = `
                    <div class="flex items-start space-x-3 animate-fade-in">
                        <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
                            <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
                        </div>
                        <div class="flex-1">
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                                <p class="text-sm">${message}</p>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                <span>${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                                <div class="flex space-x-2 ml-2">
                                    <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Copy to clipboard">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Save response">
                                        <i class="fas fa-bookmark"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        
        // Error Handling Functions
        function isValidFunction(functionType) {
            const validFunctions = [
                'security-scan', 'code-review', 'threat-analysis', 
                'network-analysis', 'general-security', 'report-generator', 
                'data-import', 'alerts'
            ];
            return validFunctions.includes(functionType);
        }
        
        function showErrorNotification(message) {
            // Create or update error notification
            let errorNotification = document.getElementById('errorNotification');
            if (!errorNotification) {
                errorNotification = document.createElement('div');
                errorNotification.id = 'errorNotification';
                errorNotification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-fade-in';
                document.body.appendChild(errorNotification);
            }
            
            errorNotification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:text-red-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorNotification && errorNotification.parentNode) {
                    errorNotification.remove();
                }
            }, 5000);
        }
        
        function handleFunctionFallback() {
            try {
                // Hide the function selection modal
                functionSelectionModal.classList.add('hidden');
                
                // Set to general security as fallback
                setActiveFunction('general-security');
                
                // Show fallback welcome message
                showFunctionWelcomeMessage('general-security');
                
                // Start auto-logout timer
                startAutoLogoutTimer();
                
            } catch (error) {
                console.error('Error in fallback handling:', error);
                // Last resort: reload the page
                location.reload();
            }
        }
        
        // API Error Handling
        function handleApiError(error, retryFunction = null) {
            console.error('API Error:', error);
            
            let errorMessage = 'An error occurred while processing your request.';
            if (error.message) {
                errorMessage = error.message;
            } else if (typeof error === 'string') {
                errorMessage = error;
            }
            
            // Show error modal with retry option
            showErrorModal(errorMessage, retryFunction);
        }
        
        function showErrorModal(message, retryFunction = null) {
            let errorModal = document.getElementById('errorModal');
            if (!errorModal) {
                errorModal = document.createElement('div');
                errorModal.id = 'errorModal';
                errorModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                document.body.appendChild(errorModal);
            }
            
            errorModal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="bg-red-100 dark:bg-red-900 rounded-full p-2">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Error</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">${message}</p>
                    <div class="flex space-x-3 justify-end">
                        ${retryFunction ? '<button onclick="' + retryFunction + '(); this.closest(\'.fixed\').remove();" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Retry</button>' : ''}
                        <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">Close</button>
                    </div>
                </div>
            `;
            
            errorModal.classList.remove('hidden');
         }
         
         // Loading Indicator Functions
         function showLoadingOverlay(message = 'Processing...') {
             let loadingOverlay = document.getElementById('loadingOverlay');
             if (!loadingOverlay) {
                 loadingOverlay = document.createElement('div');
                 loadingOverlay.id = 'loadingOverlay';
                 loadingOverlay.className = 'loading-overlay';
                 document.body.appendChild(loadingOverlay);
             }
             
             loadingOverlay.innerHTML = `
                 <div class="loading-content animate-fade-in">
                     <div class="loading-spinner mx-auto mb-4"></div>
                     <p class="text-gray-600 dark:text-gray-300">${message}</p>
                 </div>
             `;
             
             loadingOverlay.classList.remove('hidden');
         }
         
         function hideLoadingOverlay() {
             const loadingOverlay = document.getElementById('loadingOverlay');
             if (loadingOverlay) {
                 loadingOverlay.classList.add('hidden');
             }
         }
         
         function showInlineLoading(elementId, message = 'Loading...') {
             const element = document.getElementById(elementId);
             if (element) {
                 element.innerHTML = `
                     <div class="flex items-center justify-center space-x-2 py-4">
                         <div class="loading-spinner"></div>
                         <span class="text-gray-600 dark:text-gray-300">${message}</span>
                     </div>
                 `;
             }
         }
         
         function addButtonLoading(buttonElement, originalText = null) {
             if (!originalText) {
                 originalText = buttonElement.textContent;
             }
             buttonElement.disabled = true;
             buttonElement.innerHTML = `
                 <div class="flex items-center space-x-2">
                     <div class="loading-spinner"></div>
                     <span>Processing...</span>
                 </div>
             `;
             buttonElement.dataset.originalText = originalText;
         }
         
         function removeButtonLoading(buttonElement) {
             const originalText = buttonElement.dataset.originalText || 'Submit';
             buttonElement.disabled = false;
             buttonElement.textContent = originalText;
             delete buttonElement.dataset.originalText;
         }
         
         // Session Management Functions
         const sessionStorage = {
             sessions: {},
             currentFunction: null,
             
             createSession: function(functionType) {
                 if (!this.sessions[functionType]) {
                     this.sessions[functionType] = {
                         messages: [],
                         timestamp: new Date().toISOString(),
                         settings: {},
                         files: []
                     };
                 }
                 this.currentFunction = functionType;
                 return this.sessions[functionType];
             },
             
             addMessage: function(functionType, message) {
                 if (!this.sessions[functionType]) {
                     this.createSession(functionType);
                 }
                 this.sessions[functionType].messages.push({
                     ...message,
                     timestamp: new Date().toISOString()
                 });
                 this.saveToLocalStorage();
             },
             
             getSession: function(functionType) {
                 return this.sessions[functionType] || null;
             },
             
             clearSession: function(functionType) {
                 if (this.sessions[functionType]) {
                     delete this.sessions[functionType];
                     this.saveToLocalStorage();
                 }
             },
             
             exportSession: function(functionType, format = 'json') {
                 const session = this.getSession(functionType);
                 if (!session) return null;
                 
                 const timestamp = new Date().toISOString().split('T')[0];
                 const filename = `${functionType}-session-${timestamp}`;
                 
                 switch (format) {
                     case 'json':
                         this.downloadFile(`${filename}.json`, JSON.stringify(session, null, 2), 'application/json');
                         break;
                     case 'markdown':
                         const markdown = this.sessionToMarkdown(session, functionType);
                         this.downloadFile(`${filename}.md`, markdown, 'text/markdown');
                         break;
                     case 'html':
                         const html = this.sessionToHtml(session, functionType);
                         this.downloadFile(`${filename}.html`, html, 'text/html');
                         break;
                 }
             },
             
             sessionToMarkdown: function(session, functionType) {
                 let markdown = `# ${functionType.replace('-', ' ').toUpperCase()} Session\n\n`;
                 markdown += `**Date:** ${new Date(session.timestamp).toLocaleDateString()}\n\n`;
                 
                 session.messages.forEach((msg, index) => {
                     markdown += `## Message ${index + 1} (${msg.role || 'user'})\n\n`;
                     markdown += `${msg.content}\n\n`;
                     markdown += `*Time: ${new Date(msg.timestamp).toLocaleTimeString()}*\n\n---\n\n`;
                 });
                 
                 return markdown;
             },
             
             sessionToHtml: function(session, functionType) {
                 let html = `<!DOCTYPE html><html><head><title>${functionType} Session</title></head><body>`;
                 html += `<h1>${functionType.replace('-', ' ').toUpperCase()} Session</h1>`;
                 html += `<p><strong>Date:</strong> ${new Date(session.timestamp).toLocaleDateString()}</p>`;
                 
                 session.messages.forEach((msg, index) => {
                     html += `<div style="margin: 20px 0; padding: 10px; border-left: 3px solid #ccc;">`;
                     html += `<h3>Message ${index + 1} (${msg.role || 'user'})</h3>`;
                     html += `<p>${msg.content}</p>`;
                     html += `<small>Time: ${new Date(msg.timestamp).toLocaleTimeString()}</small>`;
                     html += `</div>`;
                 });
                 
                 html += `</body></html>`;
                 return html;
             },
             
             downloadFile: function(filename, content, mimeType) {
                 const blob = new Blob([content], { type: mimeType });
                 const url = URL.createObjectURL(blob);
                 const a = document.createElement('a');
                 a.href = url;
                 a.download = filename;
                 document.body.appendChild(a);
                 a.click();
                 document.body.removeChild(a);
                 URL.revokeObjectURL(url);
             },
             
             saveToLocalStorage: function() {
                 try {
                     localStorage.setItem('hexstrike-sessions', JSON.stringify(this.sessions));
                 } catch (error) {
                     console.warn('Could not save sessions to localStorage:', error);
                 }
             },
             
             loadFromLocalStorage: function() {
                 try {
                     const saved = localStorage.getItem('hexstrike-sessions');
                     if (saved) {
                         this.sessions = JSON.parse(saved);
                     }
                 } catch (error) {
                     console.warn('Could not load sessions from localStorage:', error);
                 }
             }
         };
         
         // Initialize session storage
         sessionStorage.loadFromLocalStorage();
         
         // Chat Assistant Guidelines Implementation
         const chatAssistant = {
             hasWelcomed: false,
             isWaitingForUser: true,
             
             // Validate message metadata
             validateMessage: function(message) {
                 if (!message || typeof message !== 'object') {
                     return false;
                 }
                 
                 // Check for required metadata
                 if (!message.role || message.role !== 'user') {
                     console.warn('Message rejected: Invalid or missing role');
                     return false;
                 }
                 
                 if (!message.source || message.source !== 'ui') {
                     console.warn('Message rejected: Invalid or missing source');
                     return false;
                 }
                 
                 return true;
             },
             
             // Process user input with validation
             processUserInput: function(input, metadata = {}) {
                 // Validate input is not fabricated
                 if (!input || typeof input !== 'string' || input.trim() === '') {
                     this.showError('Please provide a valid message.');
                     return false;
                 }
                 
                 // Create message object with metadata
                 const message = {
                     content: input.trim(),
                     role: 'user',
                     source: 'ui',
                     timestamp: new Date().toISOString(),
                     ...metadata
                 };
                 
                 // Validate message
                 if (!this.validateMessage(message)) {
                     this.showError('Message validation failed. Please try again.');
                     return false;
                 }
                 
                 // Process valid message
                 this.isWaitingForUser = false;
                 return this.handleValidMessage(message);
             },
             
             // Handle validated user message
             handleValidMessage: function(message) {
                 // Check if message requires target details for scans/tasks
                 if (this.requiresTargetDetails(message.content)) {
                     const missingFields = this.getMissingRequiredFields(message.content);
                     if (missingFields.length > 0) {
                         this.requestRequiredFields(missingFields);
                         return true;
                     }
                 }
                 
                 // Add to session storage
                 const currentFunction = sessionStorage.currentFunction || 'general-security';
                 sessionStorage.addMessage(currentFunction, message);
                 
                 // Process the message
                 this.sendToBackend(message);
                 return true;
             },
             
             // Check if message requires target details
             requiresTargetDetails: function(content) {
                 const scanKeywords = ['scan', 'test', 'analyze', 'audit', 'penetration', 'vulnerability'];
                 const taskKeywords = ['perform', 'execute', 'run', 'start', 'begin'];
                 
                 const lowerContent = content.toLowerCase();
                 return scanKeywords.some(keyword => lowerContent.includes(keyword)) ||
                        taskKeywords.some(keyword => lowerContent.includes(keyword));
             },
             
             // Get missing required fields
             getMissingRequiredFields: function(content) {
                 const missingFields = [];
                 const lowerContent = content.toLowerCase();
                 
                 // Check for target/URL
                 if (!this.hasTarget(content)) {
                     missingFields.push('target URL or IP address');
                 }
                 
                 // Check for scan type if mentioned
                 if (lowerContent.includes('scan') && !this.hasScanType(content)) {
                     missingFields.push('scan type (e.g., port scan, vulnerability scan)');
                 }
                 
                 return missingFields;
             },
             
             // Check if content has target information
             hasTarget: function(content) {
                 const urlPattern = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/;
                 const ipPattern = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/;
                 const domainPattern = /\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}\b/;
                 
                 return urlPattern.test(content) || ipPattern.test(content) || domainPattern.test(content);
             },
             
             // Check if content has scan type
             hasScanType: function(content) {
                 const scanTypes = ['port', 'vulnerability', 'network', 'web', 'ssl', 'dns', 'subdomain'];
                 const lowerContent = content.toLowerCase();
                 return scanTypes.some(type => lowerContent.includes(type));
             },
             
             // Request required fields from user
             requestRequiredFields: function(missingFields) {
                 const fieldList = missingFields.join(', ');
                 const message = `To perform this task, I need the following information: ${fieldList}. Please provide these details.`;
                 
                 this.addAssistantMessage(message);
                 this.isWaitingForUser = true;
             },
             
             // Show initial welcome message
             showWelcome: function() {
                 if (!this.hasWelcomed) {
                     const welcomeMessage = "Hello! I'm your cybersecurity assistant. How can I help?";
                     this.addAssistantMessage(welcomeMessage);
                     this.hasWelcomed = true;
                     this.isWaitingForUser = true;
                 }
             },
             
             // Add assistant message to chat
             addAssistantMessage: function(content) {
                 const message = {
                     content: content,
                     role: 'assistant',
                     source: 'system',
                     timestamp: new Date().toISOString()
                 };
                 
                 // Add to current session
                 const currentFunction = sessionStorage.currentFunction || 'general-security';
                 sessionStorage.addMessage(currentFunction, message);
                 
                 // Display in chat
                 this.displayMessage(message);
             },
             
             // Display message in chat interface
             displayMessage: function(message) {
                 const chatMessages = document.getElementById('chatMessages');
                 if (!chatMessages) return;
                 
                 const messageDiv = document.createElement('div');
                 messageDiv.className = `message ${message.role}-message`;
                 messageDiv.innerHTML = `
                     <div class="message-content">
                         <p>${message.content}</p>
                         <small class="message-time">${new Date(message.timestamp).toLocaleTimeString()}</small>
                     </div>
                 `;
                 
                 chatMessages.appendChild(messageDiv);
                 chatMessages.scrollTop = chatMessages.scrollHeight;
             },
             
             // Send message to backend
             sendToBackend: function(message) {
                 // Show loading indicator
                 showLoadingOverlay('Processing your request...');
                 
                 // Simulate API call (replace with actual backend integration)
                 setTimeout(() => {
                     hideLoadingOverlay();
                     
                     // Add assistant response
                     const response = this.generateResponse(message.content);
                     this.addAssistantMessage(response);
                     
                     this.isWaitingForUser = true;
                 }, 1500);
             },
             
             // Generate appropriate response (placeholder)
             generateResponse: function(userInput) {
                 const currentFunction = sessionStorage.currentFunction || 'general-security';
                 
                 switch (currentFunction) {
                     case 'security-scan':
                         return 'Security scan initiated. I\'ll analyze the target for vulnerabilities and provide a comprehensive report.';
                     case 'code-review':
                         return 'Code review started. I\'ll examine the code for security issues, best practices, and potential vulnerabilities.';
                     case 'threat-analysis':
                         return 'Threat analysis in progress. I\'ll assess potential security threats and provide mitigation recommendations.';
                     case 'network-analysis':
                         return 'Network analysis initiated. I\'ll examine network traffic and infrastructure for security concerns.';
                     case 'report-generator':
                         return 'Report generation started. I\'ll compile the security findings into a comprehensive report.';
                     case 'data-import':
                         return 'Data import process initiated. I\'ll securely process and analyze the imported data.';
                     case 'alerts':
                         return 'Alert system activated. I\'ll monitor for security events and notify you of any concerns.';
                     default:
                         return 'I\'m ready to assist with your cybersecurity needs. Please let me know what specific task you\'d like me to help with.';
                 }
             },
             
             // Show error message
             showError: function(message) {
                 showErrorNotification(message);
             },
             
             // Initialize assistant
             init: function() {
                 // Show welcome message if no prior conversation
                 const currentFunction = sessionStorage.currentFunction;
                 const session = currentFunction ? sessionStorage.getSession(currentFunction) : null;
                 
                 if (!session || session.messages.length === 0) {
                     this.showWelcome();
                 } else {
                     this.hasWelcomed = true;
                     // Restore conversation
                     session.messages.forEach(msg => {
                         if (msg.role === 'assistant' || msg.role === 'user') {
                             this.displayMessage(msg);
                         }
                     });
                 }
             }
         };
         
         // Initialize chat assistant when function is selected
         function initializeChatAssistant() {
             chatAssistant.init();
         }
         
         // Override message input handling
         function handleUserMessage(input) {
             return chatAssistant.processUserInput(input, { source: 'ui' });
         }
         
         // Logout
        const logoutBtn = document.getElementById('logoutBtn');
        
        logoutBtn.addEventListener('click', () => {
            functionSelectionModal.classList.remove('hidden');
            userMenu.classList.add('hidden');
            clearAutoLogoutTimer();
        });
        
        // Auto Logout Timer
        let autoLogoutTimer;
        const AUTO_LOGOUT_TIME = 30 * 60 * 1000; // 30 minutes
        
        function startAutoLogoutTimer() {
            clearAutoLogoutTimer();
            autoLogoutTimer = setTimeout(() => {
                functionSelectionModal.classList.remove('hidden');
                showNotification('Session Expired', 'You have been logged out due to inactivity.');
            }, AUTO_LOGOUT_TIME);
        }
        
        function clearAutoLogoutTimer() {
            clearTimeout(autoLogoutTimer);
        }
        
        // Reset timer on user activity
        ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
            document.addEventListener(event, () => {
                if (functionSelectionModal.classList.contains('hidden')) {
                    startAutoLogoutTimer();
                }
            });
        });
        
        // Process Controls
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const changeBtn = document.getElementById('changeBtn');
        
        pauseBtn.addEventListener('click', () => {
            const isPaused = pauseBtn.innerHTML.includes('Resume');
            pauseBtn.innerHTML = isPaused ? 
                '<i class="fas fa-pause mr-1"></i> Pause' : 
                '<i class="fas fa-play mr-1"></i> Resume';
            pauseBtn.classList.toggle('bg-yellow-500');
            pauseBtn.classList.toggle('bg-green-500');
            pauseBtn.classList.toggle('hover:bg-yellow-600');
            pauseBtn.classList.toggle('hover:bg-green-600');
            
            // In a real app, you would pause/resume the process here
        });
        
        stopBtn.addEventListener('click', () => {
            // Show confirmation dialog in a real app
            showErrorModal('Confirm Stop', 'Are you sure you want to stop the current process? This action cannot be undone.');
            // In a real app, you would stop the process here
        });
        
        changeBtn.addEventListener('click', () => {
            // In a real app, you would show options to change the request
            showNotification('Change Request', 'You can modify your request parameters and resubmit.');
        });
        
        // Send Message
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const chatMessages = document.getElementById('chatMessages');
        
        sendBtn.addEventListener('click', () => {
            const message = messageInput.value.trim();
            if (message) {
                // Use chat assistant to handle the message
                handleUserMessage(message);
                messageInput.value = '';
            }
        });
        
        // Update the Enter key handler to also use chat assistant
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const message = messageInput.value.trim();
                if (message) {
                    handleUserMessage(message);
                    messageInput.value = '';
                }
            }
        });
        
        // Legacy demo response handler (keeping for fallback)
        function simulateLegacyResponse() {
            setTimeout(() => {
                    const aiMessageHTML = `
                        <div class="flex items-start space-x-3 animate-fade-in">
                            <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-2">
                                <i class="fas fa-robot text-primary-600 dark:text-primary-400"></i>
                            </div>
                            <div class="flex-1">
                                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                                    <p class="text-sm">I'm processing your request: "${message}"</p>
                                    <div class="typing-indicator mt-2">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </div>
                                </div>
                                <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                    <span>${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                                    <div class="flex space-x-2 ml-2">
                                        <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Copy to clipboard">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="hover:text-gray-700 dark:hover:text-gray-300" title="Save response">
                                            <i class="fas fa-bookmark"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    chatMessages.insertAdjacentHTML('beforeend', aiMessageHTML);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    
                    // Add log entries
                    const logsOutput = document.getElementById('logsOutput');
                    const logEntry = `<div class="text-blue-600 dark:text-blue-400">[${new Date().toLocaleTimeString()}] INFO: Processing user request: "${message}"</div>`;
                    logsOutput.querySelector('.space-y-1').insertAdjacentHTML('beforeend', logEntry);
                    logsOutput.scrollTop = logsOutput.scrollHeight;
                }, 500);
            }
        });
        
        // Enter key handling is now integrated with chat assistant above
        
        // File Upload
        const uploadBtn = document.getElementById('uploadBtn');
        
        uploadBtn.addEventListener('click', () => {
            // In a real app, you would open a file picker
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv,.json,.txt,.pdf';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    showNotification('File Uploaded', `File "${file.name}" has been uploaded.`);
                    // In a real app, you would handle the file upload here
                }
            };
            input.click();
        });
        
        // Notification Toast
        function showNotification(title, message) {
            const notificationToast = document.getElementById('notificationToast');
            const notificationTitle = notificationToast.querySelector('h4');
            const notificationMessage = notificationToast.querySelector('p');
            
            notificationTitle.textContent = title;
            notificationMessage.textContent = message;
            notificationToast.classList.remove('hidden');
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                notificationToast.classList.add('hidden');
            }, 5000);
        }
        
        // Close Notification
        const closeToastBtn = document.getElementById('closeToastBtn');
        
        closeToastBtn.addEventListener('click', () => {
            document.getElementById('notificationToast').classList.add('hidden');
        });
        
        // Error Modal
        function showErrorModal(title, message) {
            const errorModal = document.getElementById('errorModal');
            const errorTitle = errorModal.querySelector('h2');
            const errorMessage = document.getElementById('errorMessage');
            
            errorTitle.textContent = title;
            errorMessage.textContent = message;
            errorModal.classList.remove('hidden');
        }
        
        // Close Error Modal
        const closeErrorBtn = document.getElementById('closeErrorBtn');
        const retryErrorBtn = document.getElementById('retryErrorBtn');
        const ignoreErrorBtn = document.getElementById('ignoreErrorBtn');
        
        closeErrorBtn.addEventListener('click', () => {
            document.getElementById('errorModal').classList.add('hidden');
        });
        
        retryErrorBtn.addEventListener('click', () => {
            document.getElementById('errorModal').classList.add('hidden');
            showNotification('Retry', 'Retrying the operation...');
            // In a real app, you would retry the operation here
        });
        
        ignoreErrorBtn.addEventListener('click', () => {
            document.getElementById('errorModal').classList.add('hidden');
            showNotification('Ignored', 'Error has been ignored. Continuing with the process.');
            // In a real app, you would continue the process here
        });
        
        // New Chat functionality is handled in script.js via startNewChat function
        
        // Export Logs
        const exportLogsBtn = document.getElementById('exportLogsBtn');
        
        exportLogsBtn.addEventListener('click', () => {
            // In a real app, you would generate a file with the logs
            const logs = document.getElementById('logsOutput').innerText;
            const blob = new Blob([logs], {type: 'text/plain'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat_box_logs_${new Date().toISOString().slice(0,10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showNotification('Logs Exported', 'Logs have been exported as a text file.');
        });
        
        // Clear Logs
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        
        clearLogsBtn.addEventListener('click', () => {
            // In a real app, you would confirm before clearing
            document.getElementById('logsOutput').querySelector('.space-y-1').innerHTML = `
                <div class="text-gray-500 dark:text-gray-400">[${new Date().toLocaleTimeString()}] Logs cleared</div>
            `;
            
            showNotification('Logs Cleared', 'All logs have been cleared.');
        });
        
        // Initialize Split View
        // Add CSS for the resizer
        const style = document.createElement('style');
        style.textContent = `
            .resizer {
                width: 5px;
                background-color: #e5e7eb;
                cursor: col-resize;
                margin: 0;
                padding: 0;
            }
            .dark .resizer {
                background-color: #4b5563;
            }
        `;
        document.head.appendChild(style);
        
        // Initialize resizable panels when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const chatPanel = document.getElementById('chatPanel');
            const logsPanel = document.getElementById('logsPanel');
            
            if (chatPanel && logsPanel) {
                // Create resizer div
                const resizer = document.createElement('div');
                resizer.className = 'resizer';
                
                // Insert resizer between panels
                chatPanel.after(resizer);
                
                let isResizing = false;
                let initialX;
                let initialChatWidth;
                let initialLogsWidth;
                
                resizer.addEventListener('mousedown', function(e) {
                    isResizing = true;
                    initialX = e.clientX;
                    initialChatWidth = chatPanel.offsetWidth;
                    initialLogsWidth = logsPanel.offsetWidth;
                    
                    document.body.style.cursor = 'col-resize';
                    document.body.style.userSelect = 'none';
                });
                
                document.addEventListener('mousemove', function(e) {
                    if (!isResizing) return;
                    
                    const deltaX = e.clientX - initialX;
                    const newChatWidth = initialChatWidth + deltaX;
                    const newLogsWidth = initialLogsWidth - deltaX;
                    
                    // Set minimum widths
                    const totalWidth = initialChatWidth + initialLogsWidth;
                    const minWidth = totalWidth * 0.2; // 20% minimum
                    
                    if (newChatWidth < minWidth || newLogsWidth < minWidth) return;
                    
                    chatPanel.style.width = newChatWidth + 'px';
                    logsPanel.style.width = newLogsWidth + 'px';
                });
                
                document.addEventListener('mouseup', function() {
                    if (isResizing) {
                        isResizing = false;
                        document.body.style.cursor = '';
                        document.body.style.userSelect = '';
                    }
                });
            }
        });
        
        // Initialize all components when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            initChatTabs();
            initAdditionalParams();
        });
        
        // Initialize chat tabs immediately since the DOM is already loaded
        initChatTabs();
        initAdditionalParams();
        
        // Initialize Additional Parameters toggle
        function initAdditionalParams() {
            const toggleParamsBtn = document.getElementById('toggleParamsBtn');
            const paramsSection = document.getElementById('paramsSection');
            const paramsIcon = document.getElementById('paramsIcon');
            const paramsText = document.getElementById('paramsText');
            const saveParamsBtn = document.getElementById('saveParamsBtn');
            
            if (!toggleParamsBtn || !paramsSection || !paramsIcon || !paramsText || !saveParamsBtn) return;
            
            let paramsVisible = false;
            
            toggleParamsBtn.addEventListener('click', () => {
                paramsVisible = !paramsVisible;
                
                if (paramsVisible) {
                    paramsSection.style.maxHeight = paramsSection.scrollHeight + 'px';
                    paramsIcon.classList.remove('fa-chevron-down');
                    paramsIcon.classList.add('fa-chevron-up');
                    paramsText.textContent = 'Hide Additional Parameters';
                } else {
                    paramsSection.style.maxHeight = '0';
                    paramsIcon.classList.remove('fa-chevron-up');
                    paramsIcon.classList.add('fa-chevron-down');
                    paramsText.textContent = 'Show Additional Parameters';
                }
            });
            
            saveParamsBtn.addEventListener('click', () => {
                const targetUrl = document.getElementById('targetUrl').value;
                const scanType = document.getElementById('scanType').value;
                const apiKey = document.getElementById('apiKey').value;
                const timeout = document.getElementById('timeout').value;
                
                // In a real app, you would save these parameters
                console.log('Saved parameters:', { targetUrl, scanType, apiKey, timeout });
                
                // Hide the parameters section
                paramsVisible = false;
                paramsSection.style.maxHeight = '0';
                paramsIcon.classList.remove('fa-chevron-up');
                paramsIcon.classList.add('fa-chevron-down');
                paramsText.textContent = 'Show Additional Parameters';
                
                showNotification('Parameters saved successfully', 'success');
            });
        }
    </script>
</body>
</html>