#!/usr/bin/env python3
"""
HexStrike AI - Vulnerability Correlator & CVE Intelligence Manager
Phase 7: Correlation & Prioritization

This tool correlates security findings, computes CVSS scores, and identifies attack chains.
"""

import json
import yaml
import csv
import requests
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
import hashlib
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vulnerability_correlation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class VulnerabilityFinding:
    """Enhanced vulnerability finding with correlation data"""
    id: str
    severity: str
    title: str
    description: str
    endpoint: str
    method: str
    cwe: str
    cvss_score: float
    evidence: str
    reproduction_steps: List[str]
    remediation: str
    timestamp: str
    # Correlation fields
    attack_vector: str = ""
    business_impact: str = ""
    exploitability: str = ""
    correlation_group: str = ""
    related_cves: List[str] = None
    attack_chain_position: int = 0
    
    def __post_init__(self):
        if self.related_cves is None:
            self.related_cves = []

@dataclass
class AttackChain:
    """Represents a potential attack chain"""
    id: str
    name: str
    description: str
    risk_level: str
    nodes: List[str]  # Vulnerability IDs in chain
    blast_radius: str
    likelihood: str
    business_impact: str
    compensating_controls: List[str]
    exploitation_steps: List[str]

class CVEIntelligenceManager:
    """Manages CVE intelligence and scoring"""
    
    def __init__(self):
        self.cve_cache = {}
        self.cwe_mappings = {
            'CWE-79': {'base_score': 6.1, 'category': 'Injection', 'common_cves': ['CVE-2021-44228', 'CVE-2020-1472']},
            'CWE-89': {'base_score': 9.8, 'category': 'Injection', 'common_cves': ['CVE-2019-0708', 'CVE-2021-34527']},
            'CWE-287': {'base_score': 7.5, 'category': 'Authentication', 'common_cves': ['CVE-2020-1472', 'CVE-2021-44228']},
            'CWE-693': {'base_score': 4.3, 'category': 'Configuration', 'common_cves': ['CVE-2021-26855']},
            'CWE-770': {'base_score': 5.3, 'category': 'Resource Management', 'common_cves': ['CVE-2021-44228']},
            'CWE-209': {'base_score': 3.1, 'category': 'Information Disclosure', 'common_cves': ['CVE-2021-26855']},
            'CWE-22': {'base_score': 7.5, 'category': 'Path Traversal', 'common_cves': ['CVE-2021-44228']},
            'CWE-352': {'base_score': 6.5, 'category': 'CSRF', 'common_cves': ['CVE-2020-1472']}
        }
    
    def get_cve_intelligence(self, cwe: str) -> Dict[str, Any]:
        """Get CVE intelligence for a given CWE"""
        if cwe in self.cwe_mappings:
            return self.cwe_mappings[cwe]
        return {'base_score': 5.0, 'category': 'Unknown', 'common_cves': []}
    
    def calculate_enhanced_cvss(self, finding: VulnerabilityFinding, context: Dict[str, Any]) -> float:
        """Calculate enhanced CVSS score with business context"""
        base_score = finding.cvss_score
        
        # Business impact multipliers
        impact_multipliers = {
            'critical_system': 1.5,
            'user_data': 1.3,
            'financial': 1.4,
            'public_facing': 1.2,
            'internal_only': 0.9
        }
        
        # Apply business context
        multiplier = 1.0
        if context.get('system_criticality') in impact_multipliers:
            multiplier *= impact_multipliers[context['system_criticality']]
        
        if context.get('data_sensitivity') == 'high':
            multiplier *= 1.2
        
        if context.get('internet_facing', False):
            multiplier *= 1.3
        
        # Ensure score doesn't exceed 10.0
        enhanced_score = min(base_score * multiplier, 10.0)
        
        return round(enhanced_score, 1)

class VulnerabilityCorrelator:
    """Main vulnerability correlation engine"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.findings: List[VulnerabilityFinding] = []
        self.attack_chains: List[AttackChain] = []
        self.cve_manager = CVEIntelligenceManager()
        self.correlation_rules = self._load_correlation_rules()
    
    def _load_correlation_rules(self) -> Dict[str, Any]:
        """Load vulnerability correlation rules"""
        return {
            'injection_chain': {
                'cwes': ['CWE-79', 'CWE-89', 'CWE-22'],
                'description': 'Injection vulnerabilities can be chained for privilege escalation',
                'risk_multiplier': 1.5
            },
            'auth_bypass_chain': {
                'cwes': ['CWE-287', 'CWE-352', 'CWE-770'],
                'description': 'Authentication bypass combined with other issues',
                'risk_multiplier': 1.8
            },
            'info_disclosure_chain': {
                'cwes': ['CWE-209', 'CWE-693'],
                'description': 'Information disclosure can aid other attacks',
                'risk_multiplier': 1.2
            }
        }
    
    def load_findings_from_file(self, filename: str):
        """Load findings from JSON report file"""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
            
            for finding_data in data.get('findings', []):
                finding = VulnerabilityFinding(**finding_data)
                # Enhance with CVE intelligence
                cve_intel = self.cve_manager.get_cve_intelligence(finding.cwe)
                finding.related_cves = cve_intel.get('common_cves', [])
                finding.attack_vector = self._determine_attack_vector(finding)
                finding.business_impact = self._assess_business_impact(finding)
                finding.exploitability = self._assess_exploitability(finding)
                
                self.findings.append(finding)
            
            logger.info(f"Loaded {len(self.findings)} findings from {filename}")
            
        except Exception as e:
            logger.error(f"Error loading findings from {filename}: {str(e)}")
    
    def _determine_attack_vector(self, finding: VulnerabilityFinding) -> str:
        """Determine attack vector based on finding characteristics"""
        if 'auth' in finding.endpoint.lower():
            return 'Network'
        elif finding.method == 'GET':
            return 'Network'
        elif 'XSS' in finding.title or 'injection' in finding.title.lower():
            return 'Network'
        else:
            return 'Local'
    
    def _assess_business_impact(self, finding: VulnerabilityFinding) -> str:
        """Assess business impact of finding"""
        high_impact_indicators = ['auth', 'login', 'admin', 'user', 'data']
        medium_impact_indicators = ['api', 'endpoint', 'service']
        
        finding_text = (finding.title + finding.description + finding.endpoint).lower()
        
        if any(indicator in finding_text for indicator in high_impact_indicators):
            return 'High'
        elif any(indicator in finding_text for indicator in medium_impact_indicators):
            return 'Medium'
        else:
            return 'Low'
    
    def _assess_exploitability(self, finding: VulnerabilityFinding) -> str:
        """Assess exploitability of finding"""
        if finding.cvss_score >= 7.0:
            return 'High'
        elif finding.cvss_score >= 4.0:
            return 'Medium'
        else:
            return 'Low'
    
    def deduplicate_findings(self):
        """Remove duplicate findings"""
        unique_findings = []
        seen_hashes = set()
        
        for finding in self.findings:
            # Create hash based on endpoint, method, and CWE
            finding_hash = hashlib.md5(
                f"{finding.endpoint}{finding.method}{finding.cwe}".encode()
            ).hexdigest()
            
            if finding_hash not in seen_hashes:
                unique_findings.append(finding)
                seen_hashes.add(finding_hash)
            else:
                logger.info(f"Deduplicated finding: {finding.title} on {finding.endpoint}")
        
        original_count = len(self.findings)
        self.findings = unique_findings
        logger.info(f"Deduplicated {original_count - len(self.findings)} findings")
    
    def correlate_findings(self):
        """Correlate findings and identify attack chains"""
        logger.info("Correlating findings and identifying attack chains...")
        
        # Group findings by correlation rules
        correlation_groups = defaultdict(list)
        
        for finding in self.findings:
            for rule_name, rule in self.correlation_rules.items():
                if finding.cwe in rule['cwes']:
                    correlation_groups[rule_name].append(finding)
                    finding.correlation_group = rule_name
        
        # Create attack chains from correlated findings
        chain_id = 1
        for group_name, group_findings in correlation_groups.items():
            if len(group_findings) > 1:
                rule = self.correlation_rules[group_name]
                
                attack_chain = AttackChain(
                    id=f"CHAIN-{chain_id:03d}",
                    name=f"{group_name.replace('_', ' ').title()}",
                    description=rule['description'],
                    risk_level=self._calculate_chain_risk(group_findings, rule),
                    nodes=[f.id for f in group_findings],
                    blast_radius=self._assess_blast_radius(group_findings),
                    likelihood=self._assess_chain_likelihood(group_findings),
                    business_impact=self._assess_chain_business_impact(group_findings),
                    compensating_controls=self._identify_compensating_controls(group_findings),
                    exploitation_steps=self._generate_exploitation_steps(group_findings)
                )
                
                self.attack_chains.append(attack_chain)
                chain_id += 1
        
        logger.info(f"Identified {len(self.attack_chains)} attack chains")
    
    def _calculate_chain_risk(self, findings: List[VulnerabilityFinding], rule: Dict[str, Any]) -> str:
        """Calculate risk level for attack chain"""
        max_cvss = max(f.cvss_score for f in findings)
        avg_cvss = sum(f.cvss_score for f in findings) / len(findings)
        
        # Apply rule multiplier
        enhanced_score = avg_cvss * rule.get('risk_multiplier', 1.0)
        
        if enhanced_score >= 8.0 or max_cvss >= 9.0:
            return 'Critical'
        elif enhanced_score >= 6.0 or max_cvss >= 7.0:
            return 'High'
        elif enhanced_score >= 4.0:
            return 'Medium'
        else:
            return 'Low'
    
    def _assess_blast_radius(self, findings: List[VulnerabilityFinding]) -> str:
        """Assess blast radius of attack chain"""
        endpoints = set(f.endpoint for f in findings)
        
        if len(endpoints) >= 3:
            return 'High - Multiple systems affected'
        elif len(endpoints) == 2:
            return 'Medium - Limited system spread'
        else:
            return 'Low - Single system'
    
    def _assess_chain_likelihood(self, findings: List[VulnerabilityFinding]) -> str:
        """Assess likelihood of successful attack chain exploitation"""
        exploitability_scores = [f.exploitability for f in findings]
        
        if all(e == 'High' for e in exploitability_scores):
            return 'High'
        elif any(e == 'High' for e in exploitability_scores):
            return 'Medium'
        else:
            return 'Low'
    
    def _assess_chain_business_impact(self, findings: List[VulnerabilityFinding]) -> str:
        """Assess business impact of attack chain"""
        impact_scores = [f.business_impact for f in findings]
        
        if any(i == 'High' for i in impact_scores):
            return 'High'
        elif any(i == 'Medium' for i in impact_scores):
            return 'Medium'
        else:
            return 'Low'
    
    def _identify_compensating_controls(self, findings: List[VulnerabilityFinding]) -> List[str]:
        """Identify compensating controls for attack chain"""
        controls = []
        
        # Check for existing security measures
        for finding in findings:
            if 'auth' in finding.endpoint.lower():
                controls.append('Authentication required')
            if 'rate' in finding.title.lower():
                controls.append('Rate limiting needed')
            if 'header' in finding.title.lower():
                controls.append('Security headers missing')
        
        return list(set(controls))  # Remove duplicates
    
    def _generate_exploitation_steps(self, findings: List[VulnerabilityFinding]) -> List[str]:
        """Generate exploitation steps for attack chain"""
        steps = []
        
        # Sort findings by typical attack progression
        sorted_findings = sorted(findings, key=lambda f: (
            0 if 'recon' in f.title.lower() else
            1 if 'auth' in f.endpoint.lower() else
            2 if 'injection' in f.title.lower() else
            3
        ))
        
        for i, finding in enumerate(sorted_findings, 1):
            steps.append(f"Step {i}: Exploit {finding.title} on {finding.endpoint}")
        
        return steps
    
    def prioritize_findings(self):
        """Prioritize findings based on enhanced scoring"""
        logger.info("Prioritizing findings...")
        
        # Business context for enhanced scoring
        business_context = {
            'system_criticality': 'critical_system',
            'data_sensitivity': 'high',
            'internet_facing': True
        }
        
        # Calculate enhanced CVSS scores
        for finding in self.findings:
            enhanced_score = self.cve_manager.calculate_enhanced_cvss(finding, business_context)
            finding.cvss_score = enhanced_score
            
            # Update severity based on enhanced score
            if enhanced_score >= 9.0:
                finding.severity = 'Critical'
            elif enhanced_score >= 7.0:
                finding.severity = 'High'
            elif enhanced_score >= 4.0:
                finding.severity = 'Medium'
            else:
                finding.severity = 'Low'
        
        # Sort by CVSS score (descending)
        self.findings.sort(key=lambda f: f.cvss_score, reverse=True)
        
        logger.info("Findings prioritized by enhanced CVSS scores")
    
    def generate_findings_yaml(self) -> str:
        """Generate findings in YAML format"""
        findings_data = {
            'assessment_info': {
                'session_id': self.session_id,
                'timestamp': datetime.now().isoformat(),
                'total_findings': len(self.findings),
                'total_chains': len(self.attack_chains)
            },
            'findings': [asdict(f) for f in self.findings],
            'attack_chains': [asdict(c) for c in self.attack_chains]
        }
        
        return yaml.dump(findings_data, default_flow_style=False, sort_keys=False)
    
    def generate_risk_table_csv(self) -> str:
        """Generate risk table in CSV format"""
        csv_data = []
        csv_data.append(['ID', 'Title', 'Severity', 'CVSS', 'Endpoint', 'CWE', 'Business Impact', 'Exploitability', 'Remediation Priority'])
        
        for finding in self.findings:
            priority = 'P1' if finding.cvss_score >= 7.0 else 'P2' if finding.cvss_score >= 4.0 else 'P3'
            csv_data.append([
                finding.id,
                finding.title,
                finding.severity,
                finding.cvss_score,
                finding.endpoint,
                finding.cwe,
                finding.business_impact,
                finding.exploitability,
                priority
            ])
        
        return csv_data
    
    def generate_chains_markdown(self) -> str:
        """Generate attack chains in Markdown format"""
        md_content = ["# Attack Chains Analysis\n"]
        md_content.append(f"**Assessment Session:** {self.session_id}\n")
        md_content.append(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if not self.attack_chains:
            md_content.append("No attack chains identified.\n")
            return ''.join(md_content)
        
        for chain in self.attack_chains:
            md_content.append(f"## {chain.name} ({chain.id})\n\n")
            md_content.append(f"**Risk Level:** {chain.risk_level}\n")
            md_content.append(f"**Description:** {chain.description}\n")
            md_content.append(f"**Blast Radius:** {chain.blast_radius}\n")
            md_content.append(f"**Likelihood:** {chain.likelihood}\n")
            md_content.append(f"**Business Impact:** {chain.business_impact}\n\n")
            
            md_content.append("### Exploitation Steps\n")
            for step in chain.exploitation_steps:
                md_content.append(f"- {step}\n")
            
            md_content.append("\n### Compensating Controls\n")
            for control in chain.compensating_controls:
                md_content.append(f"- {control}\n")
            
            md_content.append("\n---\n\n")
        
        return ''.join(md_content)
    
    def run_correlation_analysis(self, input_files: List[str]):
        """Run complete correlation analysis"""
        logger.info(f"Starting vulnerability correlation analysis for session {self.session_id}")
        
        # Load findings from all input files
        for filename in input_files:
            self.load_findings_from_file(filename)
        
        if not self.findings:
            logger.warning("No findings loaded for correlation")
            return
        
        # Process findings
        self.deduplicate_findings()
        self.correlate_findings()
        self.prioritize_findings()
        
        # Generate outputs
        findings_yaml = self.generate_findings_yaml()
        chains_md = self.generate_chains_markdown()
        risk_csv = self.generate_risk_table_csv()
        
        # Save outputs
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save YAML
        yaml_filename = f"findings_{self.session_id}_{timestamp}.yaml"
        with open(yaml_filename, 'w') as f:
            f.write(findings_yaml)
        
        # Save Markdown
        md_filename = f"chains_{self.session_id}_{timestamp}.md"
        with open(md_filename, 'w') as f:
            f.write(chains_md)
        
        # Save CSV
        csv_filename = f"risk-table_{self.session_id}_{timestamp}.csv"
        with open(csv_filename, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(risk_csv)
        
        logger.info(f"Correlation analysis complete. Generated:")
        logger.info(f"  - {yaml_filename}")
        logger.info(f"  - {md_filename}")
        logger.info(f"  - {csv_filename}")
        
        # Summary
        severity_counts = {}
        for finding in self.findings:
            severity_counts[finding.severity] = severity_counts.get(finding.severity, 0) + 1
        
        logger.info(f"Summary: {len(self.findings)} findings, {len(self.attack_chains)} attack chains")
        logger.info(f"Severity distribution: {severity_counts}")
        
        return {
            'findings_file': yaml_filename,
            'chains_file': md_filename,
            'risk_table_file': csv_filename,
            'summary': {
                'total_findings': len(self.findings),
                'total_chains': len(self.attack_chains),
                'severity_counts': severity_counts
            }
        }

def main():
    """Main function"""
    session_id = "hexstrike_assessment_20250912_220748"
    
    # Input files from previous phases
    input_files = [
        "api_security_report_hexstrike_assessment_20250912_220748.json"
    ]
    
    correlator = VulnerabilityCorrelator(session_id)
    results = correlator.run_correlation_analysis(input_files)
    
    return results

if __name__ == "__main__":
    main()