name: System Monitoring & Health Checks

on:
  schedule:
    # Run health checks every 15 minutes
    - cron: '*/15 * * * *'
    # Run comprehensive monitoring daily at 2 AM UTC
    - cron: '0 2 * * *'
    # Run performance tests weekly on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'
  
  workflow_dispatch:
    inputs:
      check_type:
        description: 'Type of monitoring check to run'
        required: true
        default: 'health'
        type: choice
        options:
          - health
          - performance
          - security
          - comprehensive
      environment:
        description: 'Environment to monitor'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production
          - all

  # Trigger on deployment completion
  workflow_run:
    workflows: ["Production Deployment"]
    types:
      - completed

env:
  MONITORING_TIMEOUT: 300
  ALERT_THRESHOLD_RESPONSE_TIME: 2000
  ALERT_THRESHOLD_ERROR_RATE: 5

jobs:
  # Basic health checks
  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    if: github.event.schedule == '*/15 * * * *' || github.event.inputs.check_type == 'health' || github.event.inputs.check_type == 'comprehensive'
    strategy:
      matrix:
        environment: [staging, production]
        exclude:
          - environment: ${{ github.event.inputs.environment != 'all' && github.event.inputs.environment != matrix.environment && 'staging' }}
          - environment: ${{ github.event.inputs.environment != 'all' && github.event.inputs.environment != matrix.environment && 'production' }}
    
    steps:
      - name: Checkout monitoring scripts
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            monitoring/
            scripts/health-check.py

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install monitoring dependencies
        run: |
          pip install requests psutil prometheus-client

      - name: Set environment URLs
        id: urls
        run: |
          if [ "${{ matrix.environment }}" = "production" ]; then
            echo "base_url=https://hexstrike.ai" >> $GITHUB_OUTPUT
            echo "api_url=https://api.hexstrike.ai" >> $GITHUB_OUTPUT
          else
            echo "base_url=https://staging.hexstrike.ai" >> $GITHUB_OUTPUT
            echo "api_url=https://staging-api.hexstrike.ai" >> $GITHUB_OUTPUT
          fi

      - name: Check application health
        id: app_health
        run: |
          echo "Checking application health for ${{ matrix.environment }}..."
          
          # Check main application
          response=$(curl -s -o /dev/null -w "%{http_code},%{time_total}" \
            --max-time ${{ env.MONITORING_TIMEOUT }} \
            "${{ steps.urls.outputs.base_url }}/health")
          
          http_code=$(echo $response | cut -d',' -f1)
          response_time=$(echo $response | cut -d',' -f2)
          response_time_ms=$(echo "$response_time * 1000" | bc)
          
          echo "HTTP Code: $http_code"
          echo "Response Time: ${response_time_ms}ms"
          
          if [ "$http_code" != "200" ]; then
            echo "❌ Health check failed with HTTP $http_code"
            echo "health_status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          if (( $(echo "$response_time_ms > ${{ env.ALERT_THRESHOLD_RESPONSE_TIME }}" | bc -l) )); then
            echo "⚠️ Slow response time: ${response_time_ms}ms"
            echo "health_status=slow" >> $GITHUB_OUTPUT
          else
            echo "✅ Health check passed"
            echo "health_status=healthy" >> $GITHUB_OUTPUT
          fi
          
          echo "response_time=$response_time_ms" >> $GITHUB_OUTPUT

      - name: Check API endpoints
        id: api_health
        run: |
          echo "Checking API endpoints for ${{ matrix.environment }}..."
          
          endpoints=(
            "/api/v1/health"
            "/api/v1/status"
            "/api/v1/metrics"
          )
          
          failed_endpoints=""
          total_endpoints=${#endpoints[@]}
          failed_count=0
          
          for endpoint in "${endpoints[@]}"; do
            url="${{ steps.urls.outputs.api_url }}$endpoint"
            echo "Testing: $url"
            
            response=$(curl -s -o /dev/null -w "%{http_code}" \
              --max-time 30 "$url")
            
            if [ "$response" != "200" ]; then
              echo "❌ $endpoint failed with HTTP $response"
              failed_endpoints="$failed_endpoints $endpoint"
              ((failed_count++))
            else
              echo "✅ $endpoint OK"
            fi
          done
          
          error_rate=$(echo "scale=2; $failed_count * 100 / $total_endpoints" | bc)
          echo "API Error Rate: $error_rate%"
          
          if (( $(echo "$error_rate > ${{ env.ALERT_THRESHOLD_ERROR_RATE }}" | bc -l) )); then
            echo "❌ API error rate too high: $error_rate%"
            echo "api_status=failed" >> $GITHUB_OUTPUT
            echo "failed_endpoints=$failed_endpoints" >> $GITHUB_OUTPUT
            exit 1
          else
            echo "✅ API health check passed"
            echo "api_status=healthy" >> $GITHUB_OUTPUT
          fi

      - name: Check database connectivity
        id: db_health
        run: |
          echo "Checking database connectivity for ${{ matrix.environment }}..."
          
          # Create a simple database health check script
          cat > db_check.py << 'EOF'
          import os
          import psycopg2
          import sys
          from urllib.parse import urlparse
          
          def check_database():
              try:
                  # Parse database URL from environment
                  db_url = os.getenv('DATABASE_URL')
                  if not db_url:
                      print("❌ DATABASE_URL not set")
                      return False
                  
                  # Connect to database
                  conn = psycopg2.connect(db_url)
                  cursor = conn.cursor()
                  
                  # Simple query to test connectivity
                  cursor.execute("SELECT 1")
                  result = cursor.fetchone()
                  
                  if result[0] == 1:
                      print("✅ Database connectivity OK")
                      
                      # Check database size and connections
                      cursor.execute("""
                          SELECT 
                              pg_size_pretty(pg_database_size(current_database())) as db_size,
                              (SELECT count(*) FROM pg_stat_activity) as active_connections
                      """)
                      
                      db_info = cursor.fetchone()
                      print(f"Database size: {db_info[0]}")
                      print(f"Active connections: {db_info[1]}")
                      
                      cursor.close()
                      conn.close()
                      return True
                  else:
                      print("❌ Database query failed")
                      return False
                      
              except Exception as e:
                  print(f"❌ Database connection failed: {e}")
                  return False
          
          if __name__ == "__main__":
              success = check_database()
              sys.exit(0 if success else 1)
          EOF
          
          # Set database URL based on environment
          if [ "${{ matrix.environment }}" = "production" ]; then
            export DATABASE_URL="${{ secrets.PROD_DATABASE_URL }}"
          else
            export DATABASE_URL="${{ secrets.STAGING_DATABASE_URL }}"
          fi
          
          python db_check.py
          echo "db_status=healthy" >> $GITHUB_OUTPUT

      - name: Check Redis connectivity
        id: redis_health
        run: |
          echo "Checking Redis connectivity for ${{ matrix.environment }}..."
          
          # Create Redis health check script
          cat > redis_check.py << 'EOF'
          import redis
          import os
          import sys
          
          def check_redis():
              try:
                  redis_url = os.getenv('REDIS_URL')
                  if not redis_url:
                      print("❌ REDIS_URL not set")
                      return False
                  
                  r = redis.from_url(redis_url)
                  
                  # Test basic operations
                  r.ping()
                  print("✅ Redis connectivity OK")
                  
                  # Get Redis info
                  info = r.info()
                  print(f"Redis version: {info['redis_version']}")
                  print(f"Connected clients: {info['connected_clients']}")
                  print(f"Used memory: {info['used_memory_human']}")
                  
                  return True
                  
              except Exception as e:
                  print(f"❌ Redis connection failed: {e}")
                  return False
          
          if __name__ == "__main__":
              success = check_redis()
              sys.exit(0 if success else 1)
          EOF
          
          pip install redis
          
          # Set Redis URL based on environment
          if [ "${{ matrix.environment }}" = "production" ]; then
            export REDIS_URL="${{ secrets.PROD_REDIS_URL }}"
          else
            export REDIS_URL="${{ secrets.STAGING_REDIS_URL }}"
          fi
          
          python redis_check.py
          echo "redis_status=healthy" >> $GITHUB_OUTPUT

      - name: Generate health report
        run: |
          echo "# Health Check Report - ${{ matrix.environment }}" > health-report-${{ matrix.environment }}.md
          echo "" >> health-report-${{ matrix.environment }}.md
          echo "**Timestamp:** $(date -u)" >> health-report-${{ matrix.environment }}.md
          echo "**Environment:** ${{ matrix.environment }}" >> health-report-${{ matrix.environment }}.md
          echo "" >> health-report-${{ matrix.environment }}.md
          echo "## Results" >> health-report-${{ matrix.environment }}.md
          echo "" >> health-report-${{ matrix.environment }}.md
          echo "- **Application:** ${{ steps.app_health.outputs.health_status }}" >> health-report-${{ matrix.environment }}.md
          echo "- **API:** ${{ steps.api_health.outputs.api_status }}" >> health-report-${{ matrix.environment }}.md
          echo "- **Database:** ${{ steps.db_health.outputs.db_status }}" >> health-report-${{ matrix.environment }}.md
          echo "- **Redis:** ${{ steps.redis_health.outputs.redis_status }}" >> health-report-${{ matrix.environment }}.md
          echo "" >> health-report-${{ matrix.environment }}.md
          echo "## Metrics" >> health-report-${{ matrix.environment }}.md
          echo "" >> health-report-${{ matrix.environment }}.md
          echo "- **Response Time:** ${{ steps.app_health.outputs.response_time }}ms" >> health-report-${{ matrix.environment }}.md

      - name: Upload health report
        uses: actions/upload-artifact@v3
        with:
          name: health-report-${{ matrix.environment }}
          path: health-report-${{ matrix.environment }}.md

  # Performance monitoring
  performance-check:
    name: Performance Check
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 3 * * 0' || github.event.inputs.check_type == 'performance' || github.event.inputs.check_type == 'comprehensive'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Run performance tests
        run: |
          echo "Running performance tests..."
          
          # Create performance test script
          cat > performance-test.js << 'EOF'
          import http from 'k6/http';
          import { check, sleep } from 'k6';
          import { Rate, Trend } from 'k6/metrics';
          
          export let errorRate = new Rate('errors');
          export let responseTime = new Trend('response_time');
          
          export let options = {
            stages: [
              { duration: '2m', target: 10 },  // Ramp up
              { duration: '5m', target: 50 },  // Stay at 50 users
              { duration: '2m', target: 100 }, // Ramp to 100 users
              { duration: '5m', target: 100 }, // Stay at 100 users
              { duration: '2m', target: 0 },   // Ramp down
            ],
            thresholds: {
              http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
              http_req_failed: ['rate<0.05'],    // Error rate under 5%
            },
          };
          
          export default function() {
            let baseUrl = __ENV.BASE_URL || 'https://hexstrike.ai';
            
            // Test main page
            let response = http.get(`${baseUrl}/`);
            check(response, {
              'status is 200': (r) => r.status === 200,
              'response time < 2000ms': (r) => r.timings.duration < 2000,
            });
            
            errorRate.add(response.status !== 200);
            responseTime.add(response.timings.duration);
            
            sleep(1);
            
            // Test API endpoint
            response = http.get(`${baseUrl}/api/v1/health`);
            check(response, {
              'API status is 200': (r) => r.status === 200,
              'API response time < 1000ms': (r) => r.timings.duration < 1000,
            });
            
            errorRate.add(response.status !== 200);
            responseTime.add(response.timings.duration);
            
            sleep(1);
          }
          EOF
          
          # Run tests against staging and production
          echo "Testing staging environment..."
          BASE_URL=https://staging.hexstrike.ai k6 run --out json=staging-results.json performance-test.js
          
          echo "Testing production environment..."
          BASE_URL=https://hexstrike.ai k6 run --out json=production-results.json performance-test.js

      - name: Analyze performance results
        run: |
          echo "Analyzing performance results..."
          
          # Create analysis script
          cat > analyze-results.py << 'EOF'
          import json
          import sys
          
          def analyze_results(filename, environment):
              with open(filename, 'r') as f:
                  results = [json.loads(line) for line in f if line.strip()]
              
              # Filter for HTTP request metrics
              http_reqs = [r for r in results if r.get('type') == 'Point' and r.get('metric') == 'http_req_duration']
              
              if not http_reqs:
                  print(f"No HTTP request data found for {environment}")
                  return
              
              # Calculate statistics
              durations = [r['data']['value'] for r in http_reqs]
              avg_duration = sum(durations) / len(durations)
              max_duration = max(durations)
              min_duration = min(durations)
              
              # Calculate percentiles
              sorted_durations = sorted(durations)
              p95_index = int(0.95 * len(sorted_durations))
              p99_index = int(0.99 * len(sorted_durations))
              
              p95_duration = sorted_durations[p95_index]
              p99_duration = sorted_durations[p99_index]
              
              print(f"\n=== {environment.upper()} PERFORMANCE RESULTS ===")
              print(f"Total requests: {len(durations)}")
              print(f"Average response time: {avg_duration:.2f}ms")
              print(f"Min response time: {min_duration:.2f}ms")
              print(f"Max response time: {max_duration:.2f}ms")
              print(f"95th percentile: {p95_duration:.2f}ms")
              print(f"99th percentile: {p99_duration:.2f}ms")
              
              # Check thresholds
              if p95_duration > 2000:
                  print(f"⚠️ WARNING: 95th percentile ({p95_duration:.2f}ms) exceeds 2000ms threshold")
                  return False
              else:
                  print(f"✅ Performance within acceptable limits")
                  return True
          
          if __name__ == "__main__":
              staging_ok = analyze_results('staging-results.json', 'staging')
              production_ok = analyze_results('production-results.json', 'production')
              
              if not (staging_ok and production_ok):
                  sys.exit(1)
          EOF
          
          python analyze-results.py

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: |
            staging-results.json
            production-results.json

  # Security monitoring
  security-check:
    name: Security Check
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 2 * * *' || github.event.inputs.check_type == 'security' || github.event.inputs.check_type == 'comprehensive'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: SSL/TLS Certificate Check
        run: |
          echo "Checking SSL certificates..."
          
          domains=("hexstrike.ai" "staging.hexstrike.ai" "api.hexstrike.ai")
          
          for domain in "${domains[@]}"; do
            echo "Checking $domain..."
            
            # Get certificate expiry
            expiry=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | \
                    openssl x509 -noout -enddate | cut -d= -f2)
            
            if [ -n "$expiry" ]; then
              expiry_epoch=$(date -d "$expiry" +%s)
              current_epoch=$(date +%s)
              days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
              
              echo "Certificate expires: $expiry ($days_until_expiry days)"
              
              if [ $days_until_expiry -lt 30 ]; then
                echo "⚠️ WARNING: Certificate for $domain expires in $days_until_expiry days"
              elif [ $days_until_expiry -lt 7 ]; then
                echo "❌ CRITICAL: Certificate for $domain expires in $days_until_expiry days"
                exit 1
              else
                echo "✅ Certificate for $domain is valid"
              fi
            else
              echo "❌ Could not retrieve certificate for $domain"
            fi
          done

      - name: Security Headers Check
        run: |
          echo "Checking security headers..."
          
          urls=("https://hexstrike.ai" "https://staging.hexstrike.ai")
          
          for url in "${urls[@]}"; do
            echo "Checking headers for $url..."
            
            headers=$(curl -s -I "$url")
            
            # Check for security headers
            security_headers=(
              "Strict-Transport-Security"
              "X-Content-Type-Options"
              "X-Frame-Options"
              "X-XSS-Protection"
              "Content-Security-Policy"
            )
            
            missing_headers=""
            
            for header in "${security_headers[@]}"; do
              if echo "$headers" | grep -qi "$header"; then
                echo "✅ $header present"
              else
                echo "❌ $header missing"
                missing_headers="$missing_headers $header"
              fi
            done
            
            if [ -n "$missing_headers" ]; then
              echo "⚠️ Missing security headers for $url:$missing_headers"
            fi
          done

      - name: Vulnerability Scan
        run: |
          echo "Running basic vulnerability scan..."
          
          # Install nuclei
          wget -q https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_linux_amd64.zip
          unzip -q nuclei_2.9.15_linux_amd64.zip
          chmod +x nuclei
          
          # Update templates
          ./nuclei -update-templates
          
          # Run scan against staging (safer)
          echo "Scanning staging environment..."
          ./nuclei -u https://staging.hexstrike.ai -t cves/ -t vulnerabilities/ -o nuclei-results.txt
          
          if [ -s nuclei-results.txt ]; then
            echo "⚠️ Vulnerabilities found:"
            cat nuclei-results.txt
          else
            echo "✅ No vulnerabilities detected"
          fi

      - name: Upload security results
        uses: actions/upload-artifact@v3
        with:
          name: security-results
          path: nuclei-results.txt

  # Alert and notification
  alert:
    name: Send Alerts
    runs-on: ubuntu-latest
    needs: [health-check, performance-check, security-check]
    if: always() && (failure() || contains(needs.*.result, 'failure'))
    
    steps:
      - name: Determine alert severity
        id: severity
        run: |
          if [ "${{ needs.health-check.result }}" = "failure" ]; then
            echo "severity=critical" >> $GITHUB_OUTPUT
            echo "message=🚨 CRITICAL: Health checks failed" >> $GITHUB_OUTPUT
          elif [ "${{ needs.performance-check.result }}" = "failure" ]; then
            echo "severity=warning" >> $GITHUB_OUTPUT
            echo "message=⚠️ WARNING: Performance issues detected" >> $GITHUB_OUTPUT
          elif [ "${{ needs.security-check.result }}" = "failure" ]; then
            echo "severity=warning" >> $GITHUB_OUTPUT
            echo "message=🔒 WARNING: Security issues detected" >> $GITHUB_OUTPUT
          else
            echo "severity=info" >> $GITHUB_OUTPUT
            echo "message=ℹ️ INFO: Monitoring completed with issues" >> $GITHUB_OUTPUT
          fi

      - name: Send Slack alert
        run: |
          echo "Sending alert: ${{ steps.severity.outputs.message }}"
          # Add Slack webhook call here
          # curl -X POST -H 'Content-type: application/json' \
          #   --data '{"text":"${{ steps.severity.outputs.message }}\nWorkflow: ${{ github.workflow }}\nRun: ${{ github.run_id }}"}' \
          #   ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Create GitHub issue
        if: steps.severity.outputs.severity == 'critical'
        run: |
          echo "Creating GitHub issue for critical alert..."
          # Add GitHub issue creation here
          # gh issue create --title "Critical Monitoring Alert" \
          #   --body "${{ steps.severity.outputs.message }}\n\nWorkflow run: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
          #   --label "critical,monitoring"

  # Summary report
  summary:
    name: Generate Summary
    runs-on: ubuntu-latest
    needs: [health-check, performance-check, security-check]
    if: always()
    
    steps:
      - name: Generate monitoring summary
        run: |
          echo "# Monitoring Summary Report" > monitoring-summary.md
          echo "" >> monitoring-summary.md
          echo "**Date:** $(date -u)" >> monitoring-summary.md
          echo "**Workflow:** ${{ github.workflow }}" >> monitoring-summary.md
          echo "**Run ID:** ${{ github.run_id }}" >> monitoring-summary.md
          echo "" >> monitoring-summary.md
          echo "## Results" >> monitoring-summary.md
          echo "" >> monitoring-summary.md
          echo "- **Health Check:** ${{ needs.health-check.result }}" >> monitoring-summary.md
          echo "- **Performance Check:** ${{ needs.performance-check.result }}" >> monitoring-summary.md
          echo "- **Security Check:** ${{ needs.security-check.result }}" >> monitoring-summary.md
          echo "" >> monitoring-summary.md
          
          if [ "${{ needs.health-check.result }}" = "success" ] && \
             [ "${{ needs.performance-check.result }}" = "success" ] && \
             [ "${{ needs.security-check.result }}" = "success" ]; then
            echo "## Overall Status: ✅ HEALTHY" >> monitoring-summary.md
          else
            echo "## Overall Status: ❌ ISSUES DETECTED" >> monitoring-summary.md
          fi
          
          echo "" >> monitoring-summary.md
          echo "## Next Steps" >> monitoring-summary.md
          echo "" >> monitoring-summary.md
          
          if [ "${{ needs.health-check.result }}" != "success" ]; then
            echo "- Investigate health check failures" >> monitoring-summary.md
          fi
          
          if [ "${{ needs.performance-check.result }}" != "success" ]; then
            echo "- Review performance metrics and optimize" >> monitoring-summary.md
          fi
          
          if [ "${{ needs.security-check.result }}" != "success" ]; then
            echo "- Address security vulnerabilities" >> monitoring-summary.md
          fi

      - name: Upload summary report
        uses: actions/upload-artifact@v3
        with:
          name: monitoring-summary
          path: monitoring-summary.md