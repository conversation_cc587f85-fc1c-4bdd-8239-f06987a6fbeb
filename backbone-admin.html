<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backbone Checks - Admin Dashboard</title>
    <style>
        /* Modern CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Color Tokens */
            --color-primary: #2563eb;
            --color-primary-hover: #1d4ed8;
            --color-success: #059669;
            --color-warning: #d97706;
            --color-error: #dc2626;
            --color-neutral-50: #f9fafb;
            --color-neutral-100: #f3f4f6;
            --color-neutral-200: #e5e7eb;
            --color-neutral-300: #d1d5db;
            --color-neutral-400: #9ca3af;
            --color-neutral-500: #6b7280;
            --color-neutral-600: #4b5563;
            --color-neutral-700: #374151;
            --color-neutral-800: #1f2937;
            --color-neutral-900: #111827;

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-12: 3rem;

            /* Typography */
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

            /* Border Radius */
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--color-neutral-800);
            background-color: var(--color-neutral-50);
            direction: ltr;
        }

        /* RTL Support */
        [dir="rtl"] {
            direction: rtl;
        }

        /* Page Layout */
        .page {
            min-height: 100vh;
            background: white;
        }

        /* Header Styles */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: white;
            border-bottom: 1px solid var(--color-neutral-200);
            box-shadow: var(--shadow-sm);
            padding: var(--space-6) var(--space-8);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .title {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--color-neutral-900);
            margin-bottom: var(--space-6);
        }

        /* Actions Row */
        .actions-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: var(--space-4);
            align-items: end;
        }

        @media (max-width: 768px) {
            .actions-row {
                grid-template-columns: 1fr;
                gap: var(--space-3);
            }
        }

        /* Input Styles */
        .input-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .input-label {
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--color-neutral-700);
        }

        .input {
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--color-neutral-300);
            border-radius: var(--radius-md);
            font-size: var(--font-size-base);
            transition: all 0.2s ease;
            background: white;
        }

        .input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .input:focus-visible {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }

        /* Button Styles */
        .button {
            padding: var(--space-3) var(--space-6);
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            min-height: 44px;
        }

        .button:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .button:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.3);
        }

        .button:focus-visible {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }

        .button:disabled {
            background: var(--color-neutral-300);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .button-secondary {
            background: var(--color-neutral-100);
            color: var(--color-neutral-700);
            border: 1px solid var(--color-neutral-300);
        }

        .button-secondary:hover {
            background: var(--color-neutral-200);
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-8);
            display: flex;
            flex-direction: column;
            gap: var(--space-8);
        }

        /* Status Cards */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
        }

        .status-card {
            background: white;
            border: 1px solid var(--color-neutral-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            box-shadow: var(--shadow-sm);
            transition: all 0.2s ease;
        }

        .status-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .status-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
        }

        .status-card-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--color-neutral-900);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--color-neutral-300);
            transition: all 0.3s ease;
        }

        .status-indicator.success {
            background: var(--color-success);
            box-shadow: 0 0 0 3px rgb(5 150 105 / 0.2);
        }

        .status-indicator.error {
            background: var(--color-error);
            box-shadow: 0 0 0 3px rgb(220 38 38 / 0.2);
        }

        .status-indicator.loading {
            background: var(--color-warning);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .status-card-content {
            color: var(--color-neutral-600);
            font-size: var(--font-size-sm);
            line-height: 1.5;
        }

        /* JSON Viewer */
        .json-viewer {
            background: white;
            border: 1px solid var(--color-neutral-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .json-viewer-header {
            background: var(--color-neutral-50);
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--color-neutral-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .json-viewer-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--color-neutral-900);
        }

        .json-viewer-content {
            padding: var(--space-6);
            max-height: 500px;
            overflow-y: auto;
        }

        .json-display {
            background: var(--color-neutral-900);
            color: #f8f8f2;
            padding: var(--space-4);
            border-radius: var(--radius-md);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: var(--font-size-sm);
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-all;
            overflow-x: auto;
        }

        /* Button Row */
        .button-row {
            display: flex;
            gap: var(--space-4);
            justify-content: flex-start;
            flex-wrap: wrap;
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Alert Messages */
        .alert {
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-4);
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        .alert-success {
            background: rgb(5 150 105 / 0.1);
            color: var(--color-success);
            border: 1px solid rgb(5 150 105 / 0.2);
        }

        .alert-error {
            background: rgb(220 38 38 / 0.1);
            color: var(--color-error);
            border: 1px solid rgb(220 38 38 / 0.2);
        }

        .alert-warning {
            background: rgb(217 119 6 / 0.1);
            color: var(--color-warning);
            border: 1px solid rgb(217 119 6 / 0.2);
        }

        /* Accessibility Improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            .input, .button, .status-card {
                border-width: 2px;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            :root {
                --color-neutral-50: #111827;
                --color-neutral-100: #1f2937;
                --color-neutral-200: #374151;
                --color-neutral-300: #4b5563;
                --color-neutral-400: #6b7280;
                --color-neutral-500: #9ca3af;
                --color-neutral-600: #d1d5db;
                --color-neutral-700: #e5e7eb;
                --color-neutral-800: #f3f4f6;
                --color-neutral-900: #f9fafb;
            }

            body {
                background-color: var(--color-neutral-50);
                color: var(--color-neutral-800);
            }

            .page {
                background: var(--color-neutral-100);
            }

            .header {
                background: var(--color-neutral-100);
                border-bottom-color: var(--color-neutral-200);
            }

            .status-card, .json-viewer {
                background: var(--color-neutral-100);
                border-color: var(--color-neutral-200);
            }

            .input {
                background: var(--color-neutral-100);
                border-color: var(--color-neutral-300);
                color: var(--color-neutral-800);
            }
        }
    </style>
</head>
<body>
    <div class="page" id="backbone_admin">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">Backbone Checks</h1>
                
                <!-- Actions Row -->
                <div class="actions-row">
                    <div class="input-group">
                        <label for="scrapeUrl" class="input-label">Scrape URL</label>
                        <input 
                            type="url" 
                            id="scrapeUrl" 
                            class="input" 
                            placeholder="https://example.com"
                            value="https://example.com"
                            aria-describedby="scrapeUrl-help"
                        >
                        <div id="scrapeUrl-help" class="sr-only">Enter a URL to test web scraping functionality</div>
                    </div>
                    
                    <div class="input-group">
                        <label for="crawlUrl" class="input-label">Crawl URL</label>
                        <input 
                            type="url" 
                            id="crawlUrl" 
                            class="input" 
                            placeholder="https://example.com"
                            value="https://example.com"
                            aria-describedby="crawlUrl-help"
                        >
                        <div id="crawlUrl-help" class="sr-only">Enter a URL to test web crawling functionality</div>
                    </div>
                    
                    <div class="input-group">
                        <label for="testHost" class="input-label">Test Host</label>
                        <input 
                            type="text" 
                            id="testHost" 
                            class="input" 
                            placeholder="example.com"
                            value="example.com"
                            aria-describedby="testHost-help"
                        >
                        <div id="testHost-help" class="sr-only">Enter a hostname for penetration testing</div>
                    </div>
                    
                    <button 
                        id="run_checks" 
                        class="button"
                        aria-describedby="run-checks-help"
                    >
                        <span class="spinner" id="run-spinner" style="display: none;"></span>
                        <span id="run-text">Run Checks</span>
                    </button>
                    <div id="run-checks-help" class="sr-only">Execute comprehensive backbone system checks</div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Alert Messages -->
            <div id="alert-container"></div>

            <!-- Status Cards -->
            <section class="cards-grid" id="module_cards" aria-label="Module Status Cards">
                <div class="status-card" id="scrape-card">
                    <div class="status-card-header">
                        <h3 class="status-card-title">Scrape</h3>
                        <div class="status-indicator" id="scrape-status" aria-label="Scrape module status"></div>
                    </div>
                    <div class="status-card-content" id="scrape-content">
                        Web scraping functionality test. Validates URL fetching, content parsing, and data extraction capabilities.
                    </div>
                </div>

                <div class="status-card" id="crawl-card">
                    <div class="status-card-header">
                        <h3 class="status-card-title">Crawl</h3>
                        <div class="status-indicator" id="crawl-status" aria-label="Crawl module status"></div>
                    </div>
                    <div class="status-card-content" id="crawl-content">
                        Web crawling functionality test. Validates multi-page navigation, robots.txt compliance, and link discovery.
                    </div>
                </div>

                <div class="status-card" id="pentest-card">
                    <div class="status-card-header">
                        <h3 class="status-card-title">Safe Pentest</h3>
                        <div class="status-indicator" id="pentest-status" aria-label="Safe pentest module status"></div>
                    </div>
                    <div class="status-card-content" id="pentest-content">
                        Safe penetration testing. Validates DNS resolution, TCP connectivity, and HTTP HEAD requests with security boundaries.
                    </div>
                </div>

                <div class="status-card" id="analyze-card">
                    <div class="status-card-header">
                        <h3 class="status-card-title">Analyze</h3>
                        <div class="status-indicator" id="analyze-status" aria-label="Analyze module status"></div>
                    </div>
                    <div class="status-card-content" id="analyze-content">
                        Content analysis functionality test. Validates text processing, entity extraction, and summary generation capabilities.
                    </div>
                </div>
            </section>

            <!-- JSON Viewer -->
            <section class="json-viewer" id="results" aria-label="Test Results">
                <div class="json-viewer-header">
                    <h2 class="json-viewer-title">Latest Result</h2>
                    <button 
                        id="refresh-results" 
                        class="button button-secondary"
                        aria-label="Refresh results display"
                    >
                        Refresh
                    </button>
                </div>
                <div class="json-viewer-content">
                    <div class="json-display" id="json-content">
                        {
  "status": "ready",
  "message": "Click 'Run Checks' to execute comprehensive backbone tests",
  "timestamp": "" + new Date().toISOString() + ""
}
                    </div>
                </div>
            </section>

            <!-- Export Actions -->
            <section class="button-row" aria-label="Export Actions">
                <button 
                    id="export_json" 
                    class="button button-secondary"
                    disabled
                    aria-describedby="export-help"
                >
                    Export JSON
                </button>
                <div id="export-help" class="sr-only">Download the latest test results as a JSON file</div>
                
                <button 
                    id="export_csv" 
                    class="button button-secondary"
                    disabled
                    aria-describedby="export-csv-help"
                >
                    Export CSV
                </button>
                <div id="export-csv-help" class="sr-only">Download the latest test results as a CSV file</div>
                
                <button 
                    id="view_audit" 
                    class="button button-secondary"
                    aria-describedby="audit-help"
                >
                    View Audit Log
                </button>
                <div id="audit-help" class="sr-only">View the security audit log for backbone operations</div>
            </section>
        </main>
    </div>

    <script>
        // Backbone Admin Dashboard JavaScript
        class BackboneAdmin {
            constructor() {
                this.latestResult = null;
                this.isRunning = false;
                this.healthData = null;
                
                this.init();
            }

            init() {
                this.bindEvents();
                this.loadHealthStatus();
                this.setupAccessibility();
            }

            bindEvents() {
                // Run checks button
                document.getElementById('run_checks').addEventListener('click', () => {
                    this.runChecks();
                });

                // Export buttons
                document.getElementById('export_json').addEventListener('click', () => {
                    this.exportJSON();
                });

                document.getElementById('export_csv').addEventListener('click', () => {
                    this.exportCSV();
                });

                // Refresh results
                document.getElementById('refresh-results').addEventListener('click', () => {
                    this.refreshResults();
                });

                // View audit log
                document.getElementById('view_audit').addEventListener('click', () => {
                    this.viewAuditLog();
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'Enter':
                                e.preventDefault();
                                if (!this.isRunning) {
                                    this.runChecks();
                                }
                                break;
                            case 's':
                                e.preventDefault();
                                this.exportJSON();
                                break;
                        }
                    }
                });
            }

            setupAccessibility() {
                // Add ARIA live regions for dynamic updates
                const alertContainer = document.getElementById('alert-container');
                alertContainer.setAttribute('aria-live', 'polite');
                alertContainer.setAttribute('aria-atomic', 'true');

                // Add focus management for status cards
                const statusCards = document.querySelectorAll('.status-card');
                statusCards.forEach(card => {
                    card.setAttribute('tabindex', '0');
                    card.setAttribute('role', 'region');
                });
            }

            async loadHealthStatus() {
                try {
                    const response = await fetch('/api/backbone/health', {
                        headers: {
                            'Authorization': 'Bearer admin_token',
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        this.healthData = await response.json();
                        this.updateHealthDisplay();
                    } else {
                        this.showAlert('Failed to load health status', 'error');
                    }
                } catch (error) {
                    console.error('Health check error:', error);
                    this.showAlert('Health check unavailable', 'warning');
                }
            }

            updateHealthDisplay() {
                if (!this.healthData) return;

                // Update JSON viewer with health data
                this.updateJSONDisplay({
                    health: this.healthData,
                    timestamp: new Date().toISOString()
                });
            }

            async runChecks() {
                if (this.isRunning) return;

                this.isRunning = true;
                this.setLoadingState(true);

                try {
                    // Get input values
                    const targets = {
                        scrapeUrl: document.getElementById('scrapeUrl').value.trim(),
                        crawlUrl: document.getElementById('crawlUrl').value.trim(),
                        testHost: document.getElementById('testHost').value.trim()
                    };

                    // Validate inputs
                    if (!targets.scrapeUrl || !targets.crawlUrl || !targets.testHost) {
                        this.showAlert('Please fill in all required fields', 'error');
                        return;
                    }

                    // Generate idempotency key
                    const idempotencyKey = this.generateIdempotencyKey(targets);

                    // Make API request
                    const response = await fetch('/api/backbone/self-test', {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer admin_token',
                            'Content-Type': 'application/json',
                            'X-Idempotency-Key': idempotencyKey
                        },
                        body: JSON.stringify({ targets })
                    });

                    if (response.ok) {
                        this.latestResult = await response.json();
                        this.updateResults();
                        this.showAlert('Backbone checks completed successfully', 'success');
                    } else {
                        const error = await response.json();
                        this.showAlert(`Checks failed: ${error.error || 'Unknown error'}`, 'error');
                    }

                } catch (error) {
                    console.error('Check execution error:', error);
                    this.showAlert('Failed to execute checks', 'error');
                } finally {
                    this.isRunning = false;
                    this.setLoadingState(false);
                }
            }

            updateResults() {
                if (!this.latestResult) return;

                // Update status cards
                this.updateStatusCard('scrape', this.latestResult.scrape);
                this.updateStatusCard('crawl', this.latestResult.crawl);
                this.updateStatusCard('pentest', this.latestResult.safe_pentest);
                this.updateStatusCard('analyze', this.latestResult.analyze);

                // Update JSON viewer
                this.updateJSONDisplay(this.latestResult);

                // Enable export buttons
                document.getElementById('export_json').disabled = false;
                document.getElementById('export_csv').disabled = false;
            }

            updateStatusCard(module, result) {
                const statusElement = document.getElementById(`${module}-status`);
                const contentElement = document.getElementById(`${module}-content`);

                if (!result) {
                    statusElement.className = 'status-indicator';
                    return;
                }

                // Update status indicator
                statusElement.className = `status-indicator ${result.ok ? 'success' : 'error'}`;
                statusElement.setAttribute('aria-label', `${module} module: ${result.ok ? 'success' : 'failed'}`);

                // Update content
                if (result.ok) {
                    let details = '';
                    switch(module) {
                        case 'scrape':
                            details = `✓ ${result.bytes || 0} bytes scraped, ${result.tableRows || 0} table rows found`;
                            break;
                        case 'crawl':
                            details = `✓ ${result.pages || 0} pages crawled, depth ${result.depthReached || 0}`;
                            break;
                        case 'pentest':
                            const checks = result.summary || {};
                            details = `✓ ${checks.successful_checks || 0}/${checks.total_checks || 0} checks passed`;
                            break;
                        case 'analyze':
                            details = `✓ ${result.summaryLen || 0} char summary, ${result.entities?.length || 0} entities`;
                            break;
                    }
                    contentElement.innerHTML = details;
                } else {
                    contentElement.innerHTML = `✗ ${result.error || 'Check failed'}`;
                }
            }

            updateJSONDisplay(data) {
                const jsonElement = document.getElementById('json-content');
                jsonElement.textContent = JSON.stringify(data, null, 2);
            }

            setLoadingState(loading) {
                const runButton = document.getElementById('run_checks');
                const spinner = document.getElementById('run-spinner');
                const text = document.getElementById('run-text');

                if (loading) {
                    runButton.disabled = true;
                    spinner.style.display = 'inline-block';
                    text.textContent = 'Running...';
                    
                    // Set all status indicators to loading
                    ['scrape', 'crawl', 'pentest', 'analyze'].forEach(module => {
                        const indicator = document.getElementById(`${module}-status`);
                        indicator.className = 'status-indicator loading';
                    });
                } else {
                    runButton.disabled = false;
                    spinner.style.display = 'none';
                    text.textContent = 'Run Checks';
                }
            }

            showAlert(message, type = 'info') {
                const container = document.getElementById('alert-container');
                
                const alert = document.createElement('div');
                alert.className = `alert alert-${type}`;
                alert.textContent = message;
                alert.setAttribute('role', 'alert');

                container.appendChild(alert);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 5000);
            }

            exportJSON() {
                if (!this.latestResult) {
                    this.showAlert('No results to export', 'warning');
                    return;
                }

                const dataStr = JSON.stringify(this.latestResult, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `backbone-checks-${new Date().toISOString().slice(0, 19)}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showAlert('JSON exported successfully', 'success');
            }

            exportCSV() {
                if (!this.latestResult) {
                    this.showAlert('No results to export', 'warning');
                    return;
                }

                // Convert results to CSV format
                const csvData = this.convertToCSV(this.latestResult);
                const blob = new Blob([csvData], { type: 'text/csv' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `backbone-checks-${new Date().toISOString().slice(0, 19)}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showAlert('CSV exported successfully', 'success');
            }

            convertToCSV(data) {
                const headers = ['Module', 'Status', 'Elapsed_MS', 'Details'];
                const rows = [];

                ['scrape', 'crawl', 'safe_pentest', 'analyze'].forEach(module => {
                    const result = data[module];
                    if (result) {
                        rows.push([
                            module,
                            result.ok ? 'SUCCESS' : 'FAILED',
                            result.elapsed_ms || 0,
                            result.error || 'OK'
                        ]);
                    }
                });

                return [headers, ...rows].map(row => 
                    row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
                ).join('\n');
            }

            async viewAuditLog() {
                try {
                    const response = await fetch('/api/backbone/health', {
                        headers: {
                            'Authorization': 'Bearer admin_token'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        const auditData = data.security || {};
                        
                        this.updateJSONDisplay({
                            audit_summary: auditData,
                            timestamp: new Date().toISOString()
                        });
                        
                        this.showAlert('Audit log loaded', 'success');
                    }
                } catch (error) {
                    this.showAlert('Failed to load audit log', 'error');
                }
            }

            refreshResults() {
                if (this.latestResult) {
                    this.updateJSONDisplay(this.latestResult);
                    this.showAlert('Results refreshed', 'success');
                } else {
                    this.loadHealthStatus();
                }
            }

            generateIdempotencyKey(data) {
                const str = JSON.stringify(data, Object.keys(data).sort());
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // Convert to 32-bit integer
                }
                return Math.abs(hash).toString(16);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new BackboneAdmin();
        });
    </script>
</body>
</html>