#!/usr/bin/env python3
"""
Backbone Security Module
Provides RBAC, allowlist validation, rate limiting, and audit logging for admin-only backbone checks.
"""

import os
import time
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from functools import wraps
from collections import defaultdict
from urllib.parse import urlparse

from flask import request, jsonify, g
from werkzeug.exceptions import Forbidden, TooManyRequests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackboneSecurityManager:
    """Centralized security manager for backbone operations"""
    
    def __init__(self):
        self.rate_limits = defaultdict(list)  # {user_id: [timestamp, ...]}
        self.audit_log = []
        self.allowlist_hosts = self._load_allowlist()
        self.admin_users = self._load_admin_users()
        
    def _load_allowlist(self) -> List[str]:
        """Load allowed hosts from environment variable"""
        allowlist_env = os.getenv('ALLOWLIST_HOSTS', 'example.com,httpbin.org,jsonplaceholder.typicode.com')
        hosts = [host.strip().lower() for host in allowlist_env.split(',') if host.strip()]
        logger.info(f"Loaded allowlist: {hosts}")
        return hosts
    
    def _load_admin_users(self) -> List[str]:
        """Load admin user IDs from environment variable"""
        admin_env = os.getenv('ADMIN_USERS', 'admin,hexstrike_admin')
        admins = [user.strip() for user in admin_env.split(',') if user.strip()]
        logger.info(f"Loaded admin users: {len(admins)} users")
        return admins
    
    def is_admin(self, user_id: str) -> bool:
        """Check if user has admin privileges"""
        return user_id in self.admin_users
    
    def validate_host_allowlist(self, url: str) -> bool:
        """Validate URL against allowlist"""
        try:
            parsed = urlparse(url)
            host = parsed.hostname
            if not host:
                return False
            
            host = host.lower()
            
            # Check exact match or subdomain
            for allowed_host in self.allowlist_hosts:
                if host == allowed_host or host.endswith(f'.{allowed_host}'):
                    return True
            
            return False
        except Exception as e:
            logger.error(f"Error validating host allowlist for {url}: {e}")
            return False
    
    def check_rate_limit(self, user_id: str, limit: int = 5, window_minutes: int = 1) -> bool:
        """Check if user is within rate limits (5 requests per minute by default)"""
        now = time.time()
        window_start = now - (window_minutes * 60)
        
        # Clean old entries
        self.rate_limits[user_id] = [
            timestamp for timestamp in self.rate_limits[user_id]
            if timestamp > window_start
        ]
        
        # Check current count
        current_count = len(self.rate_limits[user_id])
        
        if current_count >= limit:
            return False
        
        # Add current request
        self.rate_limits[user_id].append(now)
        return True
    
    def audit_action(self, actor_id: str, action: str, target: str, 
                    result: str, elapsed_ms: int, ip: str = None, user_agent: str = None):
        """Log audit event"""
        audit_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'actorId': actor_id,
            'action': action,
            'target': target,
            'result': result,
            'elapsedMs': elapsed_ms,
            'ip': ip or request.remote_addr if request else 'unknown',
            'userAgent': user_agent or request.headers.get('User-Agent', 'unknown') if request else 'unknown',
            'sessionId': getattr(g, 'session_id', 'unknown')
        }
        
        self.audit_log.append(audit_entry)
        
        # Keep only last 1000 entries to prevent memory issues
        if len(self.audit_log) > 1000:
            self.audit_log = self.audit_log[-1000:]
        
        # Log to file for persistence
        try:
            with open('backbone_audit.log', 'a', encoding='utf-8') as f:
                f.write(json.dumps(audit_entry) + '\n')
        except Exception as e:
            logger.error(f"Failed to write audit log: {e}")
    
    def get_audit_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get audit summary for the last N hours"""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        cutoff_iso = cutoff.isoformat() + 'Z'
        
        recent_entries = [
            entry for entry in self.audit_log
            if entry['timestamp'] >= cutoff_iso
        ]
        
        summary = {
            'totalEvents': len(recent_entries),
            'timeWindow': f'{hours}h',
            'actors': len(set(entry['actorId'] for entry in recent_entries)),
            'actions': {},
            'results': {},
            'avgElapsedMs': 0
        }
        
        if recent_entries:
            # Count actions and results
            for entry in recent_entries:
                action = entry['action']
                result = entry['result']
                
                summary['actions'][action] = summary['actions'].get(action, 0) + 1
                summary['results'][result] = summary['results'].get(result, 0) + 1
            
            # Calculate average elapsed time
            total_elapsed = sum(entry['elapsedMs'] for entry in recent_entries)
            summary['avgElapsedMs'] = round(total_elapsed / len(recent_entries), 2)
        
        return summary

# Global security manager instance
security_manager = BackboneSecurityManager()

def require_admin(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get user ID from request (implement your auth logic here)
        user_id = request.headers.get('X-User-ID', 'anonymous')
        
        # For demo purposes, also check a simple auth header
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer admin_'):
            user_id = 'admin'
        
        if not security_manager.is_admin(user_id):
            security_manager.audit_action(
                actor_id=user_id,
                action='access_denied',
                target=request.endpoint or 'unknown',
                result='forbidden',
                elapsed_ms=0
            )
            raise Forbidden('Admin access required')
        
        # Store user ID for use in the request
        g.user_id = user_id
        return f(*args, **kwargs)
    
    return decorated_function

def rate_limit_admin(limit: int = 5, window_minutes: int = 1):
    """Decorator to apply rate limiting to admin endpoints"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = getattr(g, 'user_id', 'anonymous')
            
            if not security_manager.check_rate_limit(user_id, limit, window_minutes):
                security_manager.audit_action(
                    actor_id=user_id,
                    action='rate_limit_exceeded',
                    target=request.endpoint or 'unknown',
                    result='too_many_requests',
                    elapsed_ms=0
                )
                raise TooManyRequests(f'Rate limit exceeded: {limit} requests per {window_minutes} minute(s)')
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def validate_allowlist_urls(urls: List[str]) -> Dict[str, bool]:
    """Validate multiple URLs against allowlist"""
    results = {}
    for url in urls:
        if url:  # Skip empty URLs
            results[url] = security_manager.validate_host_allowlist(url)
    return results

def generate_idempotency_key(data: Dict[str, Any]) -> str:
    """Generate idempotency key from request data"""
    # Sort data for consistent hashing
    sorted_data = json.dumps(data, sort_keys=True)
    return hashlib.sha256(sorted_data.encode()).hexdigest()[:16]

class IdempotencyManager:
    """Manage idempotency for backbone operations"""
    
    def __init__(self):
        self.cache = {}  # {key: {result, timestamp}}
        self.ttl_hours = 24
    
    def get_cached_result(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached result if within TTL"""
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        age_hours = (time.time() - entry['timestamp']) / 3600
        
        if age_hours > self.ttl_hours:
            del self.cache[key]
            return None
        
        return entry['result']
    
    def cache_result(self, key: str, result: Dict[str, Any]):
        """Cache result with timestamp"""
        self.cache[key] = {
            'result': result,
            'timestamp': time.time()
        }
        
        # Clean old entries
        self._cleanup_cache()
    
    def _cleanup_cache(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if (current_time - entry['timestamp']) / 3600 > self.ttl_hours
        ]
        
        for key in expired_keys:
            del self.cache[key]

# Global idempotency manager
idempotency_manager = IdempotencyManager()

def with_idempotency(f):
    """Decorator to handle idempotency"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get idempotency key from header or generate from request data
        idempotency_key = request.headers.get('X-Idempotency-Key')
        
        if not idempotency_key:
            # Generate key from request data
            request_data = request.get_json() or {}
            idempotency_key = generate_idempotency_key(request_data)
        
        # Check for cached result
        cached_result = idempotency_manager.get_cached_result(idempotency_key)
        if cached_result:
            logger.info(f"Returning cached result for idempotency key: {idempotency_key}")
            return jsonify(cached_result)
        
        # Execute function and cache result
        result = f(*args, **kwargs)
        
        # Cache the result if it's successful
        if hasattr(result, 'get_json'):
            result_data = result.get_json()
            idempotency_manager.cache_result(idempotency_key, result_data)
        
        return result
    
    return decorated_function

def get_security_status() -> Dict[str, Any]:
    """Get current security status"""
    return {
        'rbac': {
            'adminUsers': len(security_manager.admin_users),
            'enabled': True
        },
        'allowlist': {
            'hosts': len(security_manager.allowlist_hosts),
            'enabled': True
        },
        'rateLimit': {
            'limit': 5,
            'windowMinutes': 1,
            'enabled': True
        },
        'audit': {
            'totalEvents': len(security_manager.audit_log),
            'enabled': True
        },
        'idempotency': {
            'cachedResults': len(idempotency_manager.cache),
            'ttlHours': idempotency_manager.ttl_hours,
            'enabled': True
        }
    }

if __name__ == '__main__':
    # Test the security manager
    print("Backbone Security Manager Test")
    print(f"Allowlist hosts: {security_manager.allowlist_hosts}")
    print(f"Admin users: {len(security_manager.admin_users)}")
    
    # Test allowlist validation
    test_urls = [
        'https://example.com/test',
        'https://malicious.com/test',
        'https://sub.example.com/test'
    ]
    
    for url in test_urls:
        valid = security_manager.validate_host_allowlist(url)
        print(f"URL {url}: {'ALLOWED' if valid else 'BLOCKED'}")
    
    print("\nSecurity status:")
    print(json.dumps(get_security_status(), indent=2))