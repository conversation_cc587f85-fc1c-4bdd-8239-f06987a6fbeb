# HexStrike AI - Advanced Cybersecurity Chat Assistant

[![CI/CD Pipeline](https://github.com/your-org/hexstrike-ai/workflows/CI/badge.svg)](https://github.com/your-org/hexstrike-ai/actions)
[![Security Scan](https://github.com/your-org/hexstrike-ai/workflows/Security%20Scan/badge.svg)](https://github.com/your-org/hexstrike-ai/actions)
[![Docker Build](https://img.shields.io/docker/build/hexstrike/hexstrike-ai)](https://hub.docker.com/r/hexstrike/hexstrike-ai)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Python Version](https://img.shields.io/badge/python-3.11+-blue.svg)](https://python.org)

HexStrike AI is a sophisticated cybersecurity chat assistant that combines the power of AI with professional penetration testing tools. It provides an intuitive interface for security professionals to conduct assessments, analyze vulnerabilities, and generate comprehensive reports.

## 🚀 Features

### Core Capabilities
- **AI-Powered Chat Interface**: Natural language interaction with cybersecurity tools
- **Integrated Security Tools**: Nmap, Nuclei, SQLMap, Gobuster, and more
- **Real-time Vulnerability Scanning**: Automated security assessments
- **Comprehensive Reporting**: Detailed vulnerability reports with remediation guidance
- **Role-Based Access Control**: Secure multi-user environment
- **MCP Integration**: Model Context Protocol for enhanced AI capabilities

### Security Features
- **Sandboxed Execution**: Isolated tool execution environment
- **Rate Limiting**: Protection against abuse and resource exhaustion
- **Audit Logging**: Complete activity tracking and compliance
- **JWT Authentication**: Secure session management
- **Input Validation**: Comprehensive security controls
- **RBAC System**: Fine-grained permission management

### Technical Features
- **Microservices Architecture**: Scalable and maintainable design
- **Docker Containerization**: Easy deployment and scaling
- **Redis Queue System**: Asynchronous task processing
- **PostgreSQL Database**: Reliable data persistence
- **Nginx Reverse Proxy**: High-performance web serving
- **CI/CD Pipeline**: Automated testing and deployment

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Chat Server   │    │   Worker Pool   │
│   (Static)      │◄──►│   (Flask API)   │◄──►│   (Background)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   PostgreSQL    │    │   Redis Queue   │
│   (Reverse      │    │   (Database)    │    │   (Tasks)       │
│    Proxy)       │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Overview

- **Frontend**: Modern web interface built with vanilla JavaScript
- **Chat Server**: Flask-based API server handling user interactions
- **Worker Pool**: Background workers executing security tools
- **Database**: PostgreSQL for persistent data storage
- **Cache/Queue**: Redis for session management and task queuing
- **Reverse Proxy**: Nginx for load balancing and SSL termination

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended) or Windows with WSL2
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 20GB free space
- **Network**: Internet access for tool updates and AI services

### Software Dependencies
- **Docker**: 20.10+ with Docker Compose
- **Python**: 3.11+ (for development)
- **Git**: Latest version
- **PowerShell**: 5.1+ (Windows) or PowerShell Core (Linux/macOS)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/hexstrike-ai.git
cd hexstrike-ai
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.docker .env

# Edit configuration (see Configuration section)
nano .env
```

### 3. Docker Deployment

#### Windows (PowerShell)
```powershell
# Make script executable and run setup
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\docker-manage.ps1 setup
.\docker-manage.ps1 start
```

#### Linux/macOS
```bash
# Make script executable
chmod +x docker-manage.sh

# Run setup and start
./docker-manage.sh setup
./docker-manage.sh start
```

### 4. Access the Application
- **Web Interface**: https://localhost:8443
- **API Documentation**: https://localhost:8443/api/docs
- **Health Check**: https://localhost:8443/health

### 5. Default Credentials
```
Username: admin
Password: admin123
```

**⚠️ Important**: Change default credentials immediately after first login!

## Usage

### Basic Commands

```bash
# Start the main server
python chatbox_server.py --port 8000

# Run with debug mode
python chatbox_server.py --debug

# Start MCP server
python chatbox_mcp.py --host localhost --port 3000

# View help
python chatbox_server.py --help
```

### Chat Interface

The chat interface provides an intuitive way to interact with Chat Box AI:

1. **Authentication**: Secure login system with user management
2. **AI Conversations**: Natural language interaction with security AI
3. **Tool Integration**: Direct access to security tools through chat
4. **Real-time Results**: Live updates and streaming responses
5. **Error Handling**: Intelligent error recovery and user guidance

## Configuration

### Server Configuration

Edit the configuration in `chatbox_server.py`:

```python
# Server settings
HOST = '0.0.0.0'
PORT = 8000
DEBUG = False

# Security settings
AUTH_REQUIRED = True
API_KEY_REQUIRED = True
```

### MCP Configuration

Configure the MCP server in `chatbox-ai-mcp.json`:

```json
{
  "mcpServers": {
    "chatbox-ai": {
      "command": "python",
      "args": ["chatbox_mcp.py"]
    }
  }
}
```

## Development

### Project Structure

```
chatbox-ai/
├── chat-interface/          # Frontend web interface
│   ├── index.html          # Main HTML file
│   ├── script.js           # JavaScript functionality
│   ├── styles.css          # CSS styling
│   └── app.js              # Application logic
├── chatbox_server.py       # Main backend server
├── chatbox_mcp.py          # MCP protocol implementation
├── requirements.txt        # Python dependencies
├── assets/                 # Static assets and documentation
└── README.md              # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Security Considerations

- **Authentication**: All endpoints require proper authentication
- **Input Validation**: Comprehensive input sanitization and validation
- **Secure Communication**: HTTPS/TLS encryption for all communications
- **Access Control**: Role-based access control (RBAC) implementation
- **Audit Logging**: Comprehensive logging of all security operations

## Troubleshooting

### Common Issues

1. **Module Not Found Errors**
   ```bash
   pip install -r requirements.txt
   ```

2. **Server Won't Start**
   - Check if port is already in use
   - Verify Python version compatibility
   - Review error logs for specific issues

3. **Chat Interface Not Loading**
   - Ensure backend server is running
   - Check browser console for JavaScript errors
   - Verify network connectivity

### Debug Mode

Run the server in debug mode for detailed logging:

```bash
python chatbox_server.py --debug
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation in the `assets/` directory

## Acknowledgments

- Security community for tool integrations
- Open source contributors
- AI/ML research community

---

**Chat Box AI** - Empowering cybersecurity through artificial intelligence.