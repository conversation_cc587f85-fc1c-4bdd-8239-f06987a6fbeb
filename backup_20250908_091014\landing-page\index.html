<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Box AI - Modular Landing Page</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-all duration-300">
    <!-- Template Control Panel -->
    <div id="templateControlPanel" class="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-w-sm transform transition-transform duration-300 translate-x-full">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-primary-700 dark:text-primary-400">Template Manager</h3>
            <button id="togglePanel" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                <i class="fas fa-cog"></i>
            </button>
        </div>
        
        <!-- Section Controls -->
        <div class="space-y-3">
            <div class="section-control" data-section="hero">
                <div class="flex justify-between items-center">
                    <span class="font-medium">Hero Section</span>
                    <div class="flex space-x-2">
                        <select class="template-selector text-sm border rounded px-2 py-1" data-section="hero">
                            <option value="modern">Modern</option>
                            <option value="minimal">Minimal</option>
                            <option value="gradient">Gradient</option>
                        </select>
                        <button class="toggle-section px-2 py-1 text-xs bg-green-500 text-white rounded" data-section="hero">ON</button>
                    </div>
                </div>
            </div>
            
            <div class="section-control" data-section="features">
                <div class="flex justify-between items-center">
                    <span class="font-medium">Features Section</span>
                    <div class="flex space-x-2">
                        <select class="template-selector text-sm border rounded px-2 py-1" data-section="features">
                            <option value="grid">Grid Layout</option>
                            <option value="accordion">Accordion</option>
                            <option value="tabs">Tabs</option>
                        </select>
                        <button class="toggle-section px-2 py-1 text-xs bg-green-500 text-white rounded" data-section="features">ON</button>
                    </div>
                </div>
            </div>
            
            <div class="section-control" data-section="pricing">
                <div class="flex justify-between items-center">
                    <span class="font-medium">Pricing Section</span>
                    <div class="flex space-x-2">
                        <select class="template-selector text-sm border rounded px-2 py-1" data-section="pricing">
                            <option value="cards">Cards</option>
                            <option value="table">Table</option>
                            <option value="comparison">Comparison</option>
                        </select>
                        <button class="toggle-section px-2 py-1 text-xs bg-green-500 text-white rounded" data-section="pricing">ON</button>
                    </div>
                </div>
            </div>
            
            <div class="section-control" data-section="testimonials">
                <div class="flex justify-between items-center">
                    <span class="font-medium">Testimonials</span>
                    <div class="flex space-x-2">
                        <select class="template-selector text-sm border rounded px-2 py-1" data-section="testimonials">
                            <option value="carousel">Carousel</option>
                            <option value="grid">Grid</option>
                            <option value="single">Single</option>
                        </select>
                        <button class="toggle-section px-2 py-1 text-xs bg-green-500 text-white rounded" data-section="testimonials">ON</button>
                    </div>
                </div>
            </div>
            
            <div class="section-control" data-section="contact">
                <div class="flex justify-between items-center">
                    <span class="font-medium">Contact Section</span>
                    <div class="flex space-x-2">
                        <select class="template-selector text-sm border rounded px-2 py-1" data-section="contact">
                            <option value="form">Form</option>
                            <option value="info">Info Cards</option>
                            <option value="map">With Map</option>
                        </select>
                        <button class="toggle-section px-2 py-1 text-xs bg-green-500 text-white rounded" data-section="contact">ON</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Theme Controls -->
        <div class="mt-6 pt-4 border-t">
            <h4 class="font-medium mb-2">Theme</h4>
            <div class="flex space-x-2">
                <button id="lightTheme" class="px-3 py-1 text-xs bg-yellow-400 text-gray-900 rounded">Light</button>
                <button id="darkTheme" class="px-3 py-1 text-xs bg-gray-800 text-white rounded">Dark</button>
            </div>
        </div>
    </div>
    
    <!-- Toggle Button -->
    <button id="panelToggle" class="fixed top-4 right-4 z-40 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300">
        <i class="fas fa-palette"></i>
    </button>
    
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-30 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <img src="../assets/chat-box-logo.png" alt="Chat Box AI" class="h-8 w-8">
                    <span class="text-xl font-bold text-primary-700 dark:text-primary-400">Chat Box AI</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#hero" class="nav-link">Home</a>
                    <a href="#features" class="nav-link">Features</a>
                    <a href="#pricing" class="nav-link">Pricing</a>
                    <a href="#testimonials" class="nav-link">Testimonials</a>
                    <a href="#contact" class="nav-link">Contact</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="mobileMenuToggle" class="md:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobileMenu" class="md:hidden hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-2 space-y-2">
                <a href="#hero" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Home</a>
                <a href="#features" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Features</a>
                <a href="#pricing" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Pricing</a>
                <a href="#testimonials" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Testimonials</a>
                <a href="#contact" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">Contact</a>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-16">
        <!-- Hero Section -->
        <section id="hero" class="section-container" data-section="hero" data-template="modern">
            <!-- Hero templates will be loaded here -->
        </section>
        
        <!-- Features Section -->
        <section id="features" class="section-container" data-section="features" data-template="accordion">
            <!-- Features templates will be loaded here -->
        </section>
        
        <!-- Pricing Section -->
        <section id="pricing" class="section-container" data-section="pricing" data-template="cards">
            <!-- Pricing templates will be loaded here -->
        </section>
        
        <!-- Testimonials Section -->
        <section id="testimonials" class="section-container" data-section="testimonials" data-template="carousel">
            <!-- Testimonials templates will be loaded here -->
        </section>
        
        <!-- Contact Section -->
        <section id="contact" class="section-container" data-section="contact" data-template="form">
            <!-- Contact templates will be loaded here -->
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="../assets/chat-box-logo.png" alt="Chat Box AI" class="h-8 w-8">
                        <span class="text-xl font-bold">Chat Box AI</span>
                    </div>
                    <p class="text-gray-400 mb-4">Advanced AI-Powered Cybersecurity Platform with 150+ security tools and 12+ autonomous AI agents.</p>
                    <div class="flex space-x-4">
                        <a href="https://github.com/0x4m4/chat-box-ai" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://discord.gg/BWnmrrSHbA" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-discord text-xl"></i>
                        </a>
                        <a href="https://www.linkedin.com/company/chat-box-ai" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Product</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white transition-colors">Features</a></li>
                        <li><a href="#pricing" class="hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="../README.md" class="hover:text-white transition-colors">Documentation</a></li>
                        <li><a href="../chat-interface/index.html" class="hover:text-white transition-colors">Try Demo</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#contact" class="hover:text-white transition-colors">Contact</a></li>
                        <li><a href="https://discord.gg/BWnmrrSHbA" class="hover:text-white transition-colors">Community</a></li>
                        <li><a href="mailto:<EMAIL>" class="hover:text-white transition-colors">Email Support</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Chat Box AI. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="templates.js"></script>
    <script src="app.js"></script>
</body>
</html>