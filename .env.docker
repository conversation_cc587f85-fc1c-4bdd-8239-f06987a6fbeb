# Docker Environment Configuration for HexStrike AI
# Copy this file to .env and update with your secure values

# Database Configuration
POSTGRES_DB=hexstrike_ai
POSTGRES_USER=hexstrike
POSTGRES_PASSWORD=secure_password_change_me_in_production

# Redis Configuration
REDIS_PASSWORD=redis_password_change_me_in_production

# Application Secrets
JWT_SECRET_KEY=jwt_secret_key_change_me_minimum_32_characters
SECRET_KEY=flask_secret_key_change_me_minimum_32_characters

# Application Configuration
FLASK_ENV=production
CHAT_BOX_PORT=3000
WORKER_CONCURRENCY=2
WORKER_TIMEOUT=300

# Security Configuration
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Strict

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_STORAGE_URL=redis://:${REDIS_PASSWORD}@redis:6379/1

# MCP Configuration
MCP_TIMEOUT=120
MCP_MAX_RETRIES=3
MCP_RATE_LIMIT=10

# SSL Configuration (for production)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring (optional)
# SENTRY_DSN=your_sentry_dsn_here
# PROMETHEUS_ENABLED=true