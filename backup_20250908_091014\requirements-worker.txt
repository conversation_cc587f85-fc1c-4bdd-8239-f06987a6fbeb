# HexStrike AI Worker Dependencies
# Heavy security tools isolated in worker container for better security
# These tools require elevated privileges and are sandboxed

# ============================================================================
# CORE WORKER DEPENDENCIES
# ============================================================================
rq==1.15.1                      # Redis Queue worker
redis==5.0.1                    # Redis client
requests==2.31.0                # HTTP client
psutil==5.9.6                   # System monitoring
structlog==23.2.0               # Structured logging
python-json-logger==2.0.7       # JSON log formatter

# ============================================================================
# WEB SCRAPING & BROWSER AUTOMATION
# ============================================================================
selenium==4.15.2                # Browser automation
webdriver-manager==4.0.1        # ChromeDriver management
beautifulsoup4==4.12.2          # HTML parsing
requests-html==0.10.0           # JavaScript-enabled requests

# ============================================================================
# PROXY & NETWORK ANALYSIS
# ============================================================================
mitmproxy==10.1.5               # HTTP/HTTPS proxy
scapy==2.5.0                    # Packet manipulation

# ============================================================================
# BINARY ANALYSIS & EXPLOITATION
# ============================================================================
pwntools==4.11.1                # Binary exploitation framework
angr==9.2.77                    # Binary analysis platform
ropper==1.13.8                  # ROP gadget finder
capstone==5.0.1                 # Disassembly framework
keystone-engine==0.9.2          # Assembly framework
unicorn==2.0.1.post1            # CPU emulator framework

# ============================================================================
# REVERSE ENGINEERING
# ============================================================================
r2pipe==1.8.0                   # Radare2 Python bindings
pyelftools==0.30                # ELF file analysis
pefile==2023.2.7                # PE file analysis

# ============================================================================
# CRYPTOGRAPHY & FORENSICS
# ============================================================================
cryptography==41.0.7            # Cryptographic operations
hashlib-compat==1.0.1           # Hash compatibility
volatility3==2.5.2              # Memory forensics

# ============================================================================
# NETWORK SECURITY TOOLS
# ============================================================================
nmap==0.0.1                     # Nmap Python wrapper (limited)
python-nmap==0.7.1              # Alternative Nmap wrapper
impacket==0.11.0                # Network protocol implementations

# ============================================================================
# VALIDATION & SERIALIZATION
# ============================================================================
pydantic==2.5.0                 # Data validation
marshmallow==3.20.1             # Object serialization

# ============================================================================
# ASYNC & NETWORKING
# ============================================================================
aiohttp==3.9.1                  # Async HTTP client
aiofiles==23.2.1                # Async file operations

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================
pytest==7.4.3                   # Testing framework
pytest-cov==4.1.0               # Coverage reporting
pytest-asyncio==0.21.1          # Async testing support

# ============================================================================
# SYSTEM & CONTAINER SECURITY
# ============================================================================
setproctitle==1.3.3             # Process title setting
resource-limits==1.0.0          # Resource limiting (custom)