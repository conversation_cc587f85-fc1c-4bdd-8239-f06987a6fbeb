{"summary": {"total_tests": 34, "passed": 21, "failed": 13, "success_rate": "61.8%", "timestamp": "2025-09-12T15:06:38.442744"}, "results": [{"test": "Main Dashboard", "status": "✅ PASS", "duration": "2022ms", "details": "Status: 200", "timestamp": "15:05:14"}, {"test": "Chat Interface", "status": "✅ PASS", "duration": "2072ms", "details": "Status: 200", "timestamp": "15:05:16"}, {"test": "Settings Page", "status": "✅ PASS", "duration": "2057ms", "details": "Status: 200", "timestamp": "15:05:18"}, {"test": "<PERSON>", "status": "✅ PASS", "duration": "2143ms", "details": "Status: 200", "timestamp": "15:05:20"}, {"test": "All Features Page", "status": "✅ PASS", "duration": "2057ms", "details": "Status: 200", "timestamp": "15:05:22"}, {"test": "Health Check", "status": "❌ FAIL", "duration": "12036ms", "details": "Error: HTTPConnectionPool(host='localhost', port=8888): Read timed out. (read timeout=10)", "timestamp": "15:05:34"}, {"test": "Features API", "status": "✅ PASS", "duration": "2029ms", "details": "Status: 200", "timestamp": "15:05:36"}, {"test": "Settings API", "status": "✅ PASS", "duration": "2051ms", "details": "Status: 200", "timestamp": "15:05:39"}, {"test": "Scraping Status", "status": "✅ PASS", "duration": "2049ms", "details": "Status: 200", "timestamp": "15:05:41"}, {"test": "Authentication Login", "status": "✅ PASS", "duration": "2035ms", "details": "Status: 401", "timestamp": "15:05:43"}, {"test": "AI Chat API", "status": "✅ PASS", "duration": "2042ms", "details": "Status: 200", "timestamp": "15:05:45"}, {"test": "NMAP Scanner", "status": "✅ PASS", "duration": "2208ms", "details": "Status: 200", "timestamp": "15:05:47"}, {"test": "<PERSON><PERSON><PERSON><PERSON>", "status": "✅ PASS", "duration": "2220ms", "details": "Status: 200", "timestamp": "15:05:49"}, {"test": "Gobuster", "status": "❌ FAIL", "duration": "2043ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:05:51"}, {"test": "SQLMap", "status": "❌ FAIL", "duration": "2060ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:05:53"}, {"test": "<PERSON><PERSON>", "status": "✅ PASS", "duration": "2190ms", "details": "Status: 200", "timestamp": "15:05:55"}, {"test": "Metasploit", "status": "❌ FAIL", "duration": "2094ms", "details": "Status: 500 (Expected: 200)", "timestamp": "15:05:57"}, {"test": "Hydra Brute Force", "status": "❌ FAIL", "duration": "2050ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:00"}, {"test": "WordPress Scanner", "status": "❌ FAIL", "duration": "2071ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:02"}, {"test": "FFUF Fuzzer", "status": "❌ FAIL", "duration": "2064ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:04"}, {"test": "Target Analysis", "status": "✅ PASS", "duration": "3096ms", "details": "Status: 200", "timestamp": "15:06:07"}, {"test": "Tool Selection", "status": "❌ FAIL", "duration": "2067ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:09"}, {"test": "<PERSON>", "status": "✅ PASS", "duration": "3451ms", "details": "Status: 200", "timestamp": "15:06:12"}, {"test": "Payload Generation", "status": "✅ PASS", "duration": "2062ms", "details": "Status: 200", "timestamp": "15:06:14"}, {"test": "Reconnaissance", "status": "❌ FAIL", "duration": "2023ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:16"}, {"test": "Vulnerability Hunting", "status": "❌ FAIL", "duration": "2047ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:18"}, {"test": "OSINT Workflow", "status": "❌ FAIL", "duration": "2077ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:20"}, {"test": "Process List", "status": "✅ PASS", "duration": "2049ms", "details": "Status: 200", "timestamp": "15:06:23"}, {"test": "Process Dashboard", "status": "✅ PASS", "duration": "3072ms", "details": "Status: 200", "timestamp": "15:06:26"}, {"test": "Pool Statistics", "status": "✅ PASS", "duration": "3060ms", "details": "Status: 200", "timestamp": "15:06:29"}, {"test": "Health Check", "status": "✅ PASS", "duration": "3082ms", "details": "Status: 200", "timestamp": "15:06:32"}, {"test": "CTF Tool Suggestions", "status": "❌ FAIL", "duration": "2079ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:34"}, {"test": "Crypto Solver", "status": "✅ PASS", "duration": "2054ms", "details": "Status: 200", "timestamp": "15:06:36"}, {"test": "Forensics Analyzer", "status": "❌ FAIL", "duration": "2051ms", "details": "Status: 400 (Expected: 200)", "timestamp": "15:06:38"}]}