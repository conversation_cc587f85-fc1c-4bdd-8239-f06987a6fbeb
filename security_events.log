2025-09-12 22:08:01,200 - INFO - {"timestamp": "2025-09-12T22:08:01.200117", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "Successful API access to login", "details": {"method": "POST", "status": "success", "response_time": 0.002299785614013672}, "blocked": false}
2025-09-12 22:08:03,243 - INFO - {"timestamp": "2025-09-12T22:08:03.243646", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "Successful API access to login", "details": {"method": "POST", "status": "success", "response_time": 0.002015352249145508}, "blocked": false}
2025-09-12 22:08:05,278 - INFO - {"timestamp": "2025-09-12T22:08:05.278615", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "Successful API access to login", "details": {"method": "POST", "status": "success", "response_time": 0.0012905597686767578}, "blocked": false}
2025-09-12 22:08:07,332 - INFO - {"timestamp": "2025-09-12T22:08:07.332151", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "register", "description": "Successful API access to register", "details": {"method": "POST", "status": "success", "response_time": 0.0014035701751708984}, "blocked": false}
2025-09-12 22:08:09,388 - INFO - {"timestamp": "2025-09-12T22:08:09.388850", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "register", "description": "Successful API access to register", "details": {"method": "POST", "status": "success", "response_time": 0.0013599395751953125}, "blocked": false}
2025-09-12 22:08:11,448 - INFO - {"timestamp": "2025-09-12T22:08:11.448549", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "register", "description": "API error in register: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.00043487548828125}, "blocked": false}
2025-09-12 22:08:13,478 - INFO - {"timestamp": "2025-09-12T22:08:13.478148", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0006561279296875}, "blocked": false}
2025-09-12 22:08:15,512 - INFO - {"timestamp": "2025-09-12T22:08:15.512297", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0005729198455810547}, "blocked": false}
2025-09-12 22:08:17,543 - INFO - {"timestamp": "2025-09-12T22:08:17.543655", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0005166530609130859}, "blocked": false}
2025-09-12 22:08:20,597 - INFO - {"timestamp": "2025-09-12T22:08:20.597335", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.012569904327392578}, "blocked": false}
2025-09-12 22:08:22,761 - INFO - {"timestamp": "2025-09-12T22:08:22.761665", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0007109642028808594}, "blocked": false}
2025-09-12 22:08:24,969 - INFO - {"timestamp": "2025-09-12T22:08:24.969104", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0006711483001708984}, "blocked": false}
2025-09-12 22:08:27,126 - INFO - {"timestamp": "2025-09-12T22:08:27.126518", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0009632110595703125}, "blocked": false}
2025-09-12 22:08:29,272 - INFO - {"timestamp": "2025-09-12T22:08:29.272659", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0003342628479003906}, "blocked": false}
2025-09-12 22:08:31,433 - INFO - {"timestamp": "2025-09-12T22:08:31.433025", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0005209445953369141}, "blocked": false}
2025-09-12 22:08:33,596 - INFO - {"timestamp": "2025-09-12T22:08:33.596466", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0005381107330322266}, "blocked": false}
2025-09-12 22:08:35,752 - INFO - {"timestamp": "2025-09-12T22:08:35.752354", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0006856918334960938}, "blocked": false}
2025-09-12 22:08:37,920 - INFO - {"timestamp": "2025-09-12T22:08:37.920212", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0008649826049804688}, "blocked": false}
2025-09-12 22:08:40,059 - INFO - {"timestamp": "2025-09-12T22:08:40.059862", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0003666877746582031}, "blocked": false}
2025-09-12 22:08:42,232 - INFO - {"timestamp": "2025-09-12T22:08:42.232279", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0010614395141601562}, "blocked": false}
2025-09-12 22:08:44,387 - INFO - {"timestamp": "2025-09-12T22:08:44.387017", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0010607242584228516}, "blocked": false}
2025-09-12 22:08:46,570 - INFO - {"timestamp": "2025-09-12T22:08:46.570455", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0010356903076171875}, "blocked": false}
2025-09-12 22:08:48,729 - INFO - {"timestamp": "2025-09-12T22:08:48.729388", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0004515647888183594}, "blocked": false}
2025-09-12 22:08:50,885 - INFO - {"timestamp": "2025-09-12T22:08:50.885270", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.001069784164428711}, "blocked": false}
2025-09-12 22:08:53,039 - INFO - {"timestamp": "2025-09-12T22:08:53.038767", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0010859966278076172}, "blocked": false}
2025-09-12 22:08:55,214 - INFO - {"timestamp": "2025-09-12T22:08:55.214394", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0010654926300048828}, "blocked": false}
2025-09-12 22:08:57,362 - INFO - {"timestamp": "2025-09-12T22:08:57.361969", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0007920265197753906}, "blocked": false}
2025-09-12 22:08:59,528 - INFO - {"timestamp": "2025-09-12T22:08:59.528360", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0011491775512695312}, "blocked": false}
2025-09-12 22:09:01,685 - INFO - {"timestamp": "2025-09-12T22:09:01.685592", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "HexStrike-Security-Assessment/1.0", "endpoint": "login", "description": "API error in login: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0009789466857910156}, "blocked": false}
2025-09-17 08:11:45,442 - INFO - {"timestamp": "2025-09-17T08:11:45.442236", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.04730057716369629}, "blocked": false}
2025-09-18 08:39:58,349 - INFO - {"timestamp": "2025-09-18T08:39:58.349536", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0059130191802978516}, "blocked": false}
2025-09-18 08:57:58,927 - INFO - {"timestamp": "2025-09-18T08:57:58.927631", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.004222393035888672}, "blocked": false}
2025-09-19 16:24:17,156 - INFO - {"timestamp": "2025-09-19T16:24:17.156524", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.005771636962890625}, "blocked": false}
2025-09-19 16:29:09,606 - INFO - {"timestamp": "2025-09-19T16:29:09.606135", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.002468585968017578}, "blocked": false}
2025-09-19 20:08:46,344 - INFO - {"timestamp": "2025-09-19T20:08:46.343717", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.006116390228271484}, "blocked": false}
2025-09-19 20:50:26,113 - INFO - {"timestamp": "2025-09-19T20:50:26.113760", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0024824142456054688}, "blocked": false}
2025-09-19 21:05:34,427 - INFO - {"timestamp": "2025-09-19T21:05:34.427489", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0028429031372070312}, "blocked": false}
2025-09-19 21:15:29,462 - INFO - {"timestamp": "2025-09-19T21:15:29.461440", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.007469892501831055}, "blocked": false}
2025-09-19 21:21:49,462 - INFO - {"timestamp": "2025-09-19T21:21:49.461431", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0006425380706787109}, "blocked": false}
2025-09-19 21:31:02,842 - INFO - {"timestamp": "2025-09-19T21:31:02.841601", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0007123947143554688}, "blocked": false}
2025-09-19 21:33:47,668 - INFO - {"timestamp": "2025-09-19T21:33:47.668001", "event_type": "api_error", "threat_level": "medium", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36", "endpoint": "chat_endpoint", "description": "API error in chat_endpoint: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "details": {"method": "POST", "error": "403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server.", "response_time": 0.0005106925964355469}, "blocked": false}
2025-09-21 09:23:14,836 - INFO - {"timestamp": "2025-09-21T09:23:14.836651", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.005652666091918945}, "blocked": false}
2025-09-21 09:23:56,651 - INFO - {"timestamp": "2025-09-21T09:23:56.651023", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0016794204711914062}, "blocked": false}
2025-09-21 09:25:02,777 - INFO - {"timestamp": "2025-09-21T09:25:02.777848", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0027008056640625}, "blocked": false}
2025-09-21 09:25:13,890 - INFO - {"timestamp": "2025-09-21T09:25:13.889977", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.002701997756958008}, "blocked": false}
2025-09-21 09:34:36,942 - INFO - {"timestamp": "2025-09-21T09:34:36.942356", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0015232563018798828}, "blocked": false}
2025-09-22 07:52:29,051 - INFO - {"timestamp": "2025-09-22T07:52:29.051168", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "chat_endpoint", "description": "Successful API access to chat_endpoint", "details": {"method": "POST", "status": "success", "response_time": 0.0031747817993164062}, "blocked": false}
2025-09-22 07:54:39,282 - INFO - {"timestamp": "2025-09-22T07:54:39.282131", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "login", "description": "Successful API access to login", "details": {"method": "POST", "status": "success", "response_time": 0.0012080669403076172}, "blocked": false}
2025-09-22 07:54:39,296 - INFO - {"timestamp": "2025-09-22T07:54:39.296406", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "register", "description": "Successful API access to register", "details": {"method": "POST", "status": "success", "response_time": 0.0010023117065429688}, "blocked": false}
2025-09-22 08:15:05,260 - INFO - {"timestamp": "2025-09-22T08:15:05.260774", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "register", "description": "Successful API access to register", "details": {"method": "POST", "status": "success", "response_time": 0.005423784255981445}, "blocked": false}
2025-09-22 08:15:16,176 - INFO - {"timestamp": "2025-09-22T08:15:16.176804", "event_type": "api_access", "threat_level": "low", "source_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6328", "endpoint": "login", "description": "Successful API access to login", "details": {"method": "POST", "status": "success", "response_time": 0.001001119613647461}, "blocked": false}
