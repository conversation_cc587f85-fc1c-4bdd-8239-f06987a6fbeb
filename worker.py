#!/usr/bin/env python3
"""
HexStrike AI - Redis Queue Worker
Secure worker for executing security tools with sandboxing and resource limits
"""

import os
import sys
import json
import time
import signal
import subprocess
import tempfile
import shutil
import resource
import psutil
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

import redis
from rq import Worker, Queue, Connection
from rq.job import Job as RQJob
from flask import current_app

# Add project root to path
sys.path.insert(0, os.path.dirname(__file__))

from app_factory import create_app
from models import db, Job, User, Report
from security_middleware import audit_log
from input_validation import InputValidator, SQLSafeQuery

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

class SecurityConfig:
    """Security configuration for worker processes"""
    
    # Resource limits
    MAX_MEMORY_MB = 512
    MAX_CPU_TIME_SECONDS = 120
    MAX_WALL_TIME_SECONDS = 180
    MAX_FILE_SIZE_MB = 100
    MAX_OPEN_FILES = 100
    
    # Allowed binaries (whitelist)
    ALLOWED_BINARIES = {
        'nmap': '/usr/bin/nmap',
        'nikto': '/usr/bin/nikto',
        'sqlmap': '/usr/bin/sqlmap',
        'dirb': '/usr/bin/dirb',
        'gobuster': '/usr/bin/gobuster',
        'whatweb': '/usr/bin/whatweb',
        'sslyze': '/usr/bin/sslyze',
        'testssl': '/usr/bin/testssl.sh',
        'dig': '/usr/bin/dig',
        'nslookup': '/usr/bin/nslookup',
        'whois': '/usr/bin/whois',
        'curl': '/usr/bin/curl',
        'wget': '/usr/bin/wget',
        'python3': '/usr/bin/python3',
        'yara': '/usr/bin/yara',
        'strings': '/usr/bin/strings',
        'file': '/usr/bin/file',
        'hexdump': '/usr/bin/hexdump'
    }
    
    # Blocked network ranges (prevent internal network access)
    BLOCKED_NETWORKS = [
        '127.0.0.0/8',
        '10.0.0.0/8',
        '172.16.0.0/12',
        '192.168.0.0/16',
        '169.254.0.0/16',
        'fc00::/7',
        'fe80::/10',
        '::1/128'
    ]
    
    # Allowed file extensions for uploads
    ALLOWED_EXTENSIONS = {
        '.txt', '.log', '.json', '.xml', '.html', '.csv',
        '.exe', '.dll', '.bin', '.elf', '.so',
        '.pcap', '.pcapng', '.cap'
    }

# ============================================================================
# SANDBOX UTILITIES
# ============================================================================

class SandboxError(Exception):
    """Custom exception for sandbox-related errors"""
    pass

class ResourceMonitor:
    """Monitor and enforce resource limits for processes"""
    
    def __init__(self, pid: int, limits: Dict[str, Any]):
        self.pid = pid
        self.limits = limits
        self.start_time = time.time()
        self.process = psutil.Process(pid)
    
    def check_limits(self) -> bool:
        """Check if process exceeds resource limits"""
        try:
            # Check wall time
            elapsed = time.time() - self.start_time
            if elapsed > self.limits.get('wall_time', 180):
                return False
            
            # Check memory usage
            memory_mb = self.process.memory_info().rss / 1024 / 1024
            if memory_mb > self.limits.get('memory_mb', 512):
                return False
            
            # Check CPU time
            cpu_times = self.process.cpu_times()
            cpu_time = cpu_times.user + cpu_times.system
            if cpu_time > self.limits.get('cpu_time', 120):
                return False
            
            return True
        except psutil.NoSuchProcess:
            return False

@contextmanager
def secure_tempdir():
    """Create a secure temporary directory with restricted permissions"""
    temp_dir = tempfile.mkdtemp(prefix='hexstrike_')
    try:
        # Set restrictive permissions (owner only)
        os.chmod(temp_dir, 0o700)
        yield temp_dir
    finally:
        # Clean up
        shutil.rmtree(temp_dir, ignore_errors=True)

def set_resource_limits():
    """Set resource limits for the current process"""
    # Memory limit
    memory_limit = SecurityConfig.MAX_MEMORY_MB * 1024 * 1024
    resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
    
    # CPU time limit
    cpu_limit = SecurityConfig.MAX_CPU_TIME_SECONDS
    resource.setrlimit(resource.RLIMIT_CPU, (cpu_limit, cpu_limit))
    
    # File size limit
    file_limit = SecurityConfig.MAX_FILE_SIZE_MB * 1024 * 1024
    resource.setrlimit(resource.RLIMIT_FSIZE, (file_limit, file_limit))
    
    # Open files limit
    files_limit = SecurityConfig.MAX_OPEN_FILES
    resource.setrlimit(resource.RLIMIT_NOFILE, (files_limit, files_limit))

def validate_binary(tool_name: str) -> str:
    """Validate and return the full path to an allowed binary"""
    if tool_name not in SecurityConfig.ALLOWED_BINARIES:
        raise SandboxError(f"Tool '{tool_name}' is not in the allowlist")
    
    binary_path = SecurityConfig.ALLOWED_BINARIES[tool_name]
    if not os.path.exists(binary_path):
        raise SandboxError(f"Binary '{binary_path}' not found")
    
    if not os.access(binary_path, os.X_OK):
        raise SandboxError(f"Binary '{binary_path}' is not executable")
    
    return binary_path

def sanitize_arguments(args: List[str]) -> List[str]:
    """Sanitize command line arguments to prevent injection"""
    sanitized = []
    dangerous_chars = ['&', '|', ';', '`', '$', '(', ')', '<', '>', '\n', '\r']
    
    for arg in args:
        # Check for dangerous characters
        if any(char in arg for char in dangerous_chars):
            raise SandboxError(f"Dangerous character detected in argument: {arg}")
        
        # Limit argument length
        if len(arg) > 1000:
            raise SandboxError(f"Argument too long: {len(arg)} characters")
        
        sanitized.append(arg)
    
    return sanitized

# ============================================================================
# JOB EXECUTION ENGINE
# ============================================================================

class SecureJobExecutor:
    """Secure job executor with sandboxing and monitoring"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.job = None
        self.process = None
        self.monitor = None
        self.temp_dir = None
    
    def execute(self) -> Dict[str, Any]:
        """Execute a job securely with input validation"""
        try:
            # Validate job ID
            is_valid, validated_job_id = InputValidator.validate_integer(self.job_id, min_val=1)
            if not is_valid:
                raise SandboxError(f"Invalid job ID: {self.job_id}")
            
            # Load job from database using safe query
            self.job = Job.query.filter(Job.id == validated_job_id).first()
            if not self.job:
                raise SandboxError(f"Job {self.job_id} not found")
            
            # Mark job as started
            self.job.mark_started()
            db.session.commit()
            
            # Execute based on job type
            if self.job.job_type == 'security_scan':
                result = self._execute_security_scan()
            elif self.job.job_type == 'vulnerability_scan':
                result = self._execute_vulnerability_scan()
            elif self.job.job_type == 'malware_analysis':
                result = self._execute_malware_analysis()
            else:
                raise SandboxError(f"Unknown job type: {self.job.job_type}")
            
            # Mark job as completed
            self.job.mark_completed(result)
            db.session.commit()
            
            # Log success
            audit_log(
                action='job_completed',
                user_id=str(self.job.user_id),
                resource_type='job',
                resource_id=str(self.job.id),
                outcome='success',
                details={'tool_name': self.job.tool_name, 'duration': self.job.duration}
            )
            
            return result
            
        except Exception as e:
            # Mark job as failed
            if self.job:
                self.job.mark_failed(str(e))
                db.session.commit()
            
            # Log failure
            audit_log(
                action='job_failed',
                user_id=str(self.job.user_id) if self.job else None,
                resource_type='job',
                resource_id=str(self.job.id) if self.job else self.job_id,
                outcome='failure',
                details={'error': str(e)}
            )
            
            raise
    
    def _execute_security_scan(self) -> Dict[str, Any]:
        """Execute security scanning tools (nmap, etc.)"""
        tool_name = self.job.tool_name
        params = self.job.parameters
        
        if tool_name == 'nmap':
            return self._execute_nmap(params)
        elif tool_name == 'nikto':
            return self._execute_nikto(params)
        else:
            raise SandboxError(f"Unsupported security scan tool: {tool_name}")
    
    def _execute_vulnerability_scan(self) -> Dict[str, Any]:
        """Execute vulnerability scanning tools"""
        tool_name = self.job.tool_name
        params = self.job.parameters
        
        if tool_name == 'sqlmap':
            return self._execute_sqlmap(params)
        elif tool_name == 'dirb':
            return self._execute_dirb(params)
        else:
            raise SandboxError(f"Unsupported vulnerability scan tool: {tool_name}")
    
    def _execute_malware_analysis(self) -> Dict[str, Any]:
        """Execute malware analysis tools"""
        tool_name = self.job.tool_name
        params = self.job.parameters
        
        if tool_name == 'yara':
            return self._execute_yara(params)
        elif tool_name == 'strings':
            return self._execute_strings(params)
        else:
            raise SandboxError(f"Unsupported malware analysis tool: {tool_name}")
    
    def _execute_nmap(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute nmap scan"""
        target = params.get('target')
        scan_type = params.get('scan_type', 'tcp_syn')
        ports = params.get('ports', '1-1000')
        
        if not target:
            raise SandboxError("Target is required for nmap scan")
        
        # Build command
        binary_path = validate_binary('nmap')
        args = [binary_path]
        
        # Add scan type
        if scan_type == 'tcp_syn':
            args.append('-sS')
        elif scan_type == 'tcp_connect':
            args.append('-sT')
        elif scan_type == 'udp':
            args.append('-sU')
        
        # Add ports
        args.extend(['-p', ports])
        
        # Add output format
        args.extend(['-oX', '-'])  # XML output to stdout
        
        # Add target
        args.append(target)
        
        return self._run_command(args)
    
    def _execute_nikto(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute nikto web scanner"""
        target = params.get('target')
        options = params.get('options', [])
        
        if not target:
            raise SandboxError("Target is required for nikto scan")
        
        # Build command
        binary_path = validate_binary('nikto')
        args = [binary_path, '-h', target]
        
        # Add options
        for option in options:
            args.append(option)
        
        return self._run_command(args)
    
    def _execute_sqlmap(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute sqlmap SQL injection scanner"""
        url = params.get('url')
        if not url:
            raise SandboxError("URL is required for sqlmap")
        
        binary_path = validate_binary('sqlmap')
        args = [binary_path, '-u', url, '--batch', '--random-agent']
        
        return self._run_command(args)
    
    def _execute_dirb(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute dirb directory scanner"""
        url = params.get('url')
        wordlist = params.get('wordlist', '/usr/share/dirb/wordlists/common.txt')
        
        if not url:
            raise SandboxError("URL is required for dirb")
        
        binary_path = validate_binary('dirb')
        args = [binary_path, url, wordlist]
        
        return self._run_command(args)
    
    def _execute_yara(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute YARA malware scanner"""
        file_path = params.get('file_path')
        rules = params.get('rules')
        
        if not file_path or not rules:
            raise SandboxError("File path and rules are required for YARA")
        
        binary_path = validate_binary('yara')
        args = [binary_path, rules, file_path]
        
        return self._run_command(args)
    
    def _execute_strings(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute strings utility"""
        file_path = params.get('file_path')
        min_length = params.get('min_length', 4)
        
        if not file_path:
            raise SandboxError("File path is required for strings")
        
        binary_path = validate_binary('strings')
        args = [binary_path, '-n', str(min_length), file_path]
        
        return self._run_command(args)
    
    def _run_command(self, args: List[str]) -> Dict[str, Any]:
        """Run a command in a secure sandbox"""
        # Sanitize arguments
        args = sanitize_arguments(args)
        
        with secure_tempdir() as temp_dir:
            self.temp_dir = temp_dir
            
            try:
                # Start process with resource limits
                self.process = subprocess.Popen(
                    args,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=temp_dir,
                    preexec_fn=set_resource_limits,
                    env={'PATH': '/usr/bin:/bin'},  # Restricted PATH
                    text=True
                )
                
                # Monitor process
                self.monitor = ResourceMonitor(
                    self.process.pid,
                    {
                        'memory_mb': SecurityConfig.MAX_MEMORY_MB,
                        'cpu_time': SecurityConfig.MAX_CPU_TIME_SECONDS,
                        'wall_time': SecurityConfig.MAX_WALL_TIME_SECONDS
                    }
                )
                
                # Wait for completion with monitoring
                start_time = time.time()
                while self.process.poll() is None:
                    if not self.monitor.check_limits():
                        self.process.kill()
                        raise SandboxError("Process exceeded resource limits")
                    
                    if time.time() - start_time > SecurityConfig.MAX_WALL_TIME_SECONDS:
                        self.process.kill()
                        raise SandboxError("Process timed out")
                    
                    time.sleep(0.1)
                
                # Get output
                stdout, stderr = self.process.communicate()
                exit_code = self.process.returncode
                
                return {
                    'stdout': stdout,
                    'stderr': stderr,
                    'exit_code': exit_code,
                    'command': ' '.join(args),
                    'duration': time.time() - start_time
                }
                
            except Exception as e:
                if self.process:
                    try:
                        self.process.kill()
                    except:
                        pass
                raise SandboxError(f"Command execution failed: {str(e)}")

# ============================================================================
# WORKER FUNCTIONS
# ============================================================================

def execute_job(job_id: str) -> Dict[str, Any]:
    """Main worker function to execute a job"""
    app = create_app()
    
    with app.app_context():
        executor = SecureJobExecutor(job_id)
        return executor.execute()

def cleanup_old_jobs():
    """Clean up old completed jobs"""
    app = create_app()
    
    with app.app_context():
        # Delete jobs older than 30 days
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        old_jobs = Job.query.filter(
            Job.completed_at < cutoff_date,
            Job.status.in_(['completed', 'failed', 'timeout'])
        ).all()
        
        for job in old_jobs:
            db.session.delete(job)
        
        db.session.commit()
        print(f"Cleaned up {len(old_jobs)} old jobs")

# ============================================================================
# WORKER STARTUP
# ============================================================================

def main():
    """Main worker process"""
    # Create Flask app
    app = create_app()
    
    with app.app_context():
        # Connect to Redis
        redis_url = app.config.get('REDIS_URL', 'redis://localhost:6379/0')
        redis_conn = redis.from_url(redis_url)
        
        # Create queues
        high_queue = Queue('high', connection=redis_conn)
        default_queue = Queue('default', connection=redis_conn)
        low_queue = Queue('low', connection=redis_conn)
        
        # Create worker
        worker = Worker(
            [high_queue, default_queue, low_queue],
            connection=redis_conn,
            name=f'hexstrike-worker-{os.getpid()}'
        )
        
        print(f"Starting HexStrike AI worker (PID: {os.getpid()})")
        print(f"Redis URL: {redis_url}")
        print(f"Queues: high, default, low")
        
        # Start worker
        worker.work(with_scheduler=True)

if __name__ == '__main__':
    main()