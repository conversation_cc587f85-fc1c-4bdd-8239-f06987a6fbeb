<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penetration Testing Interface - HexStrike AI</title>
    <style>
        :root {
            --primary-color: #e63946;
            --secondary-color: #457b9d;
            --dark-bg: #1d3557;
            --light-bg: #f1faee;
            --accent-color: #a8dadc;
            --text-light: #f1faee;
            --text-dark: #1d3557;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 20px 0;
            text-align: center;
        }
        
        header h1 {
            margin-bottom: 10px;
        }
        
        .security-form {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="url"],
        select,
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #c1121f;
        }
        
        .results {
            margin-top: 30px;
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .results pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        .back-button {
            display: inline-block;
            margin-top: 20px;
            background-color: var(--secondary-color);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
        }
        
        .back-button:hover {
            background-color: #3d6990;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .tool-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            border-left: 5px solid var(--primary-color);
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .tool-card.selected {
            border-left: 5px solid #2ecc71;
        }
        
        .tool-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .tool-result {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: #f8f9fa;
            display: none;
        }
        
        .tool-result.running {
            display: block;
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
        }
        
        .tool-result.success {
            display: block;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .tool-result.error {
            display: block;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>HexStrike AI - Penetration Testing Interface</h1>
            <p>Advanced penetration testing and vulnerability assessment tools</p>
        </div>
    </header>
    
    <div class="container">
        <div class="security-form">
            <h2>Penetration Testing Configuration</h2>
            <form id="pentestForm">
                <div class="form-group">
                    <label for="targetUrl">Target URL or IP:</label>
                    <input type="text" id="targetUrl" name="targetUrl" placeholder="https://example.com or ***********" required>
                </div>
                
                <div class="form-group">
                    <label for="scanType">Penetration Test Type:</label>
                    <select id="scanType" name="scanType">
                        <option value="blackbox">Black Box Testing</option>
                        <option value="whitebox">White Box Testing</option>
                        <option value="graybox">Gray Box Testing</option>
                        <option value="targeted">Targeted Exploitation</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="testScope">Test Scope:</label>
                    <select id="testScope" name="testScope">
                        <option value="network">Network Infrastructure</option>
                        <option value="web">Web Application</option>
                        <option value="api">API Endpoints</option>
                        <option value="mobile">Mobile Application Backend</option>
                        <option value="cloud">Cloud Infrastructure</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="exploitOptions">Exploitation Options:</label>
                    <select id="exploitOptions" name="exploitOptions">
                        <option value="passive">Passive (No Exploitation)</option>
                        <option value="safe">Safe Exploitation (Non-destructive)</option>
                        <option value="full">Full Exploitation (With Safeguards)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="additionalNotes">Additional Notes:</label>
                    <textarea id="additionalNotes" name="additionalNotes" rows="4" placeholder="Enter any specific targets, exclusions, or special instructions"></textarea>
                </div>
                
                <button type="submit" id="startPentest">Start Penetration Test</button>
            </form>
        </div>
        
        <div class="tool-grid">
            <div class="tool-card">
                <h3>Network Reconnaissance</h3>
                <p>Discover hosts, open ports, and services on the target network</p>
                <div class="tag-container">
                    <span class="tag tag-blue">Port Scanning</span>
                    <span class="tag tag-green">Service Detection</span>
                </div>
                <div class="tool-result" id="reconResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Vulnerability Scanner</h3>
                <p>Identify known vulnerabilities in target systems and applications</p>
                <div class="tag-container">
                    <span class="tag tag-red">CVE Detection</span>
                    <span class="tag tag-orange">Patch Analysis</span>
                </div>
                <div class="tool-result" id="vulnResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Web Application Tester</h3>
                <p>Test web applications for OWASP Top 10 vulnerabilities</p>
                <div class="tag-container">
                    <span class="tag tag-purple">Injection Testing</span>
                    <span class="tag tag-blue">XSS Detection</span>
                </div>
                <div class="tool-result" id="webResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Password Cracker</h3>
                <p>Test password strength and attempt to crack weak credentials</p>
                <div class="tag-container">
                    <span class="tag tag-red">Dictionary Attack</span>
                    <span class="tag tag-orange">Brute Force</span>
                </div>
                <div class="tool-result" id="passwordResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Exploit Framework</h3>
                <p>Execute controlled exploits against vulnerable systems</p>
                <div class="tag-container">
                    <span class="tag tag-red">Remote Execution</span>
                    <span class="tag tag-purple">Privilege Escalation</span>
                </div>
                <div class="tool-result" id="exploitResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Social Engineering Toolkit</h3>
                <p>Create and deploy social engineering campaigns to test user awareness</p>
                <div class="tag-container">
                    <span class="tag tag-orange">Phishing Simulation</span>
                    <span class="tag tag-purple">Pretexting</span>
                </div>
                <div class="tool-result" id="socialEngResult"></div>
            </div>
        </div>
        
        <div class="results" style="display: none;">
            <h2>Penetration Test Results</h2>
            <div id="resultsSummary"></div>
            <pre id="resultsDetail"></pre>
            <a href="#" class="back-button" id="backToForm">Back to Configuration</a>
        </div>
    </div>
    
    <script>
        document.getElementById('pentestForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            document.getElementById('startPentest').textContent = 'Running Test...';
            document.getElementById('startPentest').disabled = true;
            
            // Simulate test running
            const toolResults = document.querySelectorAll('.tool-result');
            toolResults.forEach(result => {
                result.classList.add('running');
                result.textContent = 'Initializing...';
            });
            
            // Simulate different tool completions
            setTimeout(() => {
                document.getElementById('reconResult').textContent = 'Completed: 3 hosts discovered, 17 open ports identified';
                document.getElementById('reconResult').classList.remove('running');
                document.getElementById('reconResult').classList.add('success');
            }, 2000);
            
            setTimeout(() => {
                document.getElementById('vulnResult').textContent = 'Completed: 7 vulnerabilities detected (2 high, 3 medium, 2 low)';
                document.getElementById('vulnResult').classList.remove('running');
                document.getElementById('vulnResult').classList.add('success');
            }, 3500);
            
            setTimeout(() => {
                document.getElementById('webResult').textContent = 'Completed: XSS vulnerability found in search function';
                document.getElementById('webResult').classList.remove('running');
                document.getElementById('webResult').classList.add('success');
            }, 5000);
            
            setTimeout(() => {
                document.getElementById('exploitResult').textContent = 'Completed: 2 exploits successful';
                document.getElementById('exploitResult').classList.remove('running');
                document.getElementById('exploitResult').classList.add('success');
            }, 6500);
            
            setTimeout(() => {
                document.getElementById('passwordResult').textContent = 'Completed: Weak password policy detected';
                document.getElementById('passwordResult').classList.remove('running');
                document.getElementById('passwordResult').classList.add('success');
            }, 8000);
            
            setTimeout(() => {
                document.getElementById('postExploitResult').textContent = 'Completed: Privilege escalation possible via kernel exploit';
                document.getElementById('postExploitResult').classList.remove('running');
                document.getElementById('postExploitResult').classList.add('success');
                
                // Show results
                document.querySelector('.results').style.display = 'block';
                document.getElementById('resultsSummary').innerHTML = `
                    <h3>Summary</h3>
                    <p>Target: ${document.getElementById('targetUrl').value}</p>
                    <p>Test Type: ${document.getElementById('scanType').options[document.getElementById('scanType').selectedIndex].text}</p>
                    <p>Scope: ${document.getElementById('testScope').options[document.getElementById('testScope').selectedIndex].text}</p>
                    <p>Critical Findings: 2 high risk vulnerabilities detected</p>
                `;
                
                document.getElementById('resultsDetail').textContent = `
PENETRATION TEST REPORT
========================
Target: ${document.getElementById('targetUrl').value}
Test Date: ${new Date().toISOString().split('T')[0]}
Test Type: ${document.getElementById('scanType').options[document.getElementById('scanType').selectedIndex].text}

FINDINGS SUMMARY
---------------
- 2 High Risk Vulnerabilities
- 3 Medium Risk Vulnerabilities
- 2 Low Risk Vulnerabilities

DETAILED FINDINGS
----------------
[HIGH] SQL Injection in login form
  Description: The login form is vulnerable to SQL injection attacks
  Impact: Unauthorized access to user accounts and sensitive data
  Recommendation: Implement prepared statements and input validation

[HIGH] Outdated Web Server (Apache 2.2.15)
  Description: The web server is running an outdated version with known vulnerabilities
  Impact: Remote code execution and server compromise
  Recommendation: Upgrade to the latest stable version

[MEDIUM] Cross-Site Scripting (XSS) in search function
  Description: The search function does not properly sanitize user input
  Impact: Potential session hijacking and phishing attacks
  Recommendation: Implement proper output encoding

[MEDIUM] Weak Password Policy
  Description: System allows simple passwords with no complexity requirements
  Impact: Increased susceptibility to brute force attacks
  Recommendation: Enforce strong password policy

[MEDIUM] Insecure Direct Object References
  Description: API endpoints allow access to resources via predictable IDs
  Impact: Unauthorized access to data belonging to other users
  Recommendation: Implement proper access controls

[LOW] Missing HTTP Security Headers
  Description: Several security headers are not implemented
  Impact: Reduced browser security protections
  Recommendation: Add Content-Security-Policy, X-XSS-Protection headers

[LOW] Information Disclosure in Error Messages
  Description: Detailed error messages reveal implementation details
  Impact: Information gathering for targeted attacks
  Recommendation: Implement generic error messages in production
`;
                
                // Reset form state
                document.getElementById('startPentest').textContent = 'Start Penetration Test';
                document.getElementById('startPentest').disabled = false;
            }, 9500);
        });
        
        document.getElementById('backToForm').addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector('.results').style.display = 'none';
            
            // Reset tool results
            const toolResults = document.querySelectorAll('.tool-result');
            toolResults.forEach(result => {
                result.classList.remove('running', 'success', 'error');
                result.textContent = '';
            });
        });
    </script>
</body>
</html>