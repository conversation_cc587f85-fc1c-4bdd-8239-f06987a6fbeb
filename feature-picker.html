<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Feature Selection</title>
    <style>
        .feature-picker {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .category-group {
            margin-bottom: 24px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: 18px;
        }
        .category-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
        }
        .category-content {
            padding: 20px;
            background: #f8f9fa;
            display: none;
        }
        .category-content.active {
            display: block;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
        }
        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
            transition: all 0.2s ease;
        }
        .feature-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }
        .feature-checkbox {
            margin-top: 2px;
            width: 16px;
            height: 16px;
            accent-color: #667eea;
        }
        .feature-label {
            flex: 1;
            cursor: pointer;
        }
        .feature-name {
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 4px;
        }
        .feature-description {
            font-size: 14px;
            color: #718096;
            line-height: 1.4;
        }
        .category-actions {
            margin-bottom: 16px;
            display: flex;
            gap: 12px;
        }
        .action-btn {
            padding: 6px 12px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .action-btn:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="feature-picker">
        <h1 style="text-align: center; color: #2d3748; margin-bottom: 32px;">Select Your HexStrike AI Features</h1>
        
        <!-- Data Scraping & Intelligence -->
        <div class="category-group">
            <div class="category-header" onclick="toggleCategory('scraping')">
                <span>🔍 Data Scraping & Intelligence</span>
                <span class="toggle-icon" id="scraping-icon">▼</span>
            </div>
            <div class="category-content" id="scraping-content">
                <div class="category-actions">
                    <button class="action-btn" onclick="selectAll('scraping')">Select All</button>
                    <button class="action-btn" onclick="clearAll('scraping')">Clear All</button>
                </div>
                <div class="feature-grid">
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="point-click-selectors">
                        <label class="feature-label" for="point-click-selectors">
                            <div class="feature-name">Point-and-Click Selectors</div>
                            <div class="feature-description">Visual element selection for precise data extraction</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="auto-pagination">
                        <label class="feature-label" for="auto-pagination">
                            <div class="feature-name">Auto-Pagination Detection</div>
                            <div class="feature-description">Automatic navigation through multi-page content</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="file-extraction">
                        <label class="feature-label" for="file-extraction">
                            <div class="feature-name">Multi-Format File Extraction</div>
                            <div class="feature-description">PDF, DOCX, XLSX, HTML, CSV, JSON support</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="auth-support">
                        <label class="feature-label" for="auth-support">
                            <div class="feature-name">Authentication Support</div>
                            <div class="feature-description">Username/password and token-based authentication</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="real-time-scraping">
                        <label class="feature-label" for="real-time-scraping">
                            <div class="feature-name">Real-Time Scraping</div>
                            <div class="feature-description">Live data extraction with WebSocket connections</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="ai-content-analysis">
                        <label class="feature-label" for="ai-content-analysis">
                            <div class="feature-name">AI Content Analysis</div>
                            <div class="feature-description">Intelligent content categorization and sentiment analysis</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="metadata-extraction">
                        <label class="feature-label" for="metadata-extraction">
                            <div class="feature-name">Metadata Extraction</div>
                            <div class="feature-description">File properties and hidden information discovery</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox scraping-feature" id="export-options">
                        <label class="feature-label" for="export-options">
                            <div class="feature-name">Advanced Export Options</div>
                            <div class="feature-description">CSV/JSON export with database integration</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Testing -->
        <div class="category-group">
            <div class="category-header" onclick="toggleCategory('security')">
                <span>🛡️ Security Testing</span>
                <span class="toggle-icon" id="security-icon">▼</span>
            </div>
            <div class="category-content" id="security-content">
                <div class="category-actions">
                    <button class="action-btn" onclick="selectAll('security')">Select All</button>
                    <button class="action-btn" onclick="clearAll('security')">Clear All</button>
                </div>
                <div class="feature-grid">
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="network-scanning">
                        <label class="feature-label" for="network-scanning">
                            <div class="feature-name">Advanced Network Scanning</div>
                            <div class="feature-description">Nmap, Masscan, Rustscan for comprehensive discovery</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="vulnerability-scanning">
                        <label class="feature-label" for="vulnerability-scanning">
                            <div class="feature-name">Vulnerability Scanning</div>
                            <div class="feature-description">Nuclei-powered automated vulnerability detection</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="web-app-testing">
                        <label class="feature-label" for="web-app-testing">
                            <div class="feature-name">Web Application Testing</div>
                            <div class="feature-description">OWASP Top 10 checks and custom payload testing</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="sql-injection">
                        <label class="feature-label" for="sql-injection">
                            <div class="feature-name">SQL Injection Testing</div>
                            <div class="feature-description">SQLMap automated and manual injection testing</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="directory-enumeration">
                        <label class="feature-label" for="directory-enumeration">
                            <div class="feature-name">Directory Enumeration</div>
                            <div class="feature-description">Gobuster and FFUF for comprehensive fuzzing</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="password-security">
                        <label class="feature-label" for="password-security">
                            <div class="feature-name">Password Security Testing</div>
                            <div class="feature-description">Hydra brute force and Hashcat hash cracking</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="binary-analysis">
                        <label class="feature-label" for="binary-analysis">
                            <div class="feature-name">Binary Analysis</div>
                            <div class="feature-description">Ghidra static and GDB dynamic analysis</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="cloud-security">
                        <label class="feature-label" for="cloud-security">
                            <div class="feature-name">Cloud Security Assessment</div>
                            <div class="feature-description">AWS/Azure/GCP and container vulnerability scanning</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox security-feature" id="osint-intelligence">
                        <label class="feature-label" for="osint-intelligence">
                            <div class="feature-name">OSINT Intelligence</div>
                            <div class="feature-description">Social media analysis and DNS enumeration</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Agents -->
        <div class="category-group">
            <div class="category-header" onclick="toggleCategory('ai-agents')">
                <span>🤖 AI Agents</span>
                <span class="toggle-icon" id="ai-agents-icon">▼</span>
            </div>
            <div class="category-content" id="ai-agents-content">
                <div class="category-actions">
                    <button class="action-btn" onclick="selectAll('ai-agents')">Select All</button>
                    <button class="action-btn" onclick="clearAll('ai-agents')">Clear All</button>
                </div>
                <div class="feature-grid">
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="bugbounty-agent">
                        <label class="feature-label" for="bugbounty-agent">
                            <div class="feature-name">BugBounty Agent</div>
                            <div class="feature-description">Automated vulnerability hunting workflows</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="ctf-solver">
                        <label class="feature-label" for="ctf-solver">
                            <div class="feature-name">CTF Solver Agent</div>
                            <div class="feature-description">Capture The Flag challenge automation</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="cve-intelligence">
                        <label class="feature-label" for="cve-intelligence">
                            <div class="feature-name">CVE Intelligence Agent</div>
                            <div class="feature-description">Vulnerability database analysis and monitoring</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="exploit-generator">
                        <label class="feature-label" for="exploit-generator">
                            <div class="feature-name">Exploit Generator Agent</div>
                            <div class="feature-description">Custom exploit development and payload creation</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="network-recon">
                        <label class="feature-label" for="network-recon">
                            <div class="feature-name">Network Reconnaissance Agent</div>
                            <div class="feature-description">Automated network mapping and enumeration</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="social-engineering">
                        <label class="feature-label" for="social-engineering">
                            <div class="feature-name">Social Engineering Agent</div>
                            <div class="feature-description">Phishing simulation and awareness training</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="compliance-assessment">
                        <label class="feature-label" for="compliance-assessment">
                            <div class="feature-name">Compliance Assessment Agent</div>
                            <div class="feature-description">Regulatory compliance checking and reporting</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox ai-agents-feature" id="incident-response">
                        <label class="feature-label" for="incident-response">
                            <div class="feature-name">Incident Response Agent</div>
                            <div class="feature-description">Automated threat hunting and investigation</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reporting & Visualization -->
        <div class="category-group">
            <div class="category-header" onclick="toggleCategory('reporting')">
                <span>📊 Reporting & Visualization</span>
                <span class="toggle-icon" id="reporting-icon">▼</span>
            </div>
            <div class="category-content" id="reporting-content">
                <div class="category-actions">
                    <button class="action-btn" onclick="selectAll('reporting')">Select All</button>
                    <button class="action-btn" onclick="clearAll('reporting')">Clear All</button>
                </div>
                <div class="feature-grid">
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="real-time-dashboards">
                        <label class="feature-label" for="real-time-dashboards">
                            <div class="feature-name">Real-Time Dashboards</div>
                            <div class="feature-description">Live progress tracking and historical trend analysis</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="export-formats">
                        <label class="feature-label" for="export-formats">
                            <div class="feature-name">Multiple Export Formats</div>
                            <div class="feature-description">PDF, HTML, CSV, JSON, and XML report generation</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="vulnerability-correlation">
                        <label class="feature-label" for="vulnerability-correlation">
                            <div class="feature-name">Vulnerability Correlation</div>
                            <div class="feature-description">Cross-tool analysis with intelligent risk scoring</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="visual-analytics">
                        <label class="feature-label" for="visual-analytics">
                            <div class="feature-name">Visual Analytics</div>
                            <div class="feature-description">Interactive charts, graphs, and network topology maps</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="custom-templates">
                        <label class="feature-label" for="custom-templates">
                            <div class="feature-name">Custom Report Templates</div>
                            <div class="feature-description">Executive summaries and technical deep-dive reports</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="compliance-reports">
                        <label class="feature-label" for="compliance-reports">
                            <div class="feature-name">Compliance Reports</div>
                            <div class="feature-description">OWASP, PCI, SOX, and industry standard reporting</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox reporting-feature" id="audit-trails">
                        <label class="feature-label" for="audit-trails">
                            <div class="feature-name">Comprehensive Audit Trails</div>
                            <div class="feature-description">Activity logging and change tracking</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Process Management -->
        <div class="category-group">
            <div class="category-header" onclick="toggleCategory('process')">
                <span>⚙️ Process Management</span>
                <span class="toggle-icon" id="process-icon">▼</span>
            </div>
            <div class="category-content" id="process-content">
                <div class="category-actions">
                    <button class="action-btn" onclick="selectAll('process')">Select All</button>
                    <button class="action-btn" onclick="clearAll('process')">Clear All</button>
                </div>
                <div class="feature-grid">
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="ai-tool-selection">
                        <label class="feature-label" for="ai-tool-selection">
                            <div class="feature-name">AI-Powered Tool Selection</div>
                            <div class="feature-description">Automatic tool recommendation and optimization</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="auto-vulnerability-scanning">
                        <label class="feature-label" for="auto-vulnerability-scanning">
                            <div class="feature-name">Automated Vulnerability Scanning</div>
                            <div class="feature-description">Continuous monitoring and scheduled assessments</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="advanced-logging">
                        <label class="feature-label" for="advanced-logging">
                            <div class="feature-name">Advanced Logging System</div>
                            <div class="feature-description">Debug-level logging with performance monitoring</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="real-time-notifications">
                        <label class="feature-label" for="real-time-notifications">
                            <div class="feature-name">Real-Time Notifications</div>
                            <div class="feature-description">Instant alerts and batch notification systems</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="auto-update-tools">
                        <label class="feature-label" for="auto-update-tools">
                            <div class="feature-name">Auto-Update Management</div>
                            <div class="feature-description">Automatic tool updates with manual override options</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="proxy-configuration">
                        <label class="feature-label" for="proxy-configuration">
                            <div class="feature-name">Proxy Configuration</div>
                            <div class="feature-description">HTTP/HTTPS and SOCKS proxy support</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="rate-limiting">
                        <label class="feature-label" for="rate-limiting">
                            <div class="feature-name">Intelligent Rate Limiting</div>
                            <div class="feature-description">Conservative and aggressive scanning modes</div>
                        </label>
                    </div>
                    <div class="feature-item">
                        <input type="checkbox" class="feature-checkbox process-feature" id="resource-management">
                        <label class="feature-label" for="resource-management">
                            <div class="feature-name">Resource Management</div>
                            <div class="feature-description">CPU/Memory limits with performance optimization</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 32px;">
            <button class="action-btn" style="padding: 12px 24px; font-size: 16px; background: #667eea; color: white;" onclick="getSelectedFeatures()">Get Selected Features</button>
        </div>
    </div>

    <script>
        function toggleCategory(categoryId) {
            const content = document.getElementById(categoryId + '-content');
            const icon = document.getElementById(categoryId + '-icon');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.textContent = '▼';
            } else {
                content.classList.add('active');
                icon.textContent = '▲';
            }
        }

        function selectAll(categoryId) {
            const checkboxes = document.querySelectorAll('.' + categoryId + '-feature');
            checkboxes.forEach(checkbox => checkbox.checked = true);
        }

        function clearAll(categoryId) {
            const checkboxes = document.querySelectorAll('.' + categoryId + '-feature');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }

        function getSelectedFeatures() {
            const selected = {};
            const categories = ['scraping', 'security', 'ai-agents', 'reporting', 'process'];
            
            categories.forEach(category => {
                const checkboxes = document.querySelectorAll('.' + category + '-feature:checked');
                selected[category] = Array.from(checkboxes).map(cb => ({
                    id: cb.id,
                    name: cb.nextElementSibling.querySelector('.feature-name').textContent,
                    description: cb.nextElementSibling.querySelector('.feature-description').textContent
                }));
            });
            
            console.log('Selected Features:', selected);
            alert('Selected features logged to console. Check developer tools.');
            return selected;
        }

        // Auto-expand first category on load
        document.addEventListener('DOMContentLoaded', function() {
            toggleCategory('scraping');
        });
    </script>
</body>
</html>