# HexStrike AI Security Audit - Hardened Dependencies
# Updated with pinned versions and security patches
# Split into API and Worker requirements for better security isolation

# ============================================================================
# CORE API FRAMEWORK DEPENDENCIES (PINNED VERSIONS)
# ============================================================================
flask==3.0.0                    # Web framework for API server
flask-cors==4.0.0               # CORS support with strict origin controls
flask-jwt-extended==4.6.0       # JWT authentication with 30min expiry
flask-limiter==3.5.0            # Rate limiting (60 req/min)
flask-sqlalchemy==3.1.1         # Database ORM
flask-migrate==4.0.5            # Database migrations
requests==2.31.0                # HTTP client library
psutil==5.9.6                   # System utilities
fastmcp==0.2.0                  # MCP framework
cryptography==41.0.7            # Cryptographic operations
gunicorn==21.2.0                # WSGI HTTP Server

# ============================================================================
# DATABASE & CACHING
# ============================================================================
psycopg2-binary==2.9.9          # PostgreSQL adapter
supabase==2.18.1                # Supabase Python client
redis==5.0.1                    # Redis client
rq==1.15.1                      # Redis Queue for background jobs
sqlalchemy==2.0.23              # SQL toolkit
alembic==1.12.1                 # Database migration tool

# ============================================================================
# VALIDATION & SERIALIZATION
# ============================================================================
pydantic==2.5.0                 # Data validation using Python type hints
marshmallow==3.20.1             # Object serialization/deserialization

# ============================================================================
# SECURITY & MONITORING
# ============================================================================
opentelemetry-api==1.21.0       # Observability framework
opentelemetry-sdk==1.21.0       # OpenTelemetry SDK
opentelemetry-instrumentation-flask==0.42b0  # Flask instrumentation
structlog==23.2.0               # Structured logging
python-json-logger==2.0.7       # JSON log formatter

# ============================================================================
# ASYNC & NETWORKING (API ONLY)
# ============================================================================
aiohttp==3.9.1                  # Async HTTP client

# ============================================================================
# WEB SCRAPING & AUTOMATION (MOVED TO WORKER)
# ============================================================================
beautifulsoup4==4.12.2          # HTML parsing (lightweight for API)

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================
pytest==7.4.3                   # Testing framework
pytest-cov==4.1.0               # Coverage reporting
pytest-asyncio==0.21.1          # Async testing support
bandit==1.7.5                   # Security linting
ruff==0.1.6                     # Fast Python linter
black==23.11.0                  # Code formatter
isort==5.12.0                   # Import sorting

# ============================================================================
# BINARY ANALYSIS (CONDITIONALLY USED - MOVED TO WORKER)
# ============================================================================
# These are moved to requirements-worker.txt for security isolation
# pwntools, angr, selenium, mitmproxy will be in worker container only

# ============================================================================
# EXTERNAL SECURITY TOOLS (150+ Tools - Install separately)
# ============================================================================
# 
# Chat Box v6.0 integrates with 150+ external security tools that must be
# installed separately from their official sources:
# 
# 🔍 Network & Reconnaissance (25+ tools):
# - nmap, masscan, rustscan, autorecon, amass, subfinder, fierce
# - dnsenum, theharvester, responder, netexec, enum4linux-ng
# 
# 🌐 Web Application Security (40+ tools):
# - gobuster, feroxbuster, ffuf, dirb, dirsearch, nuclei, nikto
# - sqlmap, wpscan, arjun, paramspider, x8, katana, httpx
# - dalfox, jaeles, hakrawler, gau, waybackurls, wafw00f
# 
# 🔐 Authentication & Password (12+ tools):
# - hydra, john, hashcat, medusa, patator, netexec
# - evil-winrm, hash-identifier, ophcrack
# 
# 🔬 Binary Analysis & Reverse Engineering (25+ tools):
# - ghidra, radare2, gdb, binwalk, ropgadget, checksec, strings
# - volatility3, foremost, steghide, exiftool, angr, pwntools
# 
# ☁️ Cloud & Container Security (20+ tools):
# - prowler, scout-suite, trivy, kube-hunter, kube-bench
# - docker-bench-security, checkov, terrascan, falco
# 
# 🏆 CTF & Forensics (20+ tools):
# - volatility3, autopsy, sleuthkit, stegsolve, zsteg, outguess
# - photorec, testdisk, scalpel, bulk-extractor
# 
# 🕵️ OSINT & Intelligence (20+ tools):
# - sherlock, social-analyzer, recon-ng, maltego, spiderfoot
# - shodan-cli, censys-cli, have-i-been-pwned
# 
# Installation Notes:
# 1. Kali Linux 2024.1+ includes most tools by default
# 2. Ubuntu/Debian users should install tools from official repositories
# 3. Some tools require compilation from source or additional setup
# 4. Cloud tools require API keys and authentication configuration
# 5. Browser Agent requires Chrome/Chromium and ChromeDriver installation
# 
# For complete installation instructions and setup guides, see README.md