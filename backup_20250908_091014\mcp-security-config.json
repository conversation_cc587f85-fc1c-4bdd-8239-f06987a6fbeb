{"mcpSecurityPolicy": {"version": "1.0", "description": "Security configuration for Chat Box AI MCP Server", "lastUpdated": "2024-01-15", "securityLevel": "HIGH", "globalSettings": {"enforceAuthentication": true, "requireTLS": true, "sessionTimeout": 3600, "maxConcurrentSessions": 5, "auditLogging": true, "sensitiveDataMasking": true}, "toolCategories": {"safe": {"description": "Safe reconnaissance tools with minimal risk", "tools": ["nmap_scan", "gobuster_scan", "nuclei_scan", "whatweb_scan", "httpx_scan", "subfinder_scan", "amass_scan", "dig_lookup", "whois_lookup"], "requireApproval": false, "maxExecutionTime": 300, "rateLimitPerHour": 50}, "moderate": {"description": "Moderate risk tools requiring approval", "tools": ["prowler_scan", "trivy_scan", "scout_suite_assessment", "cloudmapper_analysis", "nikto_scan", "wpscan_scan", "burp_scan", "zap_scan", "sqlmap_scan"], "requireApproval": true, "maxExecutionTime": 600, "rateLimitPerHour": 20}, "high_risk": {"description": "High-risk exploitation tools - blocked by default", "tools": ["pacu_exploitation", "metasploit_exploit", "empire_agent", "cobalt_strike", "<PERSON><PERSON><PERSON><PERSON>", "bloodhound", "crackmapexec", "kube_hunter_scan"], "requireApproval": true, "blocked": true, "maxExecutionTime": 1200, "rateLimitPerHour": 5}}, "networkSecurity": {"allowedTargets": {"internal": ["10.0.0.0/8", "**********/12", "***********/16"], "external": ["*"], "blocked": ["*********/8", "***********/16", "*********/4", "240.0.0.0/4"]}, "portRestrictions": {"allowedPorts": [80, 443, 8080, 8443, 3000, 5000], "blockedPorts": [22, 23, 135, 139, 445, 1433, 3389]}, "dnsFiltering": {"enabled": true, "blockedDomains": ["*.onion", "*.i2p", "malware-domain.com"]}}, "resourceLimits": {"perTool": {"maxMemoryMB": 512, "maxCpuPercent": 50, "maxExecutionTimeMs": 300000, "maxOutputSizeKB": 10240}, "perSession": {"maxMemoryMB": 1024, "maxConcurrentTools": 3, "maxToolsPerHour": 100}}, "complianceSettings": {"gdprCompliant": true, "hipaaCompliant": false, "sox404Compliant": true, "dataRetentionDays": 90, "encryptionRequired": true}, "alerting": {"enabled": true, "channels": ["email", "webhook"], "triggers": {"suspiciousActivity": true, "rateLimitExceeded": true, "blockedToolAttempt": true, "resourceLimitExceeded": true, "authenticationFailure": true}}, "emergencyControls": {"killSwitchEnabled": true, "emergencyContactEmail": "<EMAIL>", "autoShutdownOnSuspiciousActivity": true, "quarantineMode": {"enabled": false, "allowedTools": ["nmap_scan", "nuclei_scan"]}}}}