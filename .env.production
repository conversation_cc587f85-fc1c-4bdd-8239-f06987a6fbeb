# Production Environment Configuration for HexStrike AI

# Database Configuration (Supabase PostgreSQL)
SUPABASE_URL=https://lcwgdbheihfccapmnbsp.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxjd2dkYmhlaWhmY2NhcG1uYnNwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczMTA2NjMsImV4cCI6MjA3Mjg4NjY2M30.1QVuwfnU_Z2XOiwTXxgmLYU-FNxdYDToARC6pcrZokQ
# IMPORTANT: Set your actual service role key before deployment
SUPABASE_SERVICE_ROLE_KEY=YOUR_ACTUAL_SERVICE_ROLE_KEY_HERE
# IMPORTANT: Set your actual database password before deployment
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Redis Configuration (Production)
# IMPORTANT: Use a secure Redis instance for production
REDIS_PASSWORD=YOUR_SECURE_REDIS_PASSWORD_HERE
REDIS_URL=redis://:YOUR_SECURE_REDIS_PASSWORD_HERE@your-redis-host:6379/0

# Application Secrets (Production)
# IMPORTANT: Generate strong, unique secrets for production
JWT_SECRET_KEY=YOUR_SECURE_JWT_SECRET_KEY_HERE_MINIMUM_32_CHARACTERS
SECRET_KEY=YOUR_SECURE_FLASK_SECRET_KEY_HERE_MINIMUM_32_CHARACTERS

# Application Configuration (Production)
FLASK_ENV=production
FLASK_DEBUG=false
CHAT_BOX_PORT=8888
WORKER_CONCURRENCY=4
WORKER_TIMEOUT=600

# Security Configuration (Production)
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Strict

# Logging (Production)
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting (Production)
RATE_LIMIT_STORAGE_URL=redis://:YOUR_SECURE_REDIS_PASSWORD_HERE@your-redis-host:6379/1

# MCP Configuration (Production)
MCP_TIMEOUT=180
MCP_MAX_RETRIES=5
MCP_RATE_LIMIT=20

# Production flags
DEBUG=false
TESTING=false

# SSL Configuration (Production)
SSL_DISABLE=false
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# Python Configuration
PYTHONIOENCODING=utf-8
PYTHONPATH=.

# Additional Production Settings
WEB_CONCURRENCY=4
MAX_WORKERS=8
KEEP_ALIVE=2
TIMEOUT=120
BIND=0.0.0.0:8888

# Monitoring and Health Checks
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true
METRICS_PORT=9090

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
BACKUP_RETENTION_DAYS=30