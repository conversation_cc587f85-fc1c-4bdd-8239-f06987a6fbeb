/**
 * Chat Box AI Chat Interface - Custom Styles
 * Additional styling beyond Tailwind CSS
 */

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.dark ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes blink {
    0% { opacity: 0.1; }
    20% { opacity: 1; }
    100% { opacity: 0.1; }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-in-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.3s ease-in-out;
}

.animate-pulse {
    animation: pulse 1.5s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Typing indicator */
.typing-indicator span {
    animation: blink 1.4s infinite;
    animation-fill-mode: both;
    height: 5px;
    width: 5px;
    background-color: #b71c1c;
    display: inline-block;
    border-radius: 50%;
    margin: 0 1px;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

.dark .typing-indicator span {
    background-color: #ef4444;
}

/* Resizable panels */
.gutter {
    background-color: #edf2f7;
    background-repeat: no-repeat;
    background-position: 50%;
}

.dark .gutter {
    background-color: #2d3748;
}

.gutter.gutter-horizontal {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==');
    cursor: col-resize;
}

/* Message bubbles */
.message-bubble {
    position: relative;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background-color: #e3f2fd;
    color: #0d47a1;
    margin-left: auto;
}

.ai-message {
    background-color: #f5f5f5;
    color: #212121;
}

.dark .user-message {
    background-color: #1e3a8a;
    color: #e0e7ff;
}

.dark .ai-message {
    background-color: #374151;
    color: #e5e7eb;
}

/* Code blocks in messages */
.message-bubble pre {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
    padding: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0;
}

.dark .message-bubble pre {
    background-color: rgba(0, 0, 0, 0.3);
}

.message-bubble code {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Log levels */
.log-info {
    color: #2196f3;
}

.log-success {
    color: #4caf50;
}

.log-warning {
    color: #ff9800;
}

.log-error {
    color: #f44336;
}

.log-critical {
    color: #f44336;
    font-weight: bold;
}

.dark .log-info {
    color: #64b5f6;
}

.dark .log-success {
    color: #81c784;
}

.dark .log-warning {
    color: #ffb74d;
}

.dark .log-error, 
.dark .log-critical {
    color: #e57373;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .flex.h-screen {
        flex-direction: column;
    }
    
    #chatPanel, 
    #logsPanel {
        width: 100% !important;
        height: 50vh;
    }
    
    .gutter.gutter-horizontal {
        display: none;
    }
    
    .message-bubble {
        max-width: 90%;
    }
}

/* Tabs */
.tab {
    position: relative;
    cursor: pointer;
}

.tab.active {
    border-bottom: 2px solid #b71c1c;
    color: #b71c1c;
}

.dark .tab.active {
    border-bottom: 2px solid #ef4444;
    color: #ef4444;
}

.tab:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark .tab:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Form elements */
input:focus, 
select:focus, 
textarea:focus {
    outline: 2px solid #b71c1c;
    outline-offset: -1px;
}

.dark input:focus, 
.dark select:focus, 
.dark textarea:focus {
    outline: 2px solid #ef4444;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #b71c1c;
    color: white;
}

.btn-primary:hover {
    background-color: #9a0007;
}

.btn-secondary {
    background-color: #e0e0e0;
    color: #212121;
}

.btn-secondary:hover {
    background-color: #bdbdbd;
}

.dark .btn-secondary {
    background-color: #4b5563;
    color: #e5e7eb;
}

.dark .btn-secondary:hover {
    background-color: #374151;
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

.btn-warning {
    background-color: #ff9800;
    color: white;
}

.btn-warning:hover {
    background-color: #f57c00;
}

.btn-success {
    background-color: #4caf50;
    color: white;
}

.btn-success:hover {
    background-color: #388e3c;
}

.btn-icon {
    padding: 0.5rem;
    border-radius: 9999px;
}

/* Tooltips */
.tooltip {
    position: relative;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.25rem 0.5rem;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 0.25rem;
}

/* File upload */
.file-upload {
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.file-upload input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

/* Progress bar */
.progress-bar {
    height: 0.375rem;
    border-radius: 9999px;
    overflow: hidden;
    background-color: #e0e0e0;
}

.dark .progress-bar {
    background-color: #4b5563;
}

.progress-bar-fill {
    height: 100%;
    background-color: #4caf50;
    transition: width 0.3s ease;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-running {
    background-color: #4caf50;
    animation: pulse 1.5s infinite;
}

.status-paused {
    background-color: #ff9800;
}

.status-stopped {
    background-color: #f44336;
}

.status-completed {
    background-color: #2196f3;
}

/* Collapsible sections */
.collapsible {
    overflow: hidden;
    transition: max-height 0.3s ease;
}

/* Notification toast */
.toast {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    z-index: 50;
    animation: slideIn 0.3s ease-in-out;
}

.dark .toast {
    background-color: #1f2937;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* Error modal */
.modal {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    animation: fadeIn 0.3s ease-in-out;
}

.modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    width: 100%;
    max-width: 28rem;
    animation: slideIn 0.3s ease-in-out;
}

.dark .modal-content {
    background-color: #1f2937;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* Role switcher */
.role-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.5rem;
    cursor: pointer;
}

.role-badge i {
    margin-right: 0.25rem;
}

.role-badge.active {
    background-color: #b71c1c;
    color: white;
}

.dark .role-badge.active {
    background-color: #ef4444;
}

.role-badge:not(.active) {
    background-color: #e0e0e0;
    color: #212121;
}

.dark .role-badge:not(.active) {
    background-color: #4b5563;
    color: #e5e7eb;
}

/* Environment presets */
.environment-preset {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
}

.environment-preset:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark .environment-preset:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.environment-preset.active {
    background-color: rgba(183, 28, 28, 0.1);
    border-left: 3px solid #b71c1c;
}

.dark .environment-preset.active {
    background-color: rgba(239, 68, 68, 0.1);
    border-left: 3px solid #ef4444;
}

/* Metrics dashboard */
.metric-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.dark .metric-card {
    background-color: #1f2937;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 0.5rem;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.dark .metric-label {
    color: #9ca3af;
}

/* Request history */
.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    transition: background-color 0.2s;
}

.history-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark .history-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Mobile optimizations */
@media (max-width: 640px) {
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .metric-card {
        padding: 0.75rem;
    }
    
    .metric-value {
        font-size: 1.25rem;
    }
    
    .toast {
        max-width: 90%;
        left: 5%;
        right: 5%;
    }
}