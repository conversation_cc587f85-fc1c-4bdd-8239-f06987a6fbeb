﻿{
    "technology_fingerprinting":  {
                                      "content_length":  1256,
                                      "status":  "Basic fingerprinting completed",
                                      "javascript_frameworks":  "None",
                                      "cms_detected":  "None",
                                      "server_header":  "Not detected"
                                  },
    "timestamp":  "2025-09-12 21:40:47",
    "limitations":  [
                        "Limited to basic HTTP requests",
                        "No specialized webapp scanners available",
                        "Manual parameter discovery required"
                    ],
    "directory_discovery":  {
                                "status":  "No common paths discovered",
                                "forbidden_paths":  [

                                                    ],
                                "paths_tested":  [
                                                     "/robots.txt",
                                                     "/sitemap.xml",
                                                     "/admin",
                                                     "/login",
                                                     "/api",
                                                     "/wp-admin",
                                                     "/phpmyadmin",
                                                     "/.git",
                                                     "/.env",
                                                     "/config"
                                                 ],
                                "found_paths":  [

                                                ]
                            },
    "phase":  "Phase 2 - WebApp Mapping",
    "target":  "https://example.com"
}
