{"type": "Page", "id": "landing", "props": {"bg": "bg-gradient-to-b from-white to-slate-50", "dirSupport": true}, "children": [{"type": "Header", "id": "topnav", "props": {"sticky": true, "elevation": "rest", "border": "subtle"}, "children": [{"type": "Logo", "props": {"text": "HexStrike AI"}}, {"type": "NavLinks", "props": {"items": ["Features", "Pricing", "FAQ"]}}, {"type": "CTA", "props": {"text": "Get Started", "variant": "primary"}}]}, {"type": "Hero", "id": "hero", "props": {"title": "Choose your tool. Get instant results.", "subtitle": "One workspace for scraping, safe testing, crawling, and AI analysis—built for speed and clarity.", "primaryCta": "Start Free", "secondaryCta": "Watch Demo"}}, {"type": "ToolPicker", "id": "tools", "role": "tablist", "state": {"active": "scrape"}, "items": [{"key": "scrape", "title": "Scrape", "blurb": "Extract clean data from pages & files.", "icon": "grid"}, {"key": "test", "title": "Test", "blurb": "Run safe, allowlisted security checks.", "icon": "shield"}, {"key": "crawl", "title": "Crawl", "blurb": "Map sites with depth & rules.", "icon": "network"}, {"key": "analyze", "title": "Analyze", "blurb": "Turn raw data into insights.", "icon": "chart"}], "styles": {"card": {"radius": "2xl", "border": "subtle", "elevation": {"default": "rest", "hover": "hover", "active": "active"}, "transition": "fast", "raisedEdges": true}, "icon": {"size": 24, "container": {"size": 48, "radius": "xl", "border": "subtle"}}}, "interactions": {"onSelect": {"setState": {"path": "tools.state.active", "valueFrom": "item.key"}, "update": [{"componentId": "featuresPanel", "prop": "variant", "valueFrom": "item.key"}, {"componentId": "hero", "prop": "primaryCta", "valueFrom": "concat:Start ,item.title"}]}}}, {"type": "FeaturesPanel", "id": "featuresPanel", "variant": "scrape", "itemsByVariant": {"scrape": ["Point-and-click selectors, auto-pagination", "File scraping: PDF, DOCX, XLSX, HTML", "Export to CSV/JSON, save to DB", "Auth support (username/password, token)"], "test": ["Allowlisted targets with safe presets", "Rate limits + audit logs", "OWASP quick checks", "Result diff & remediation tips"], "crawl": ["Depth, robots, and sitemaps aware", "Queue & retry policies", "Rule builder (include/exclude)", "Live progress with pause/resume"], "analyze": ["AI summaries & insight cards", "Entity/metric extraction", "Charts & downloadable reports", "Project reports"]}}, {"type": "Footer", "id": "footer", "props": {"links": ["Privacy", "Terms", "Contact"]}}]}