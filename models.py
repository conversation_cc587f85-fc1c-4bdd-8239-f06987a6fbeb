#!/usr/bin/env python3
"""
HexStrike AI - Database Models
SQLAlchemy models for users, jobs, reports, and audit logs
"""

import uuid
from datetime import datetime
from typing import Optional

from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy import Index

db = SQLAlchemy()

# ============================================================================
# USER MANAGEMENT MODELS
# ============================================================================

class User(db.Model):
    """User model with role-based access control"""
    __tablename__ = 'users'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='guest', index=True)
    
    # Profile information
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    organization = db.Column(db.String(100))
    
    # Account status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Security settings
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    
    # Relationships
    jobs = db.relationship('Job', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    reports = db.relationship('Report', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    audit_logs = db.relationship('AuditLog', backref='user', lazy='dynamic')
    
    def set_password(self, password: str) -> None:
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def is_locked(self) -> bool:
        """Check if account is locked"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False
    
    def lock_account(self, duration_minutes: int = 30) -> None:
        """Lock account for specified duration"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.failed_login_attempts += 1
    
    def unlock_account(self) -> None:
        """Unlock account and reset failed attempts"""
        self.locked_until = None
        self.failed_login_attempts = 0
    
    def to_dict(self) -> dict:
        """Convert to dictionary (excluding sensitive data)"""
        return {
            'id': str(self.id),
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'organization': self.organization,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'

# ============================================================================
# JOB MANAGEMENT MODELS
# ============================================================================

class Job(db.Model):
    """Background job model for async task execution"""
    __tablename__ = 'jobs'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False, index=True)
    
    # Job details
    job_type = db.Column(db.String(50), nullable=False, index=True)
    tool_name = db.Column(db.String(100), nullable=False, index=True)
    parameters = db.Column(JSONB, nullable=False, default=dict)
    
    # Job status
    status = db.Column(db.String(20), nullable=False, default='pending', index=True)
    priority = db.Column(db.String(10), nullable=False, default='normal', index=True)
    
    # Execution details
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    timeout_seconds = db.Column(db.Integer, default=120)
    
    # Results
    result = db.Column(JSONB)
    error_message = db.Column(db.Text)
    exit_code = db.Column(db.Integer)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Worker information
    worker_id = db.Column(db.String(100))
    queue_name = db.Column(db.String(50), default='default')
    
    # Relationships
    reports = db.relationship('Report', backref='job', lazy='dynamic')
    
    @property
    def duration(self) -> Optional[float]:
        """Calculate job duration in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def is_running(self) -> bool:
        """Check if job is currently running"""
        return self.status in ['pending', 'running']
    
    @property
    def is_completed(self) -> bool:
        """Check if job is completed (success or failure)"""
        return self.status in ['completed', 'failed', 'timeout']
    
    def mark_started(self) -> None:
        """Mark job as started"""
        self.status = 'running'
        self.started_at = datetime.utcnow()
    
    def mark_completed(self, result: dict = None, exit_code: int = 0) -> None:
        """Mark job as completed successfully"""
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
        self.result = result or {}
        self.exit_code = exit_code
    
    def mark_failed(self, error_message: str, exit_code: int = 1) -> None:
        """Mark job as failed"""
        self.status = 'failed'
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        self.exit_code = exit_code
    
    def mark_timeout(self) -> None:
        """Mark job as timed out"""
        self.status = 'timeout'
        self.completed_at = datetime.utcnow()
        self.error_message = f'Job timed out after {self.timeout_seconds} seconds'
        self.exit_code = 124  # Standard timeout exit code
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'job_type': self.job_type,
            'tool_name': self.tool_name,
            'parameters': self.parameters,
            'status': self.status,
            'priority': self.priority,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration': self.duration,
            'result': self.result,
            'error_message': self.error_message,
            'exit_code': self.exit_code,
            'created_at': self.created_at.isoformat(),
            'worker_id': self.worker_id,
            'queue_name': self.queue_name
        }
    
    def __repr__(self):
        return f'<Job {self.id} - {self.tool_name} ({self.status})>'

# ============================================================================
# REPORT MANAGEMENT MODELS
# ============================================================================

class Report(db.Model):
    """Security report model for storing scan results"""
    __tablename__ = 'reports'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False, index=True)
    job_id = db.Column(UUID(as_uuid=True), db.ForeignKey('jobs.id'), nullable=True, index=True)
    
    # Report metadata
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    report_type = db.Column(db.String(50), nullable=False, index=True)
    
    # Target information
    target = db.Column(db.String(500), nullable=False, index=True)
    target_type = db.Column(db.String(50))  # domain, ip, url, etc.
    
    # Report content
    findings = db.Column(JSONB, nullable=False, default=list)
    raw_output = db.Column(db.Text)
    summary = db.Column(JSONB)
    
    # Classification
    severity = db.Column(db.String(20), index=True)  # critical, high, medium, low, info
    confidence = db.Column(db.String(20))  # high, medium, low
    
    # Status
    status = db.Column(db.String(20), nullable=False, default='draft', index=True)
    is_public = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = db.Column(db.DateTime)
    
    # File attachments
    attachments = db.Column(JSONB, default=list)  # List of file paths/URLs
    
    @property
    def finding_count(self) -> int:
        """Count of findings in report"""
        return len(self.findings) if self.findings else 0
    
    @property
    def critical_findings(self) -> list:
        """Get critical severity findings"""
        if not self.findings:
            return []
        return [f for f in self.findings if f.get('severity') == 'critical']
    
    def add_finding(self, finding: dict) -> None:
        """Add a finding to the report"""
        if not self.findings:
            self.findings = []
        self.findings.append(finding)
        self.updated_at = datetime.utcnow()
    
    def publish(self) -> None:
        """Publish the report"""
        self.status = 'published'
        self.published_at = datetime.utcnow()
    
    def to_dict(self, include_raw=False) -> dict:
        """Convert to dictionary"""
        data = {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'job_id': str(self.job_id) if self.job_id else None,
            'title': self.title,
            'description': self.description,
            'report_type': self.report_type,
            'target': self.target,
            'target_type': self.target_type,
            'findings': self.findings,
            'summary': self.summary,
            'severity': self.severity,
            'confidence': self.confidence,
            'status': self.status,
            'is_public': self.is_public,
            'finding_count': self.finding_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'published_at': self.published_at.isoformat() if self.published_at else None,
            'attachments': self.attachments
        }
        
        if include_raw:
            data['raw_output'] = self.raw_output
        
        return data
    
    def __repr__(self):
        return f'<Report {self.id} - {self.title}>'

# ============================================================================
# AUDIT LOG MODELS
# ============================================================================

class AuditLog(db.Model):
    """Audit log model for security and compliance tracking"""
    __tablename__ = 'audit_logs'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=True, index=True)
    
    # Event details
    action = db.Column(db.String(100), nullable=False, index=True)
    resource_type = db.Column(db.String(50), index=True)
    resource_id = db.Column(db.String(100), index=True)
    
    # Request context
    ip_address = db.Column(db.String(45), index=True)  # IPv6 compatible
    user_agent = db.Column(db.String(500))
    correlation_id = db.Column(db.String(100), index=True)
    
    # Event data
    details = db.Column(JSONB)
    outcome = db.Column(db.String(20), index=True)  # success, failure, error
    
    # Timestamps
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # Security classification
    severity = db.Column(db.String(20), index=True)  # low, medium, high, critical
    category = db.Column(db.String(50), index=True)  # authentication, authorization, data_access, etc.
    
    @classmethod
    def log_event(cls, action: str, user_id: str = None, **kwargs) -> 'AuditLog':
        """Create and save an audit log entry"""
        log_entry = cls(
            action=action,
            user_id=user_id,
            **kwargs
        )
        db.session.add(log_entry)
        db.session.commit()
        return log_entry
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id) if self.user_id else None,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'correlation_id': self.correlation_id,
            'details': self.details,
            'outcome': self.outcome,
            'timestamp': self.timestamp.isoformat(),
            'severity': self.severity,
            'category': self.category
        }
    
    def __repr__(self):
        return f'<AuditLog {self.id} - {self.action}>'

# ============================================================================
# SESSION MANAGEMENT MODELS
# ============================================================================

class ChatSession(db.Model):
    """Chat session model for conversation history"""
    __tablename__ = 'chat_sessions'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False, index=True)
    
    # Session metadata
    title = db.Column(db.String(200))
    description = db.Column(db.Text)
    
    # Session data
    messages = db.Column(JSONB, nullable=False, default=list)
    context = db.Column(JSONB, default=dict)
    
    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def message_count(self) -> int:
        """Count of messages in session"""
        return len(self.messages) if self.messages else 0
    
    def add_message(self, role: str, content: str, metadata: dict = None) -> None:
        """Add a message to the session"""
        if not self.messages:
            self.messages = []
        
        message = {
            'id': str(uuid.uuid4()),
            'role': role,
            'content': content,
            'timestamp': datetime.utcnow().isoformat(),
            'metadata': metadata or {}
        }
        
        self.messages.append(message)
        self.last_activity = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'title': self.title,
            'description': self.description,
            'messages': self.messages,
            'context': self.context,
            'is_active': self.is_active,
            'message_count': self.message_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_activity': self.last_activity.isoformat()
        }
    
    def __repr__(self):
        return f'<ChatSession {self.id} - {self.title or "Untitled"}>'

# ============================================================================
# DATABASE INDEXES FOR PERFORMANCE
# ============================================================================

# Composite indexes for common queries
Index('idx_jobs_user_status', Job.user_id, Job.status)
Index('idx_jobs_created_status', Job.created_at, Job.status)
Index('idx_reports_user_type', Report.user_id, Report.report_type)
Index('idx_audit_logs_user_timestamp', AuditLog.user_id, AuditLog.timestamp)
Index('idx_audit_logs_action_timestamp', AuditLog.action, AuditLog.timestamp)
Index('idx_chat_sessions_user_active', ChatSession.user_id, ChatSession.is_active)