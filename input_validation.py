"""Input Validation and SQL Injection Protection Module
Provides comprehensive input validation, sanitization, and parameterized query helpers
to prevent SQL injection attacks across the HexStrike AI application.
"""

import re
import html
import json
import logging
from typing import Any, Dict, List, Optional, Union
from flask import request, jsonify
from functools import wraps
from sqlalchemy import text
from models import db

logger = logging.getLogger(__name__)

class InputValidator:
    """Comprehensive input validation and sanitization class"""
    
    # SQL injection patterns to detect and block
    SQL_INJECTION_PATTERNS = [
        r"('|(\-\-)|(;)|(\||\|)|(\*|\*))",
        r"(union|select|insert|delete|update|drop|create|alter|exec|execute)",
        r"(script|javascript|vbscript|onload|onerror|onclick)",
        r"(\<|\>|\&|\#|\%|\+|\=)",
        r"(information_schema|sysobjects|syscolumns)",
        r"(waitfor|delay|benchmark|sleep)",
        r"(xp_|sp_|fn_|@@)",
        r"(char|ascii|substring|concat|cast|convert)"
    ]
    
    # XSS patterns to detect and block
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
        r"<link[^>]*>",
        r"<meta[^>]*>"
    ]
    
    # Command injection patterns
    COMMAND_INJECTION_PATTERNS = [
        r"(;|\||&|\$|`|\(|\)|\{|\})",
        r"(rm|del|format|shutdown|reboot)",
        r"(cat|type|more|less|head|tail)",
        r"(wget|curl|nc|netcat|telnet)",
        r"(chmod|chown|sudo|su)"
    ]
    
    @staticmethod
    def validate_string(value: str, max_length: int = 255, allow_html: bool = False) -> tuple[bool, str]:
        """Validate and sanitize string input"""
        if not isinstance(value, str):
            return False, "Input must be a string"
        
        # Check length
        if len(value) > max_length:
            return False, f"Input exceeds maximum length of {max_length} characters"
        
        # Check for SQL injection patterns
        for pattern in InputValidator.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"SQL injection attempt detected: {value[:50]}...")
                return False, "Potentially malicious input detected"
        
        # Check for XSS patterns if HTML is not allowed
        if not allow_html:
            for pattern in InputValidator.XSS_PATTERNS:
                if re.search(pattern, value, re.IGNORECASE):
                    logger.warning(f"XSS attempt detected: {value[:50]}...")
                    return False, "Potentially malicious script detected"
        
        # Check for command injection patterns
        for pattern in InputValidator.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Command injection attempt detected: {value[:50]}...")
                return False, "Potentially malicious command detected"
        
        return True, "Valid"
    
    @staticmethod
    def sanitize_string(value: str, escape_html: bool = True) -> str:
        """Sanitize string input by escaping dangerous characters"""
        if not isinstance(value, str):
            return str(value)
        
        # HTML escape if requested
        if escape_html:
            value = html.escape(value)
        
        # Remove null bytes
        value = value.replace('\x00', '')
        
        # Normalize whitespace
        value = re.sub(r'\s+', ' ', value).strip()
        
        return value
    
    @staticmethod
    def validate_integer(value: Any, min_val: int = None, max_val: int = None) -> tuple[bool, Union[int, str]]:
        """Validate integer input"""
        try:
            int_val = int(value)
            
            if min_val is not None and int_val < min_val:
                return False, f"Value must be at least {min_val}"
            
            if max_val is not None and int_val > max_val:
                return False, f"Value must be at most {max_val}"
            
            return True, int_val
        except (ValueError, TypeError):
            return False, "Invalid integer value"
    
    @staticmethod
    def validate_username(username: str) -> tuple[bool, str]:
        """Validate username format"""
        if not isinstance(username, str):
            return False, "Username must be a string"
        
        # Username should be alphanumeric with underscores and hyphens
        username_pattern = r'^[a-zA-Z0-9_-]+$'
        
        if not re.match(username_pattern, username):
            return False, "Username can only contain letters, numbers, underscores, and hyphens"
        
        if len(username) < 3:
            return False, "Username must be at least 3 characters long"
        
        if len(username) > 50:
            return False, "Username must be less than 50 characters long"
        
        return True, "Valid username"
    
    @staticmethod
    def validate_email(email: str) -> tuple[bool, str]:
        """Validate email format"""
        if not isinstance(email, str):
            return False, "Email must be a string"
        
        # Basic email regex pattern
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(email_pattern, email):
            return False, "Invalid email format"
        
        if len(email) > 254:  # RFC 5321 limit
            return False, "Email address too long"
        
        return True, "Valid email"
    
    @staticmethod
    def validate_username(username: str) -> tuple[bool, str]:
        """Validate username format"""
        if not isinstance(username, str):
            return False, "Username must be a string"
        
        # Username requirements: 3-30 chars, alphanumeric + underscore/hyphen
        if not re.match(r'^[a-zA-Z0-9_-]{3,30}$', username):
            return False, "Username must be 3-30 characters, alphanumeric with underscore/hyphen only"
        
        return True, "Valid username"
    
    @staticmethod
    def validate_url(url: str) -> tuple[bool, str]:
        """Validate URL format"""
        if not isinstance(url, str):
            return False, "URL must be a string"
        
        # Basic URL validation
        url_pattern = r'^https?://[a-zA-Z0-9.-]+(?:\.[a-zA-Z]{2,})?(?:/.*)?$'
        
        if not re.match(url_pattern, url):
            return False, "Invalid URL format"
        
        if len(url) > 2048:  # Common URL length limit
            return False, "URL too long"
        
        return True, "Valid URL"

class SQLSafeQuery:
    """Safe SQL query execution with parameterized queries"""
    
    @staticmethod
    def execute_safe_query(query: str, params: Dict[str, Any] = None) -> Any:
        """Execute a parameterized SQL query safely"""
        try:
            if params is None:
                params = {}
            
            # Log the query for debugging (without sensitive data)
            logger.debug(f"Executing safe query: {query[:100]}...")
            
            # Use SQLAlchemy's text() with bound parameters
            result = db.session.execute(text(query), params)
            
            return result
        except Exception as e:
            logger.error(f"Safe query execution failed: {str(e)}")
            raise
    
    @staticmethod
    def safe_user_lookup(username: str) -> Optional[Any]:
        """Safely lookup user by username using parameterized query"""
        try:
            # Validate username first
            is_valid, message = InputValidator.validate_username(username)
            if not is_valid:
                logger.warning(f"Invalid username in lookup: {username}")
                return None
            
            # Use SQLAlchemy ORM (automatically parameterized)
            from models import User
            user = User.query.filter_by(username=username, is_active=True).first()
            
            return user
        except Exception as e:
            logger.error(f"Safe user lookup failed: {str(e)}")
            return None
    
    @staticmethod
    def safe_select(table: str, conditions: dict = None, limit: int = None) -> Any:
        """Perform a safe SELECT query"""
        try:
            query = f"SELECT * FROM {table}"
            params = {}
            
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    # Validate column name to prevent injection
                    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                        raise ValueError(f"Invalid column name: {key}")
                    
                    where_clauses.append(f"{key} = :{key}")
                    params[key] = value
                
                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)
            
            if limit:
                query += f" LIMIT {int(limit)}"
            
            return SQLSafeQuery.execute_safe_query(query, params)
        except Exception as e:
            logger.error(f"Safe select error: {str(e)}")
            raise

def validate_json_input(required_fields: List[str] = None, optional_fields: List[str] = None):
    """Decorator to validate JSON input for API endpoints"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get JSON data
                data = request.get_json()
                
                if data is None:
                    return jsonify({'error': 'No JSON data provided'}), 400
                
                if not isinstance(data, dict):
                    return jsonify({'error': 'JSON data must be an object'}), 400
                
                # Check required fields
                if required_fields:
                    for field in required_fields:
                        if field not in data:
                            return jsonify({'error': f'Missing required field: {field}'}), 400
                        
                        # Validate string fields
                        if isinstance(data[field], str):
                            is_valid, message = InputValidator.validate_string(data[field])
                            if not is_valid:
                                return jsonify({'error': f'Invalid {field}: {message}'}), 400
                            
                            # Sanitize the input
                            data[field] = InputValidator.sanitize_string(data[field])
                
                # Validate optional fields if present
                if optional_fields:
                    for field in optional_fields:
                        if field in data and isinstance(data[field], str):
                            is_valid, message = InputValidator.validate_string(data[field])
                            if not is_valid:
                                return jsonify({'error': f'Invalid {field}: {message}'}), 400
                            
                            # Sanitize the input
                            data[field] = InputValidator.sanitize_string(data[field])
                
                # Add validated data to request context
                request.validated_json = data
                
                return f(*args, **kwargs)
                
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid JSON format'}), 400
            except Exception as e:
                logger.error(f"Input validation error: {str(e)}")
                return jsonify({'error': 'Input validation failed'}), 500
        
        return decorated_function
    return decorator

def validate_query_params(allowed_params: List[str] = None):
    """Decorator to validate query parameters"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Validate query parameters
                for param, value in request.args.items():
                    if allowed_params and param not in allowed_params:
                        return jsonify({'error': f'Invalid query parameter: {param}'}), 400
                    
                    # Validate parameter value
                    if isinstance(value, str):
                        is_valid, message = InputValidator.validate_string(value)
                        if not is_valid:
                            return jsonify({'error': f'Invalid parameter {param}: {message}'}), 400
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Query parameter validation error: {str(e)}")
                return jsonify({'error': 'Parameter validation failed'}), 500
        
        return decorated_function
    return decorator

# Security middleware for automatic input validation
def init_input_validation(app):
    """Initialize input validation middleware"""
    
    @app.before_request
    def validate_all_inputs():
        """Validate all incoming requests"""
        try:
            # Skip validation for certain endpoints
            skip_endpoints = ['/health', '/api/auth/verify', '/static']
            
            if any(request.path.startswith(endpoint) for endpoint in skip_endpoints):
                return
            
            # Validate Content-Type for POST/PUT requests
            if request.method in ['POST', 'PUT', 'PATCH']:
                if request.content_type and 'application/json' not in request.content_type:
                    if not request.path.startswith('/api/files/'):
                        return jsonify({'error': 'Content-Type must be application/json'}), 400
            
            # Log all requests for security monitoring
            logger.info(f"Request: {request.method} {request.path} from {request.remote_addr}")
            
        except Exception as e:
            logger.error(f"Input validation middleware error: {str(e)}")
            return jsonify({'error': 'Request validation failed'}), 500
    
    logger.info("Input validation middleware initialized")