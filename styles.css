/* HexStrike AI Styles */

/* Base styles */
:root {
  --primary-color: #e53e3e;
  --primary-hover: #c53030;
  --text-dark: #1a202c;
  --text-light: #f7fafc;
  --bg-light: #f7fafc;
  --bg-dark: #1a202c;
}

/* Tailwind-like utility classes */
.container {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
}

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.flex-grow { flex-grow: 1; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

.bg-white { background-color: white; }
.bg-gray-100 { background-color: #f7fafc; }
.bg-gray-700 { background-color: #4a5568; }
.bg-gray-800 { background-color: #2d3748; }
.bg-gray-900 { background-color: #1a202c; }
.bg-red-600 { background-color: #e53e3e; }

.text-white { color: white; }
.text-gray-300 { color: #e2e8f0; }
.text-gray-400 { color: #cbd5e0; }
.text-gray-500 { color: #a0aec0; }
.text-gray-600 { color: #718096; }
.text-gray-700 { color: #4a5568; }
.text-gray-800 { color: #2d3748; }
.text-gray-900 { color: #1a202c; }
.text-red-600 { color: #e53e3e; }
.text-green-600 { color: #38a169; }

.hover\:text-red-500:hover { color: #f56565; }
.hover\:text-red-600:hover { color: #e53e3e; }
.hover\:text-red-800:hover { color: #c53030; }
.hover\:bg-red-700:hover { background-color: #c53030; }
.hover\:bg-gray-100:hover { background-color: #f7fafc; }
.hover\:bg-gray-700:hover { background-color: #4a5568; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.m-3 { margin: 0.75rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.mt-10 { margin-top: 2.5rem; }

.w-full { width: 100%; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.max-w-3xl { max-width: 48rem; }
.min-h-screen { min-height: 100vh; }

.text-sm { font-size: 0.875rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.space-x-6 > * + * { margin-left: 1.5rem; }

.hidden { display: none; }
.block { display: block; }
.inline-flex { display: inline-flex; }

.transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

.text-center { text-align: center; }

/* Dark mode */
.dark .dark\:bg-gray-700 { background-color: #4a5568; }
.dark .dark\:bg-gray-800 { background-color: #2d3748; }
.dark .dark\:bg-gray-900 { background-color: #1a202c; }
.dark .dark\:text-white { color: white; }
.dark .dark\:text-gray-200 { color: #edf2f7; }
.dark .dark\:text-gray-300 { color: #e2e8f0; }
.dark .dark\:text-gray-400 { color: #cbd5e0; }
.dark .dark\:hover\:bg-gray-700:hover { background-color: #4a5568; }
.dark .dark\:hover\:text-red-500:hover { color: #f56565; }

/* Chat styles */
.chat-messages {
  height: 400px;
  overflow-y: auto;
  padding: 1rem;
}

.user-message .flex {
  justify-content: flex-end;
}

.user-message .bg-gray-100 {
  background-color: #ebf8ff;
}

.dark .user-message .bg-gray-700 {
  background-color: #2c5282;
}

/* Responsive */
@media (min-width: 768px) {
  .md\:flex { display: flex; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}