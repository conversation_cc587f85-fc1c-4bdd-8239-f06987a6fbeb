{"summary": {"total_tests": 18, "passed": 18, "failed": 0, "success_rate": 100.0, "timestamp": "2025-09-11T08:08:09.560925", "categories": {"Authentication": {"passed": 1, "total": 1}, "Core Features": {"passed": 1, "total": 1}, "Web Interface": {"passed": 3, "total": 3}, "Security Tools": {"passed": 5, "total": 5}, "AI Features": {"passed": 3, "total": 3}, "Workflow Features": {"passed": 3, "total": 3}, "File Operations": {"passed": 1, "total": 1}, "Database Features": {"passed": 1, "total": 1}}}, "detailed_results": [{"test": "Authentication", "success": true, "details": "Successfully authenticated", "response_time_ms": 2067.14, "timestamp": "2025-09-11T08:07:33.642183"}, {"test": "Features Discovery", "success": true, "details": "Found 2 feature categories", "response_time_ms": 2061.67, "timestamp": "2025-09-11T08:07:35.704269"}, {"test": "Main Interface", "success": true, "details": "HTML interface loaded", "response_time_ms": 2039.75, "timestamp": "2025-09-11T08:07:37.744808"}, {"test": "Chat Interface", "success": true, "details": "HTML interface loaded", "response_time_ms": 2032.93, "timestamp": "2025-09-11T08:07:39.778045"}, {"test": "Settings Interface", "success": true, "details": "HTML interface loaded", "response_time_ms": 2072.49, "timestamp": "2025-09-11T08:07:41.850818"}, {"test": "Tool: NMAP", "success": true, "details": "<PERSON><PERSON> executed successfully", "response_time_ms": 2185.5, "timestamp": "2025-09-11T08:07:44.036759"}, {"test": "Tool: NUCLEI", "success": true, "details": "<PERSON><PERSON> executed successfully", "response_time_ms": 2227.07, "timestamp": "2025-09-11T08:07:46.264129"}, {"test": "Tool: GOBUSTER", "success": true, "details": "Tool validates input (expected)", "response_time_ms": 2058.23, "timestamp": "2025-09-11T08:07:48.322605"}, {"test": "Tool: SQLMAP", "success": true, "details": "Tool validates input (expected)", "response_time_ms": 2082.22, "timestamp": "2025-09-11T08:07:50.405127"}, {"test": "Tool: NIKTO", "success": true, "details": "<PERSON><PERSON> executed successfully", "response_time_ms": 2166.56, "timestamp": "2025-09-11T08:07:52.571963"}, {"test": "AI Payload Generation", "success": true, "details": "AI feature accessible", "response_time_ms": 2074.54, "timestamp": "2025-09-11T08:07:54.646854"}, {"test": "Target Analysis", "success": true, "details": "AI feature accessible", "response_time_ms": 2589.74, "timestamp": "2025-09-11T08:07:57.236829"}, {"test": "Tool Selection", "success": true, "details": "Input validation working", "response_time_ms": 2032.69, "timestamp": "2025-09-11T08:07:59.269786"}, {"test": "Bug <PERSON>unty Recon", "success": true, "details": "Input validation working", "response_time_ms": 2048.87, "timestamp": "2025-09-11T08:08:01.319015"}, {"test": "Vulnerability Hunting", "success": true, "details": "Input validation working", "response_time_ms": 2061.3, "timestamp": "2025-09-11T08:08:03.380627"}, {"test": "CTF Tool Suggestion", "success": true, "details": "Input validation working", "response_time_ms": 2047.2, "timestamp": "2025-09-11T08:08:05.428077"}, {"test": "File Listing", "success": true, "details": "File operations accessible", "response_time_ms": 2058.13, "timestamp": "2025-09-11T08:08:07.486712"}, {"test": "Database Connection", "success": true, "details": "Status: success", "response_time_ms": 2071.16, "timestamp": "2025-09-11T08:08:09.558327"}]}