<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links a {
            color: #4ecdc4;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(78, 205, 196, 0.2);
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
            padding: 1rem;
        }
        
        .chat-header {
            text-align: center;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .chat-header h1 {
            color: #ff6b6b;
            margin-bottom: 0.5rem;
        }
        
        .chat-header p {
            color: #b0b0b0;
        }
        
        .chat-messages {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            overflow-y: auto;
            min-height: 400px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .message.user {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            margin-left: auto;
            text-align: right;
        }
        
        .message.ai {
            background: rgba(78, 205, 196, 0.2);
            border-left: 4px solid #4ecdc4;
        }
        
        .message-header {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-bottom: 0.5rem;
        }
        
        .chat-input-container {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 25px;
            padding: 1rem 1.5rem;
            color: #ffffff;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .chat-input:focus {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
        }
        
        .chat-input::placeholder {
            color: #888;
        }
        
        .send-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            border-radius: 25px;
            padding: 1rem 2rem;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }
        
        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .typing-indicator {
            display: none;
            padding: 1rem;
            color: #4ecdc4;
            font-style: italic;
        }
        
        .features-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #ff6b6b;
        }
        
        .features-info h3 {
            color: #ff6b6b;
            margin-bottom: 0.5rem;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #b0b0b0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🔥 HexStrike AI</div>
        <div class="nav-links">
            <a href="/">🏠 Home</a>
            <a href="/health">🔍 Health</a>
            <a href="/api">📚 API Docs</a>
        </div>
    </div>
    
    <div class="chat-container">
        <div class="chat-header">
            <h1>💬 AI Security Assistant</h1>
            <p>Ask me about penetration testing, security tools, or get help with your security research</p>
        </div>
        
        <div class="features-info">
            <h3>🚀 Available Commands & Features</h3>
            <div class="features-list">
                <div>• Network Scanning (nmap, rustscan)</div>
                <div>• Web Security Testing (nuclei, sqlmap)</div>
                <div>• Directory Enumeration (gobuster, ffuf)</div>
                <div>• Password Attacks (hydra, hashcat)</div>
                <div>• Binary Analysis (ghidra, radare2)</div>
                <div>• CTF Challenge Solving</div>
                <div>• Bug Bounty Workflows</div>
                <div>• AI-Powered Tool Selection</div>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message ai">
                <div class="message-header">HexStrike AI Assistant</div>
                <div>👋 Welcome to HexStrike AI! I'm your AI-powered security testing assistant. I can help you with:</div>
                <br>
                <div>🔍 <strong>Security Scanning:</strong> Run nmap, nuclei, or other security tools</div>
                <div>🌐 <strong>Web Testing:</strong> Directory enumeration, SQL injection testing, XSS detection</div>
                <div>🔐 <strong>Password Attacks:</strong> Brute force attacks, hash cracking</div>
                <div>🏆 <strong>CTF Challenges:</strong> Binary analysis, cryptography, forensics</div>
                <div>🎯 <strong>Bug Bounty:</strong> Automated reconnaissance and vulnerability hunting</div>
                <br>
                <div>Just type your request below, and I'll help you choose the right tools and parameters!</div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            🤖 HexStrike AI is thinking...
        </div>
        
        <div class="chat-input-container">
            <input type="text" class="chat-input" id="chatInput" placeholder="Ask me about security testing, tools, or techniques..." maxlength="1000">
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">🚀 Send</button>
        </div>
    </div>
    
    <script>
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        
        // Handle Enter key press
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            chatInput.value = '';
            
            // Show typing indicator
            showTyping(true);
            sendBtn.disabled = true;
            
            try {
                // Send to API
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: 'web-chat-' + Date.now(),
                        user_id: 'web-user'
                    })
                });
                
                const data = await response.json();
                
                // Add AI response
                if (data.response) {
                    addMessage(data.response, 'ai');
                } else if (data.error) {
                    addMessage('❌ Error: ' + data.message, 'ai');
                } else {
                    addMessage('❌ Sorry, I encountered an unexpected error. Please try again.', 'ai');
                }
            } catch (error) {
                addMessage('❌ Connection error. Please check if the server is running and try again.', 'ai');
                console.error('Chat error:', error);
            }
            
            // Hide typing indicator
            showTyping(false);
            sendBtn.disabled = false;
            chatInput.focus();
        }
        
        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const headerDiv = document.createElement('div');
            headerDiv.className = 'message-header';
            headerDiv.textContent = sender === 'user' ? 'You' : 'HexStrike AI Assistant';
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = text;
            
            messageDiv.appendChild(headerDiv);
            messageDiv.appendChild(contentDiv);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function showTyping(show) {
            typingIndicator.style.display = show ? 'block' : 'none';
            if (show) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }
        
        // Focus on input when page loads
        window.addEventListener('load', function() {
            chatInput.focus();
        });
    </script>
</body>
</html>