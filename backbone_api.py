#!/usr/bin/env python3
"""
Backbone API Module
Provides admin-only endpoints for system health checks and comprehensive self-testing.
"""

import os
import time
import json
import asyncio
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse, urljoin
from urllib.robotparser import RobotFileParser

from flask import Blueprint, request, jsonify, g
from bs4 import BeautifulSoup

# Import our security and pentest modules
from backbone_security import (
    require_admin, rate_limit_admin, with_idempotency,
    security_manager, validate_allowlist_urls, get_security_status
)
from safe_pentest import run_safe_pentest, safe_pentest_engine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Blueprint
backbone_bp = Blueprint('backbone', __name__, url_prefix='/api/backbone')

class BackboneHealthChecker:
    """System health checker for backbone services"""
    
    def __init__(self):
        self.start_time = time.time()
        
    def get_uptime_seconds(self) -> int:
        """Get system uptime in seconds"""
        return int(time.time() - self.start_time)
    
    def check_database(self) -> Dict[str, Any]:
        """Check database connectivity and response time"""
        start_time = time.time()
        
        try:
            # Import here to avoid circular imports
            from models import db
            
            # Simple query to test DB connectivity
            result = db.session.execute('SELECT 1').fetchone()
            rtt_ms = int((time.time() - start_time) * 1000)
            
            return {
                'ok': True,
                'rttMs': rtt_ms,
                'status': 'connected'
            }
        except Exception as e:
            rtt_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Database health check failed: {e}")
            return {
                'ok': False,
                'rttMs': rtt_ms,
                'status': 'error',
                'error': str(e)
            }
    
    def check_queue(self) -> Dict[str, Any]:
        """Check queue system status"""
        try:
            # Import here to avoid circular imports
            from queue_manager import queue_manager
            
            # Get queue statistics
            stats = queue_manager.get_stats() if hasattr(queue_manager, 'get_stats') else {}
            
            return {
                'ok': True,
                'pending': stats.get('pending', 0),
                'active': stats.get('active', 0),
                'status': 'operational'
            }
        except Exception as e:
            logger.error(f"Queue health check failed: {e}")
            return {
                'ok': False,
                'pending': 0,
                'active': 0,
                'status': 'error',
                'error': str(e)
            }
    
    def check_storage(self) -> Dict[str, Any]:
        """Check storage system status"""
        try:
            # Check if we can write to the current directory
            test_file = 'backbone_health_test.tmp'
            with open(test_file, 'w') as f:
                f.write('health_check')
            
            # Clean up
            os.remove(test_file)
            
            return {
                'ok': True,
                'status': 'writable'
            }
        except Exception as e:
            logger.error(f"Storage health check failed: {e}")
            return {
                'ok': False,
                'status': 'error',
                'error': str(e)
            }

class BackboneSelfTester:
    """Comprehensive self-testing system"""
    
    def __init__(self):
        self.timeout_seconds = 20  # Hard cap per module
        
    async def test_scrape_module(self, url: str) -> Dict[str, Any]:
        """Test web scraping functionality"""
        start_time = time.time()
        result = {
            'ok': False,
            'url': url,
            'bytes': 0,
            'h1': None,
            'tableRows': 0,
            'elapsed_ms': 0,
            'error': None
        }
        
        try:
            # Validate URL against allowlist
            if not security_manager.validate_host_allowlist(url):
                raise ValueError(f"URL not in allowlist: {url}")
            
            # Perform HTTP request with timeout
            response = requests.get(url, timeout=self.timeout_seconds, headers={
                'User-Agent': 'HexStrike-Backbone-Checker/1.0'
            })
            response.raise_for_status()
            
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # Extract metrics
            h1_tag = soup.find('h1')
            table_rows = len(soup.find_all('tr'))
            
            result.update({
                'ok': True,
                'bytes': len(content),
                'h1': h1_tag.get_text().strip() if h1_tag else None,
                'tableRows': table_rows,
                'statusCode': response.status_code,
                'contentType': response.headers.get('Content-Type', 'unknown')
            })
            
            logger.info(f"Scrape test successful for {url}: {len(content)} bytes")
            
        except requests.exceptions.Timeout:
            result['error'] = f'Request timeout after {self.timeout_seconds}s'
        except requests.exceptions.RequestException as e:
            result['error'] = f'Request error: {str(e)}'
        except Exception as e:
            result['error'] = f'Scrape test error: {str(e)}'
            logger.error(f"Scrape test error for {url}: {e}")
        
        result['elapsed_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    async def test_crawl_module(self, url: str) -> Dict[str, Any]:
        """Test web crawling functionality with robots.txt respect"""
        start_time = time.time()
        result = {
            'ok': False,
            'url': url,
            'pages': 0,
            'depthReached': 0,
            'respectedRobots': True,
            'elapsed_ms': 0,
            'error': None
        }
        
        try:
            # Validate URL against allowlist
            if not security_manager.validate_host_allowlist(url):
                raise ValueError(f"URL not in allowlist: {url}")
            
            # Check robots.txt
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            rp = RobotFileParser()
            rp.set_url(robots_url)
            
            try:
                rp.read()
                can_fetch = rp.can_fetch('*', url)
                if not can_fetch:
                    result.update({
                        'ok': False,
                        'error': 'Crawling disallowed by robots.txt',
                        'respectedRobots': True
                    })
                    return result
            except Exception:
                # If robots.txt is not accessible, proceed with caution
                logger.info(f"Could not read robots.txt for {parsed_url.netloc}")
            
            # Perform limited crawl (max 3 pages for testing)
            visited_urls = set()
            to_visit = [url]
            max_pages = 3
            max_depth = 2
            current_depth = 0
            
            while to_visit and len(visited_urls) < max_pages and current_depth <= max_depth:
                current_url = to_visit.pop(0)
                
                if current_url in visited_urls:
                    continue
                
                try:
                    response = requests.get(current_url, timeout=5, headers={
                        'User-Agent': 'HexStrike-Backbone-Checker/1.0'
                    })
                    response.raise_for_status()
                    
                    visited_urls.add(current_url)
                    
                    # Extract links for next depth (limited)
                    if current_depth < max_depth:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        links = soup.find_all('a', href=True)
                        
                        for link in links[:5]:  # Limit to 5 links per page
                            href = link['href']
                            absolute_url = urljoin(current_url, href)
                            
                            # Only follow same-domain links
                            if urlparse(absolute_url).netloc == parsed_url.netloc:
                                if absolute_url not in visited_urls and absolute_url not in to_visit:
                                    to_visit.append(absolute_url)
                
                except Exception as e:
                    logger.warning(f"Error crawling {current_url}: {e}")
                    continue
                
                current_depth += 1
            
            result.update({
                'ok': True,
                'pages': len(visited_urls),
                'depthReached': current_depth,
                'respectedRobots': True,
                'visitedUrls': list(visited_urls)[:5]  # Return first 5 for reference
            })
            
            logger.info(f"Crawl test successful for {url}: {len(visited_urls)} pages")
            
        except Exception as e:
            result['error'] = f'Crawl test error: {str(e)}'
            logger.error(f"Crawl test error for {url}: {e}")
        
        result['elapsed_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    async def test_analyze_module(self, content: str) -> Dict[str, Any]:
        """Test content analysis functionality"""
        start_time = time.time()
        result = {
            'ok': False,
            'summaryLen': 0,
            'entities': [],
            'elapsed_ms': 0,
            'error': None
        }
        
        try:
            # Simple analysis - extract key information
            words = content.split()
            sentences = content.split('.')
            
            # Generate simple summary (first 200 chars)
            summary = content[:200].strip()
            if len(content) > 200:
                summary += '...'
            
            # Extract potential entities (simple approach)
            entities = []
            for word in words[:50]:  # Limit processing
                if word.istitle() and len(word) > 3:
                    entities.append(word.strip('.,!?;:'))
            
            # Remove duplicates and limit
            entities = list(set(entities))[:10]
            
            result.update({
                'ok': True,
                'summaryLen': len(summary),
                'summary': summary,
                'entities': entities,
                'wordCount': len(words),
                'sentenceCount': len(sentences),
                'characterCount': len(content)
            })
            
            logger.info(f"Analysis test successful: {len(words)} words, {len(entities)} entities")
            
        except Exception as e:
            result['error'] = f'Analysis test error: {str(e)}'
            logger.error(f"Analysis test error: {e}")
        
        result['elapsed_ms'] = int((time.time() - start_time) * 1000)
        return result
    
    async def run_comprehensive_test(self, targets: Dict[str, str]) -> Dict[str, Any]:
        """Run comprehensive self-test of all modules"""
        start_time = time.time()
        logs = []
        
        def log_step(step: str, note: str = ''):
            elapsed = int((time.time() - start_time) * 1000)
            logs.append({'step': step, 'ms': elapsed, 'note': note})
            logger.info(f"Self-test step: {step} ({elapsed}ms) - {note}")
        
        log_step('start', 'Beginning comprehensive self-test')
        
        # Extract targets
        scrape_url = targets.get('scrapeUrl', 'https://example.com')
        crawl_url = targets.get('crawlUrl', 'https://example.com')
        test_host = targets.get('testHost', 'example.com')
        
        # Validate all URLs against allowlist
        url_validation = validate_allowlist_urls([scrape_url, crawl_url])
        blocked_urls = [url for url, valid in url_validation.items() if not valid]
        
        if blocked_urls:
            log_step('validation_failed', f'Blocked URLs: {blocked_urls}')
            return {
                'error': f'URLs not in allowlist: {blocked_urls}',
                'logs': logs,
                'elapsedMs': int((time.time() - start_time) * 1000)
            }
        
        log_step('validation_passed', 'All URLs validated against allowlist')
        
        results = {}
        
        try:
            # Apply overall timeout
            async with asyncio.timeout(60):  # 1 minute total timeout
                
                # 1. Scrape Test
                log_step('scrape_start', f'Testing scrape module with {scrape_url}')
                results['scrape'] = await self.test_scrape_module(scrape_url)
                log_step('scrape_complete', f"Scrape result: {results['scrape']['ok']}")
                
                # 2. Crawl Test
                log_step('crawl_start', f'Testing crawl module with {crawl_url}')
                results['crawl'] = await self.test_crawl_module(crawl_url)
                log_step('crawl_complete', f"Crawl result: {results['crawl']['ok']}")
                
                # 3. Safe Pentest
                log_step('pentest_start', f'Testing safe pentest with {test_host}')
                results['safe_pentest'] = await run_safe_pentest(test_host)
                log_step('pentest_complete', f"Pentest result: {results['safe_pentest']['ok']}")
                
                # 4. Analysis Test (use scraped content if available)
                log_step('analyze_start', 'Testing analyze module')
                test_content = "This is a test content for analysis. It contains multiple sentences. Some words are Capitalized for entity extraction."
                
                # Use scraped content if available
                if results['scrape']['ok'] and 'content' in results['scrape']:
                    test_content = results['scrape']['content'][:1000]  # Limit content
                
                results['analyze'] = await self.test_analyze_module(test_content)
                log_step('analyze_complete', f"Analysis result: {results['analyze']['ok']}")
        
        except asyncio.TimeoutError:
            log_step('timeout', 'Comprehensive test timed out')
            results['error'] = 'Test suite timeout'
        except Exception as e:
            log_step('error', f'Test suite error: {str(e)}')
            results['error'] = str(e)
        
        total_elapsed = int((time.time() - start_time) * 1000)
        log_step('complete', f'Self-test completed in {total_elapsed}ms')
        
        # Add summary
        results['logs'] = logs
        results['elapsedMs'] = total_elapsed
        results['summary'] = {
            'totalTests': 4,
            'passedTests': sum(1 for key in ['scrape', 'crawl', 'safe_pentest', 'analyze'] 
                             if results.get(key, {}).get('ok', False)),
            'timestamp': datetime.utcnow().isoformat() + 'Z'
        }
        
        return results

# Global instances
health_checker = BackboneHealthChecker()
self_tester = BackboneSelfTester()

@backbone_bp.route('/health', methods=['GET'])
@require_admin
def get_health():
    """Get system health status"""
    start_time = time.time()
    
    try:
        health_data = {
            'ok': True,
            'version': '1.0.0',
            'uptimeSec': health_checker.get_uptime_seconds(),
            'nowISO': datetime.utcnow().isoformat() + 'Z',
            'db': health_checker.check_database(),
            'queue': health_checker.check_queue(),
            'storage': health_checker.check_storage(),
            'modules': ['scrape', 'crawl', 'safe_pentest', 'analyze'],
            'security': get_security_status(),
            'pentest_engine': safe_pentest_engine.get_engine_status()
        }
        
        # Overall health based on critical components
        critical_ok = (
            health_data['db']['ok'] and
            health_data['storage']['ok']
        )
        health_data['ok'] = critical_ok
        
        elapsed_ms = int((time.time() - start_time) * 1000)
        
        # Audit the health check
        security_manager.audit_action(
            actor_id=g.user_id,
            action='health_check',
            target='/api/backbone/health',
            result='success' if critical_ok else 'degraded',
            elapsed_ms=elapsed_ms
        )
        
        return jsonify(health_data)
        
    except Exception as e:
        elapsed_ms = int((time.time() - start_time) * 1000)
        
        security_manager.audit_action(
            actor_id=g.user_id,
            action='health_check',
            target='/api/backbone/health',
            result='error',
            elapsed_ms=elapsed_ms
        )
        
        logger.error(f"Health check error: {e}")
        return jsonify({
            'ok': False,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat() + 'Z'
        }), 500

@backbone_bp.route('/self-test', methods=['POST'])
@require_admin
@rate_limit_admin(limit=5, window_minutes=1)
@with_idempotency
def run_self_test():
    """Run comprehensive self-test"""
    start_time = time.time()
    
    try:
        # Get request data
        data = request.get_json() or {}
        targets = data.get('targets', {})
        
        # Validate required targets
        required_keys = ['scrapeUrl', 'crawlUrl', 'testHost']
        missing_keys = [key for key in required_keys if not targets.get(key)]
        
        if missing_keys:
            return jsonify({
                'error': f'Missing required targets: {missing_keys}',
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }), 400
        
        # Run async self-test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(self_tester.run_comprehensive_test(targets))
        finally:
            loop.close()
        
        elapsed_ms = int((time.time() - start_time) * 1000)
        
        # Determine overall result
        overall_success = not result.get('error') and result.get('summary', {}).get('passedTests', 0) >= 3
        
        # Audit the self-test
        security_manager.audit_action(
            actor_id=g.user_id,
            action='self_test',
            target=f"scrape:{targets['scrapeUrl']}, crawl:{targets['crawlUrl']}, pentest:{targets['testHost']}",
            result='success' if overall_success else 'partial_failure',
            elapsed_ms=elapsed_ms
        )
        
        return jsonify(result)
        
    except Exception as e:
        elapsed_ms = int((time.time() - start_time) * 1000)
        
        security_manager.audit_action(
            actor_id=g.user_id,
            action='self_test',
            target='unknown',
            result='error',
            elapsed_ms=elapsed_ms
        )
        
        logger.error(f"Self-test error: {e}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat() + 'Z'
        }), 500

if __name__ == '__main__':
    # Test the backbone API components
    print("Backbone API Test")
    
    # Test health checker
    health = health_checker.check_storage()
    print(f"Storage health: {health}")
    
    # Test self-tester (async)
    async def test_self_tester():
        targets = {
            'scrapeUrl': 'https://example.com',
            'crawlUrl': 'https://example.com',
            'testHost': 'example.com'
        }
        result = await self_tester.run_comprehensive_test(targets)
        print(f"Self-test result: {result.get('summary', {})}")
    
    asyncio.run(test_self_tester())