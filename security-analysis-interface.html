<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Analysis Interface - HexStrike AI</title>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #166d67;
            --accent-color: #47b39d;
            --background-color: #f8f9fa;
            --card-background: #ffffff;
            --text-color: #333333;
            --border-color: #e0e0e0;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .security-form {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
        }
        
        button {
            background-color: var(--accent-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #3a9d8a;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .tool-card {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .tool-card h3 {
            margin-top: 0;
            color: var(--primary-color);
        }
        
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .tag {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            color: white;
        }
        
        .tag-blue { background-color: #4285f4; }
        .tag-green { background-color: #34a853; }
        .tag-red { background-color: #ea4335; }
        .tag-orange { background-color: #fbbc05; }
        .tag-purple { background-color: #8e44ad; }
        .tag-teal { background-color: #009688; }
        
        .tool-result {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            display: none;
        }
        
        .results-section {
            background-color: var(--card-background);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .results-section h2 {
            margin-top: 0;
            color: var(--primary-color);
        }
        
        .results-content {
            margin-top: 1.5rem;
            padding: 1.5rem;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .severity-high {
            color: var(--danger-color);
            font-weight: bold;
        }
        
        .severity-medium {
            color: var(--warning-color);
            font-weight: bold;
        }
        
        .severity-low {
            color: var(--info-color);
        }
        
        .severity-info {
            color: var(--success-color);
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">HexStrike AI - Security Analysis</div>
    </header>
    
    <div class="container">
        <div class="security-form">
            <h2>Security Analysis Configuration</h2>
            <form id="securityAnalysisForm">
                <div class="form-group">
                    <label for="targetSystem">Target System:</label>
                    <select id="targetSystem" name="targetSystem">
                        <option value="application">Application Code</option>
                        <option value="infrastructure">Infrastructure Configuration</option>
                        <option value="container">Container Images</option>
                        <option value="cloud">Cloud Resources</option>
                        <option value="database">Database Security</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="analysisType">Analysis Type:</label>
                    <select id="analysisType" name="analysisType">
                        <option value="static">Static Analysis</option>
                        <option value="configuration">Configuration Review</option>
                        <option value="compliance">Compliance Check</option>
                        <option value="dependency">Dependency Scanning</option>
                        <option value="secrets">Secrets Detection</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="complianceFramework">Compliance Framework:</label>
                    <select id="complianceFramework" name="complianceFramework">
                        <option value="none">None</option>
                        <option value="pci">PCI DSS</option>
                        <option value="hipaa">HIPAA</option>
                        <option value="gdpr">GDPR</option>
                        <option value="nist">NIST 800-53</option>
                        <option value="iso">ISO 27001</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="additionalNotes">Additional Notes:</label>
                    <textarea id="additionalNotes" name="additionalNotes" rows="4" placeholder="Enter any specific areas of concern or analysis requirements"></textarea>
                </div>
                
                <button type="submit" id="startAnalysis">Start Security Analysis</button>
            </form>
        </div>
        
        <div class="tool-grid">
            <div class="tool-card">
                <h3>Code Security Scanner</h3>
                <p>Analyze source code for security vulnerabilities and coding issues</p>
                <div class="tag-container">
                    <span class="tag tag-blue">SAST</span>
                    <span class="tag tag-green">Code Quality</span>
                </div>
                <div class="tool-result" id="codeResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Dependency Analyzer</h3>
                <p>Scan dependencies for known vulnerabilities and outdated packages</p>
                <div class="tag-container">
                    <span class="tag tag-red">SCA</span>
                    <span class="tag tag-orange">CVE Tracking</span>
                </div>
                <div class="tool-result" id="dependencyResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Configuration Auditor</h3>
                <p>Review configuration files for security misconfigurations</p>
                <div class="tag-container">
                    <span class="tag tag-purple">Hardening Check</span>
                    <span class="tag tag-blue">Best Practices</span>
                </div>
                <div class="tool-result" id="configResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Secrets Detector</h3>
                <p>Find exposed secrets, API keys, and credentials in code and configs</p>
                <div class="tag-container">
                    <span class="tag tag-red">Credential Scanning</span>
                    <span class="tag tag-orange">Pattern Matching</span>
                </div>
                <div class="tool-result" id="secretsResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Compliance Checker</h3>
                <p>Verify compliance with security standards and regulations</p>
                <div class="tag-container">
                    <span class="tag tag-green">Regulatory</span>
                    <span class="tag tag-purple">Framework Mapping</span>
                </div>
                <div class="tool-result" id="complianceResult"></div>
            </div>
            
            <div class="tool-card">
                <h3>Container Security Scanner</h3>
                <p>Analyze container images for vulnerabilities and misconfigurations</p>
                <div class="tag-container">
                    <span class="tag tag-teal">Image Scanning</span>
                    <span class="tag tag-blue">Runtime Security</span>
                </div>
                <div class="tool-result" id="containerResult"></div>
            </div>
        </div>
        
        <div class="results-section" id="resultsSection" style="display: none;">
            <h2>Security Analysis Results</h2>
            <div class="results-content" id="resultsContent"></div>
        </div>
    </div>
    
    <script>
        document.getElementById('securityAnalysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            document.getElementById('startAnalysis').textContent = 'Analyzing...';
            document.getElementById('startAnalysis').disabled = true;
            
            // Reset previous results
            document.querySelectorAll('.tool-result').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            // Simulate analysis process
            setTimeout(() => {
                // Display results for each tool
                simulateToolResults();
                
                // Show overall results section
                document.getElementById('resultsSection').style.display = 'block';
                
                // Generate summary report
                generateSummaryReport();
                
                // Reset button
                document.getElementById('startAnalysis').textContent = 'Start Security Analysis';
                document.getElementById('startAnalysis').disabled = false;
            }, 2000);
        });
        
        function simulateToolResults() {
            // Code Security Scanner
            const codeResult = document.getElementById('codeResult');
            codeResult.textContent = 'Analysis complete. Found 3 high, 5 medium, and 8 low severity issues.';
            codeResult.style.display = 'block';
            
            // Dependency Analyzer
            const dependencyResult = document.getElementById('dependencyResult');
            dependencyResult.textContent = 'Scanned 247 dependencies. Found 2 critical, 7 high, and 12 medium vulnerabilities.';
            dependencyResult.style.display = 'block';
            
            // Configuration Auditor
            const configResult = document.getElementById('configResult');
            configResult.textContent = 'Audited 18 configuration files. Found 4 security misconfigurations and 6 hardening recommendations.';
            configResult.style.display = 'block';
            
            // Secrets Detector
            const secretsResult = document.getElementById('secretsResult');
            secretsResult.textContent = 'Detected 2 potential API keys and 1 hardcoded credential in source files.';
            secretsResult.style.display = 'block';
            
            // Compliance Checker
            const complianceResult = document.getElementById('complianceResult');
            complianceResult.textContent = 'Compliance score: 78%. 23 controls passed, 7 failed, 4 need review.';
            complianceResult.style.display = 'block';
            
            // Container Security Scanner
            const containerResult = document.getElementById('containerResult');
            containerResult.textContent = 'Scanned 5 container images. Found 3 critical, 8 high, and 15 medium vulnerabilities.';
            containerResult.style.display = 'block';
        }
        
        function generateSummaryReport() {
            const resultsContent = document.getElementById('resultsContent');
            const targetSystem = document.getElementById('targetSystem').value;
            const analysisType = document.getElementById('analysisType').value;
            
            let report = `SECURITY ANALYSIS SUMMARY\n`;
            report += `======================\n\n`;
            report += `Target: ${document.getElementById('targetSystem').options[document.getElementById('targetSystem').selectedIndex].text}\n`;
            report += `Analysis Type: ${document.getElementById('analysisType').options[document.getElementById('analysisType').selectedIndex].text}\n`;
            report += `Compliance Framework: ${document.getElementById('complianceFramework').options[document.getElementById('complianceFramework').selectedIndex].text}\n\n`;
            
            report += `FINDINGS SUMMARY\n`;
            report += `--------------\n\n`;
            report += `<span class="severity-high">Critical Issues: 5</span>\n`;
            report += `<span class="severity-high">High Severity: 23</span>\n`;
            report += `<span class="severity-medium">Medium Severity: 42</span>\n`;
            report += `<span class="severity-low">Low Severity: 18</span>\n`;
            report += `<span class="severity-info">Informational: 7</span>\n\n`;
            
            report += `TOP RECOMMENDATIONS\n`;
            report += `------------------\n\n`;
            report += `1. Update vulnerable dependencies in package.json\n`;
            report += `2. Remove hardcoded API keys in config/services.js\n`;
            report += `3. Enable HTTPS for all endpoints\n`;
            report += `4. Fix SQL injection vulnerability in user controller\n`;
            report += `5. Update Docker base images to latest versions\n\n`;
            
            report += `COMPLIANCE STATUS\n`;
            report += `----------------\n\n`;
            report += `Overall Compliance: 78%\n`;
            report += `Failed Controls: 7\n`;
            report += `Remediation Required: 11 items\n`;
            
            resultsContent.innerHTML = report;
        }
        
        // Reset form
        document.getElementById('securityAnalysisForm').addEventListener('reset', function() {
            document.querySelectorAll('.tool-result').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            document.getElementById('resultsSection').style.display = 'none';
        });
    </script>
</body>
</html>