/**
 * Chat Box AI Chat Interface - Main JavaScript
 * Handles UI interactions, chat functionality, and state management
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize the application
    initApp();
});

/**
 * Initialize the application
 */
function initApp() {
    // Initialize state
    initState();
    
    // Attach event listeners
    attachEventListeners();
    
    // Initialize resizable panels
    initResizablePanels();
    
    // Check for saved session
    checkSession();
    
    // Initialize tooltips
    initTooltips();
    
    // Initialize tabs
    initTabs();
    
    console.log('Chat Box AI Chat Interface initialized');
}

/**
 * Initialize application state
 */
function initState() {
    window.appState = {
        darkMode: localStorage.getItem('darkMode') === 'true',
        isLoggedIn: localStorage.getItem('isLoggedIn') === 'true',
        isProcessRunning: false,
        isPaused: false,
        currentRequest: null,
        chatHistory: JSON.parse(localStorage.getItem('chatHistory') || '[]'),
        requestQueue: JSON.parse(localStorage.getItem('requestQueue') || '[]'),
        requestHistory: JSON.parse(localStorage.getItem('requestHistory') || '[]'),
        currentRole: localStorage.getItem('currentRole') || 'helper',
        currentEnvironment: localStorage.getItem('currentEnvironment') || 'default',
        environments: JSON.parse(localStorage.getItem('environments') || '{}'),
        lastActivity: Date.now(),
        securityLevel: localStorage.getItem('securityLevel') || 'standard',
        todoList: JSON.parse(localStorage.getItem('todoList') || '[]'),
        metrics: {
            totalRequests: parseInt(localStorage.getItem('metrics_totalRequests') || '0'),
            successfulRequests: parseInt(localStorage.getItem('metrics_successfulRequests') || '0'),
            failedRequests: parseInt(localStorage.getItem('metrics_failedRequests') || '0'),
            averageRuntime: parseFloat(localStorage.getItem('metrics_averageRuntime') || '0'),
            lastRequestTime: parseInt(localStorage.getItem('metrics_lastRequestTime') || '0')
        }
    };
    
    // Apply dark mode if enabled
    if (window.appState.darkMode) {
        document.documentElement.classList.add('dark');
    }
    
    // Update UI based on login state
    updateUIForAuthState();
}

/**
 * Attach event listeners to UI elements
 */
function attachEventListeners() {
    // Theme toggle
    document.getElementById('themeToggle').addEventListener('click', toggleDarkMode);
    document.getElementById('loginThemeToggle').addEventListener('click', toggleDarkMode);
    
    // Login form
    document.getElementById('loginForm').addEventListener('submit', handleLoginSubmit);
    
    // Guest access button (we'll add this to the HTML)
    const guestBtn = document.getElementById('guestAccessBtn');
    if (guestBtn) {
        guestBtn.addEventListener('click', handleGuestAccess);
    }
    
    // Password toggle
    document.getElementById('toggleLoginPassword').addEventListener('click', togglePasswordVisibility);
    
    // Process control buttons
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');
    const changeRequestBtn = document.getElementById('changeRequestBtn');
    const newRequestBtn = document.getElementById('newRequestBtn');
    
    if (pauseBtn) pauseBtn.addEventListener('click', togglePause);
    if (stopBtn) stopBtn.addEventListener('click', stopProcess);
    if (changeRequestBtn) changeRequestBtn.addEventListener('click', showChangeRequestModal);
    if (newRequestBtn) newRequestBtn.addEventListener('click', startNewChat);
    
    // Chat form
    const chatForm = document.getElementById('chatForm');
    if (chatForm) chatForm.addEventListener('submit', handleChatSubmit);
    
    // File upload
    const fileUpload = document.getElementById('fileUpload');
    if (fileUpload) fileUpload.addEventListener('change', handleFileUpload);
    
    // Parameters toggle
    const paramsToggle = document.getElementById('paramsToggle');
    if (paramsToggle) paramsToggle.addEventListener('click', toggleParamsSection);
    
    // User menu
    const userMenuBtn = document.getElementById('userMenuBtn');
    if (userMenuBtn) userMenuBtn.addEventListener('click', toggleUserMenu);
    
    // New chat button
    const newChatBtn = document.getElementById('newChatBtn');
    if (newChatBtn) newChatBtn.addEventListener('click', startNewChat);
    
    // Confirm modal close button
    const closeConfirmBtn = document.getElementById('closeConfirmBtn');
    if (closeConfirmBtn) {
        closeConfirmBtn.addEventListener('click', () => {
            document.getElementById('confirmModal').classList.add('hidden');
        });
    }
    
    // View menu
    document.getElementById('viewMenuBtn').addEventListener('click', toggleViewMenu);
    
    // Export logs
    document.getElementById('exportLogsBtn').addEventListener('click', exportLogs);
    
    // Clear logs
    document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);
    
    // Role switcher
    document.querySelectorAll('.role-option').forEach(option => {
        option.addEventListener('click', () => switchRole(option.dataset.role));
    });
    
    // Environment presets
    document.querySelectorAll('.environment-option').forEach(option => {
        option.addEventListener('click', () => switchEnvironment(option.dataset.env));
    });
    
    // Request history
    document.getElementById('historyBtn').addEventListener('click', toggleHistoryPanel);
    
    // Inactivity detection
    document.addEventListener('mousemove', resetInactivityTimer);
    document.addEventListener('keypress', resetInactivityTimer);
    
    // Window beforeunload
    window.addEventListener('beforeunload', saveAppState);
}

/**
 * Initialize resizable panels
 */
function initResizablePanels() {
    const chatPanel = document.getElementById('chatPanel');
    const logsPanel = document.getElementById('logsPanel');
    const resizeHandle = document.getElementById('resizeHandle');
    
    let isResizing = false;
    let startX;
    let startWidthChat;
    let startWidthLogs;
    
    resizeHandle.addEventListener('mousedown', (e) => {
        isResizing = true;
        startX = e.clientX;
        startWidthChat = chatPanel.offsetWidth;
        startWidthLogs = logsPanel.offsetWidth;
        document.body.classList.add('resizing');
    });
    
    document.addEventListener('mousemove', (e) => {
        if (!isResizing) return;
        
        const deltaX = e.clientX - startX;
        const containerWidth = chatPanel.parentElement.offsetWidth;
        
        const newChatWidth = Math.max(300, Math.min(startWidthChat + deltaX, containerWidth - 300));
        const newLogsWidth = containerWidth - newChatWidth - 10; // 10px for the resize handle
        
        chatPanel.style.width = `${newChatWidth}px`;
        logsPanel.style.width = `${newLogsWidth}px`;
    });
    
    document.addEventListener('mouseup', () => {
        if (isResizing) {
            isResizing = false;
            document.body.classList.remove('resizing');
        }
    });
}

/**
 * Toggle dark mode
 */
function toggleDarkMode() {
    const isDarkMode = document.documentElement.classList.toggle('dark');
    window.appState.darkMode = isDarkMode;
    localStorage.setItem('darkMode', isDarkMode);
    
    // Update icon
    const themeIcon = document.getElementById('themeIcon');
    if (isDarkMode) {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    } else {
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
    }
}

/**
 * Toggle user menu
 */
function toggleUserMenu() {
    const userMenu = document.getElementById('userMenu');
    userMenu.classList.toggle('hidden');
}

/**
 * Toggle view menu
 */
function toggleViewMenu() {
    const viewMenu = document.getElementById('viewMenu');
    viewMenu.classList.toggle('hidden');
}

/**
 * Toggle parameters section
 */
function toggleParamsSection() {
    const paramsSection = document.getElementById('paramsSection');
    paramsSection.classList.toggle('hidden');
    
    const paramsIcon = document.getElementById('paramsIcon');
    if (paramsSection.classList.contains('hidden')) {
        paramsIcon.classList.remove('fa-chevron-up');
        paramsIcon.classList.add('fa-chevron-down');
    } else {
        paramsIcon.classList.remove('fa-chevron-down');
        paramsIcon.classList.add('fa-chevron-up');
    }
}

/**
 * Show login modal
 */
function showLoginModal() {
    const loginModal = document.getElementById('loginModal');
    loginModal.classList.remove('hidden');
    
    document.getElementById('loginForm').addEventListener('submit', (e) => {
        e.preventDefault();
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Demo login - in a real app, this would be an API call
        if (username && password) {
            login(username, password);
            loginModal.classList.add('hidden');
        }
    });
    
    document.getElementById('cancelLogin').addEventListener('click', () => {
        loginModal.classList.add('hidden');
    });
}

/**
 * Login user
 */
async function login(username, password) {
    try {
        // Import auth module
        const authModule = await import('./js/auth.js');
        
        // Authenticate user
        const user = authModule.authenticateUser(username, password);
        
        if (!user) {
            // Show error message
            const loginError = document.getElementById('loginError');
            loginError.textContent = 'Invalid username or password';
            loginError.classList.remove('hidden');
            return false;
        }
        
        // Store user session
        authModule.storeUserSession(user);
        
        // Update app state
        window.appState.isLoggedIn = true;
        window.appState.lastActivity = Date.now();
        window.appState.currentRole = user.role;
        window.appState.username = user.username;
        
        // Update UI
        updateUIForAuthState();
        showNotification(`Logged in successfully as ${user.role} user`);
        
        // Start auto-logout timer
        startAutoLogoutTimer();
        
        return true;
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed: ' + error.message, 'error');
        return false;
    }
}

/**
 * Logout user
 */
async function logout() {
    try {
        // Import auth module
        const authModule = await import('./js/auth.js');
        
        // Clear user session
        authModule.clearUserSession();
        
        // Update app state
        window.appState.isLoggedIn = false;
        window.appState.currentRole = null;
        window.appState.username = null;
        
        // Update UI
        updateUIForAuthState();
        showNotification('Logged out successfully');
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Logout failed: ' + error.message, 'error');
    }
}

/**
 * Handle login form submission
 */
async function handleLoginSubmit(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const loginError = document.getElementById('loginError');
    
    // Clear previous errors
    loginError.classList.add('hidden');
    
    try {
        const success = await login(username, password);
        if (success) {
            // Hide login modal
            const loginModal = document.getElementById('loginModal');
            loginModal.classList.add('hidden');
            
            // Update UI
            updateUIForAuthState();
            
            // Load chat history and other data
            renderChatHistory();
            renderRequestHistory();
        }
    } catch (error) {
        console.error('Login error:', error);
        loginError.textContent = 'Login failed. Please try again.';
        loginError.classList.remove('hidden');
    }
}

/**
 * Handle guest access
 */
async function handleGuestAccess() {
    try {
        // Import auth module
        const authModule = await import('./js/auth.js');
        
        // Enable guest access
        const guestUser = authModule.enableGuestAccess();
        
        // Update app state
        window.appState.isLoggedIn = true;
        window.appState.lastActivity = Date.now();
        window.appState.currentRole = guestUser.role;
        window.appState.username = guestUser.username;
        
        // Hide login modal
        const loginModal = document.getElementById('loginModal');
        loginModal.classList.add('hidden');
        
        // Update UI
        updateUIForAuthState();
        showNotification('Welcome! You are now using Chat Box AI as a guest.');
        
        // Load chat history and other data
        renderChatHistory();
        renderRequestHistory();
    } catch (error) {
        console.error('Guest access error:', error);
        showNotification('Failed to enable guest access', 'error');
    }
}

/**
 * Toggle password visibility
 */
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('loginPasswordIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
}

/**
 * Update UI based on authentication state
 */
function updateUIForAuthState() {
    const loggedInElements = document.querySelectorAll('.logged-in-only');
    const loggedOutElements = document.querySelectorAll('.logged-out-only');
    const loginModal = document.getElementById('loginModal');
    
    if (window.appState.isLoggedIn) {
        loggedInElements.forEach(el => el.classList.remove('hidden'));
        loggedOutElements.forEach(el => el.classList.add('hidden'));
        
        // Hide login modal
        if (loginModal) {
            loginModal.classList.add('hidden');
        }
        
        // Update username display
        const usernameDisplay = document.getElementById('usernameDisplay');
        if (usernameDisplay) {
            usernameDisplay.textContent = localStorage.getItem('username') || 'User';
        }
        
        // Update role display
        const roleDisplay = document.getElementById('roleDisplay');
        if (roleDisplay) {
            roleDisplay.textContent = localStorage.getItem('currentRole') || 'User';
        }
    } else {
        loggedInElements.forEach(el => el.classList.add('hidden'));
        loggedOutElements.forEach(el => el.classList.remove('hidden'));
        
        // Show login modal
        if (loginModal) {
            loginModal.classList.remove('hidden');
        }
    }
}

/**
 * Check for existing session
 */
function checkSession() {
    if (window.appState.isLoggedIn) {
        // Start auto-logout timer
        startAutoLogoutTimer();
        
        // Load chat history
        renderChatHistory();
        
        // Load request history
        renderRequestHistory();
    }
}

/**
 * Start auto-logout timer
 */
function startAutoLogoutTimer() {
    // Auto-logout after 30 minutes of inactivity
    const AUTO_LOGOUT_TIME = 30 * 60 * 1000; // 30 minutes
    
    setInterval(() => {
        const currentTime = Date.now();
        const timeSinceLastActivity = currentTime - window.appState.lastActivity;
        
        if (timeSinceLastActivity > AUTO_LOGOUT_TIME && window.appState.isLoggedIn) {
            logout();
            showNotification('You have been logged out due to inactivity');
        }
    }, 60000); // Check every minute
}

/**
 * Reset inactivity timer
 */
function resetInactivityTimer() {
    window.appState.lastActivity = Date.now();
}

/**
 * Toggle pause state
 */
function togglePause() {
    if (!window.appState.isProcessRunning) return;
    
    window.appState.isPaused = !window.appState.isPaused;
    
    const pauseBtn = document.getElementById('pauseBtn');
    const pauseIcon = pauseBtn.querySelector('i');
    const pauseText = pauseBtn.querySelector('span');
    
    if (window.appState.isPaused) {
        pauseIcon.classList.remove('fa-pause');
        pauseIcon.classList.add('fa-play');
        pauseText.textContent = 'Resume';
        addLog('Process paused', 'warning');
        
        // Security check for enhanced and maximum security levels
        if (window.appState.securityLevel === 'enhanced' || window.appState.securityLevel === 'maximum') {
            // Log the pause action for audit purposes
            logSecurityEvent({
                action: 'pause',
                timestamp: new Date().toISOString(),
                user: window.appState.username || 'anonymous',
                requestId: window.appState.currentRequest?.id || 'unknown'
            });
        }
    } else {
        pauseIcon.classList.remove('fa-play');
        pauseIcon.classList.add('fa-pause');
        pauseText.textContent = 'Pause';
        addLog('Process resumed', 'info');
        
        // Security check for enhanced and maximum security levels
        if (window.appState.securityLevel === 'enhanced' || window.appState.securityLevel === 'maximum') {
            // Log the resume action for audit purposes
            logSecurityEvent({
                action: 'resume',
                timestamp: new Date().toISOString(),
                user: window.appState.username || 'anonymous',
                requestId: window.appState.currentRequest?.id || 'unknown'
            });
        }
    }
}

/**
 * Stop the current process
 */
function stopProcess() {
    if (!window.appState.isProcessRunning) return;
    
    // Show confirmation modal
    const confirmModal = document.getElementById('confirmModal');
    confirmModal.classList.remove('hidden');
    
    // Remove existing event listeners to prevent duplicates
    const confirmBtn = document.getElementById('confirmStop');
    const cancelBtn = document.getElementById('cancelStop');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    const newCancelBtn = cancelBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
    
    newConfirmBtn.addEventListener('click', () => {
        // Store request info before stopping for security logging
        const requestInfo = {
            id: window.appState.currentRequest?.id || 'unknown',
            type: window.appState.currentRequest?.type || 'unknown',
            timestamp: new Date().toISOString(),
            user: window.appState.username || 'anonymous'
        };
        
        window.appState.isProcessRunning = false;
        window.appState.isPaused = false;
        
        // Update UI
        updateProcessControlsUI();
        
        addLog('Process stopped', 'warning');
        showNotification('Process stopped');
        
        // Security logging for enhanced and maximum security levels
        if (window.appState.securityLevel === 'enhanced' || window.appState.securityLevel === 'maximum') {
            logSecurityEvent({
                action: 'stop_process',
                timestamp: new Date().toISOString(),
                user: window.appState.username || 'anonymous',
                requestId: requestInfo.id,
                requestType: requestInfo.type
            });
        }
        
        confirmModal.classList.add('hidden');
    });
    
    newCancelBtn.addEventListener('click', () => {
        confirmModal.classList.add('hidden');
    });
}

/**
 * Show change request modal
 */
function showChangeRequestModal() {
    if (!window.appState.isProcessRunning) return;
    
    const changeRequestModal = document.getElementById('changeRequestModal');
    changeRequestModal.classList.remove('hidden');
    
    // Pre-fill with current request
    document.getElementById('modifiedRequest').value = window.appState.currentRequest;
    
    document.getElementById('changeRequestForm').addEventListener('submit', (e) => {
        e.preventDefault();
        const modifiedRequest = document.getElementById('modifiedRequest').value;
        
        if (modifiedRequest) {
            window.appState.currentRequest = modifiedRequest;
            addLog('Request modified', 'info');
            
            // If paused, resume
            if (window.appState.isPaused) {
                togglePause();
            }
            
            changeRequestModal.classList.add('hidden');
        }
    });
    
    document.getElementById('cancelChangeRequest').addEventListener('click', () => {
        changeRequestModal.classList.add('hidden');
    });
}

/**
 * Start a new chat
 */
function startNewChat() {
    // Open a new chat window
    const newWindow = window.open(
        window.location.href,
        '_blank',
        'width=1200,height=800,scrollbars=yes,resizable=yes'
    );
    
    if (newWindow) {
        // Focus on the new window
        newWindow.focus();
        
        // Add a small delay to ensure the new window loads properly
        setTimeout(() => {
            try {
                // Clear the new window's chat when it loads
                newWindow.addEventListener('load', () => {
                    if (newWindow.resetChat && typeof newWindow.resetChat === 'function') {
                        newWindow.resetChat();
                    }
                });
            } catch (e) {
                // Handle cross-origin issues if any
                console.log('New chat window opened successfully');
            }
        }, 100);
    } else {
        // Fallback if popup is blocked - show notification
        showNotification('Please allow popups to open new chat windows, or use Ctrl+Click to open in new tab', 'warning');
    }
}

/**
 * Reset chat to initial state
 */
function resetChat() {
    // Clear chat messages
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = '';
    
    // Clear logs
    const logsContainer = document.getElementById('logsContainer');
    logsContainer.innerHTML = '';
    
    // Reset state
    window.appState.isProcessRunning = false;
    window.appState.isPaused = false;
    window.appState.currentRequest = null;
    
    // Update UI
    updateProcessControlsUI();
    
    // Add initial log
    addLog('New chat started', 'info');
}

/**
 * Handle chat form submission
 */
function handleChatSubmit(e) {
    e.preventDefault();
    
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Get parameters if any
    const params = {};
    const paramInputs = document.querySelectorAll('#paramsSection input, #paramsSection select');
    paramInputs.forEach(input => {
        if (input.value) {
            params[input.name] = input.value;
        }
    });
    
    // Add user message to chat
    addChatMessage(message, 'user', params);
    
    // Clear input
    messageInput.value = '';
    
    // Set as current request
    window.appState.currentRequest = message;
    
    // Start process
    startProcess(message, params);
}

/**
 * Start processing a request
 */
function startProcess(request, params = {}) {
    // Generate a unique request ID
    const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    // Store current request info
    window.appState.currentRequest = {
        id: requestId,
        type: params.type || 'standard',
        text: request,
        params: params,
        startTime: Date.now()
    };
    
    window.appState.isProcessRunning = true;
    window.appState.isPaused = false;
    
    // Security checks based on security level
    if (window.appState.securityLevel === 'enhanced' || window.appState.securityLevel === 'maximum') {
        // Log the process start for audit purposes
        logSecurityEvent({
            action: 'start_process',
            timestamp: new Date().toISOString(),
            user: window.appState.username || 'anonymous',
            requestId: requestId,
            requestType: params.type || 'standard',
            requestText: request.substring(0, 100) + (request.length > 100 ? '...' : '') // Truncate for log size
        });
        
        // Check for sensitive patterns in the request
        const shouldProceed = handleSensitivePattern(request, requestId);
        
        // If maximum security and sensitive pattern detected, user declined to proceed
        if (!shouldProceed) {
            stopProcess();
            return;
        }
        
        // For maximum security, add additional checks
        if (window.appState.securityLevel === 'maximum') {
            // Rate limiting check
            const lastRequestTime = window.appState.metrics.lastRequestTime;
            const now = Date.now();
            if (lastRequestTime && (now - lastRequestTime < 2000)) { // 2 second rate limit
                addLog('Rate limit warning: Requests are being sent too quickly', 'warning');
                logSecurityEvent({
                    action: 'rate_limit_warning',
                    timestamp: new Date().toISOString(),
                    user: window.appState.username || 'anonymous',
                    requestId: requestId,
                    timeSinceLastRequest: now - lastRequestTime
                });
            }
            window.appState.metrics.lastRequestTime = now;
            localStorage.setItem('metrics_lastRequestTime', now);
        }
        }
    }
    
    // Update UI
    updateProcessControlsUI();
    
    // Add to request history
    addToRequestHistory(request, params);
    
    // Update metrics
    updateMetrics('start');
    
    // Log start
    addLog(`Processing request: ${request}`, 'info');
    if (Object.keys(params).length > 0) {
        addLog(`Parameters: ${JSON.stringify(params)}`, 'info');
    }
    
    // Simulate AI response (in a real app, this would be an API call)
    simulateAIResponse(request, params);
}

/**
 * Update process controls UI based on current state
 */
function updateProcessControlsUI() {
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');
    const changeRequestBtn = document.getElementById('changeRequestBtn');
    const processingIndicator = document.getElementById('processingIndicator');
    
    if (window.appState.isProcessRunning) {
        pauseBtn.disabled = false;
        stopBtn.disabled = false;
        changeRequestBtn.disabled = false;
        processingIndicator.classList.remove('hidden');
    } else {
        pauseBtn.disabled = true;
        stopBtn.disabled = true;
        changeRequestBtn.disabled = true;
        processingIndicator.classList.add('hidden');
        
        // Reset pause button
        const pauseIcon = pauseBtn.querySelector('i');
        const pauseText = pauseBtn.querySelector('span');
        pauseIcon.classList.remove('fa-play');
        pauseIcon.classList.add('fa-pause');
        pauseText.textContent = 'Pause';
    }
}

/**
 * Add a message to the chat
 */
function addChatMessage(message, sender, params = {}) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    
    messageElement.classList.add('message-bubble', 'animate-fade-in');
    
    if (sender === 'user') {
        messageElement.classList.add('user-message');
        messageElement.innerHTML = `<p>${escapeHTML(message)}</p>`;
        
        // Add parameters if any
        if (Object.keys(params).length > 0) {
            const paramsHTML = `<div class="text-xs mt-1 opacity-75">Parameters: ${escapeHTML(JSON.stringify(params))}</div>`;
            messageElement.innerHTML += paramsHTML;
        }
    } else {
        messageElement.classList.add('ai-message');
        
        // Format code blocks if present
        const formattedMessage = formatMessageWithCodeBlocks(message);
        messageElement.innerHTML = formattedMessage;
    }
    
    chatMessages.appendChild(messageElement);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Add to chat history
    window.appState.chatHistory.push({
        sender,
        message,
        params,
        timestamp: new Date().toISOString()
    });
    
    // Save to localStorage
    localStorage.setItem('chatHistory', JSON.stringify(window.appState.chatHistory));
}

/**
 * Format message with code blocks
 */
function formatMessageWithCodeBlocks(message) {
    // Simple regex to detect code blocks (```code```)
    const codeBlockRegex = /```([\s\S]*?)```/g;
    
    return message.replace(codeBlockRegex, (match, code) => {
        return `<pre><code>${escapeHTML(code)}</code></pre>`;
    });
}

/**
 * Handle file upload
 */
function handleFileUpload(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    // Check file size (max 5MB)
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_FILE_SIZE) {
        showErrorModal('File too large', 'Maximum file size is 5MB');
        return;
    }
    
    // Read file
    const reader = new FileReader();
    reader.onload = (event) => {
        const fileContent = event.target.result;
        
        // Add file info to chat
        addChatMessage(`Uploaded file: ${file.name} (${formatFileSize(file.size)})`, 'user');
        
        // Add file content to logs
        addLog(`File uploaded: ${file.name} (${formatFileSize(file.size)})`, 'info');
        
        // In a real app, you would send this file to the server
        // For demo purposes, we'll just show a notification
        showNotification(`File uploaded: ${file.name}`);
    };
    
    reader.onerror = () => {
        showErrorModal('File Error', 'Failed to read file');
    };
    
    reader.readAsText(file);
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' bytes';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
}

/**
 * Simulate AI response
 */
function simulateAIResponse(request, params) {
    // Show typing indicator
    const chatMessages = document.getElementById('chatMessages');
    const typingIndicator = document.createElement('div');
    typingIndicator.classList.add('typing-indicator', 'ai-message', 'message-bubble');
    typingIndicator.innerHTML = '<span></span><span></span><span></span>';
    chatMessages.appendChild(typingIndicator);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Simulate thinking time
    const thinkingTime = Math.random() * 1000 + 500; // 0.5-1.5 seconds
    
    setTimeout(() => {
        // Remove typing indicator
        chatMessages.removeChild(typingIndicator);
        
        // Check for potentially malicious content - DISABLED FOR DEMO
        // This check is now disabled to prevent false positives
        
        // Generate dynamic response based on user input and current role
        let response;
        const lowerRequest = request.toLowerCase();
        
        // Process the request to generate a relevant response
        if (lowerRequest.includes('hi') || lowerRequest.includes('hello') || lowerRequest.includes('hey')) {
            // Simple greeting response
            response = `Hello! I'm your AI-powered security testing assistant. How can I help you today?`;
        } else if (lowerRequest.includes('security') || lowerRequest.includes('scan') || lowerRequest.includes('vulnerability')) {
            response = generateSecurityResponse(request);
        } else if (lowerRequest.includes('web') || lowerRequest.includes('testing') || lowerRequest.includes('sql') || lowerRequest.includes('xss')) {
            response = generateWebTestingResponse(request);
        } else if (lowerRequest.includes('password') || lowerRequest.includes('brute force') || lowerRequest.includes('crack')) {
            response = generatePasswordResponse(request);
        } else if (lowerRequest.includes('ctf') || lowerRequest.includes('challenge') || lowerRequest.includes('forensic')) {
            response = generateCTFResponse(request);
        } else if (lowerRequest.includes('bug') || lowerRequest.includes('bounty') || lowerRequest.includes('hunting')) {
            response = generateBugBountyResponse(request);
        } else {
            // Default responses based on role
            switch (window.appState.currentRole) {
                case 'coder':
                    response = `Here's a code example for your request:\n\n\`\`\`javascript\n// Implementation for: ${request}\nfunction processRequest(params) {\n  console.log('Processing:', params);\n  // Your implementation here\n  return { success: true, data: 'Result' };\n}\n\`\`\``;
                    break;
                case 'explainer':
                    response = `Let me explain how this works:\n\n${request} involves several key concepts:\n\n1. First, we need to understand the basics\n2. Then, we can explore the advanced features\n3. Finally, we'll look at practical applications`;
                    break;
                default: // helper
                    response = `I'll help you with that request: "${request}". Let me process this for you...\n\nBased on your parameters ${JSON.stringify(params)}, here's what I can suggest: This is a simulated response that would normally contain helpful information related to your query.`;
            }
        }
        
        // Add AI response to chat
        addChatMessage(response, 'ai');
        
        // Simulate log entries
        simulateLogEntries();
        
        // Finish process after some time
        const processTime = Math.random() * 5000 + 3000; // 3-8 seconds
        setTimeout(() => {
            if (!window.appState.isPaused) {
                finishProcess(true);
            }
        }, processTime);
}

/**
 * Generate security scanning response
 */
function generateSecurityResponse(request) {
    const responses = [
        `I'll help you with security scanning. Here are some recommended tools:\n\n1. **Nmap** - For network discovery and security auditing\n2. **Nuclei** - For vulnerability scanning using templates\n3. **OpenVAS** - Open source vulnerability scanner\n\nWould you like me to explain how to use any of these tools for your specific needs?`,
        `For your security scanning request, I recommend starting with a basic reconnaissance:\n\n1. Passive information gathering\n2. Network scanning with Nmap\n3. Vulnerability assessment\n4. Exploitation testing (with proper authorization)\n\nWhat specific aspect of security testing are you interested in?`,
        `Security scanning involves identifying vulnerabilities in systems and networks. Based on your request, I'd recommend:\n\n- Port scanning\n- Service enumeration\n- Vulnerability identification\n- Reporting and documentation\n\nWould you like me to provide specific commands for any of these steps?`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
}

/**
 * Generate web testing response
 */
function generateWebTestingResponse(request) {
    const responses = [
        `For web application testing, I recommend focusing on these key areas:\n\n1. **Directory Enumeration** - Using tools like Gobuster or Dirbuster\n2. **SQL Injection Testing** - Using SQLmap or manual techniques\n3. **XSS Detection** - Using tools like XSStrike\n\nWould you like specific examples for any of these techniques?`,
        `Web testing typically follows this methodology:\n\n1. Reconnaissance\n2. Mapping the application\n3. Vulnerability discovery\n4. Exploitation\n5. Reporting\n\nBased on your request, I'd focus on step ${Math.floor(Math.random() * 3) + 2}. Would you like more details?`,
        `For effective web testing, consider these approaches:\n\n- Automated scanning with tools like OWASP ZAP\n- Manual testing for business logic flaws\n- API security testing\n- Client-side security testing\n\nWhat specific aspect of web testing are you interested in?`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
}

/**
 * Generate password attack response
 */
function generatePasswordResponse(request) {
    const responses = [
        `For password attacks, these are the common techniques:\n\n1. **Dictionary Attacks** - Using wordlists like rockyou.txt\n2. **Brute Force** - Trying all possible combinations\n3. **Hash Cracking** - Using tools like Hashcat or John the Ripper\n\nWhat specific password testing are you looking to perform?`,
        `Password security testing typically involves:\n\n- Testing for weak passwords\n- Checking for password reuse\n- Analyzing hash strength\n- Testing password policies\n\nBased on your request, I'd recommend focusing on hash cracking with Hashcat. Would you like sample commands?`,
        `For password security, consider these approaches:\n\n- Online attacks (with proper authorization)\n- Offline hash cracking\n- Password spraying\n- Credential stuffing tests\n\nWhat specific password testing scenario are you working with?`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
}

/**
 * Generate CTF challenge response
 */
function generateCTFResponse(request) {
    const responses = [
        `For CTF challenges, I recommend these approaches:\n\n1. **Binary Analysis** - Using tools like Ghidra or IDA Pro\n2. **Cryptography** - Understanding common algorithms and their weaknesses\n3. **Forensics** - Using tools like Autopsy or Volatility\n\nWhat type of CTF challenge are you working on?`,
        `CTF challenges typically fall into these categories:\n\n- Web exploitation\n- Reverse engineering\n- Binary exploitation\n- Cryptography\n- Forensics\n\nBased on your request, I'd focus on forensic analysis techniques. Would you like specific tool recommendations?`,
        `For CTF challenges, consider these strategies:\n\n- Understand the challenge description thoroughly\n- Look for hidden clues in files or images\n- Check for common encoding techniques\n- Use specialized tools for the challenge category\n\nWhat specific challenge are you trying to solve?`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
}

/**
 * Generate bug bounty response
 */
function generateBugBountyResponse(request) {
    const responses = [
        `For bug bounty hunting, I recommend this methodology:\n\n1. **Reconnaissance** - Gathering information about the target\n2. **Automated Scanning** - Using tools like Nuclei\n3. **Manual Testing** - Looking for business logic flaws\n4. **Reporting** - Creating clear, actionable reports\n\nWhat stage of bug bounty hunting are you currently in?`,
        `Bug bounty hunting typically involves:\n\n- Target selection\n- Scope understanding\n- Vulnerability discovery\n- Proof of concept creation\n- Responsible disclosure\n\nBased on your request, I'd focus on vulnerability discovery techniques. Would you like specific approaches?`,
        `For effective bug bounty hunting, consider these tips:\n\n- Focus on functionality that handles sensitive data\n- Look for newly added features\n- Test error handling\n- Check for race conditions\n\nWhat specific type of vulnerability are you hunting for?`
    ];
    return responses[Math.floor(Math.random() * responses.length)];
}
    }, thinkingTime);
}

/**
 * Simulate log entries
 */
function simulateLogEntries() {
    const logTypes = ['info', 'success', 'warning', 'error'];
    const logMessages = [
        'Initializing process',
        'Loading dependencies',
        'Analyzing request parameters',
        'Fetching data from API',
        'Processing data',
        'Applying transformations',
        'Generating response',
        'Validating output',
        'Optimizing results',
        'Finalizing response'
    ];
    
    let i = 0;
    const logInterval = setInterval(() => {
        // Stop if process is paused or completed
        if (window.appState.isPaused || !window.appState.isProcessRunning) {
            clearInterval(logInterval);
            return;
        }
        
        // Add log entry
        const logType = i === 9 ? 'success' : (Math.random() < 0.2 ? logTypes[Math.floor(Math.random() * logTypes.length)] : 'info');
        addLog(logMessages[i], logType);
        
        i++;
        if (i >= logMessages.length) {
            clearInterval(logInterval);
        }
    }, 800);
}

/**
 * Finish process
 */
function finishProcess(success = true) {
    if (!window.appState.isProcessRunning) return;
    
    window.appState.isProcessRunning = false;
    window.appState.isPaused = false;
    
    // Update UI
    updateProcessControlsUI();
    
    // Add final log
    if (success) {
        addLog('Process completed successfully', 'success');
        showNotification('Process completed');
        
        // Update metrics
        updateMetrics('success');
    } else {
        addLog('Process failed', 'error');
        showErrorModal('Process Failed', 'The process encountered an error and could not complete.');
        
        // Update metrics
        updateMetrics('failure');
    }
}

/**
 * Add a log entry
 */
function addLog(message, type = 'info') {
    const logsContainer = document.getElementById('logsContainer');
    const logElement = document.createElement('div');
    
    logElement.classList.add('log-entry', `log-${type}`, 'animate-fade-in');
    
    const timestamp = new Date().toLocaleTimeString();
    logElement.innerHTML = `<span class="text-xs opacity-75">[${timestamp}]</span> ${escapeHTML(message)}`;
    
    logsContainer.appendChild(logElement);
    
    // Scroll to bottom if not paused
    if (!window.appState.isPaused) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
    }
}

/**
 * Export logs
 */
function exportLogs() {
    const logsContainer = document.getElementById('logsContainer');
    const logEntries = logsContainer.querySelectorAll('.log-entry');
    
    let logText = '';
    logEntries.forEach(entry => {
        logText += entry.textContent + '\n';
    });
    
    // Create file
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    // Create download link
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-box-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    a.click();
    
    // Clean up
    URL.revokeObjectURL(url);
    
    showNotification('Logs exported successfully');
}

/**
 * Clear logs
 */
function clearLogs() {
    const logsContainer = document.getElementById('logsContainer');
    logsContainer.innerHTML = '';
    
    addLog('Logs cleared', 'info');
}

/**
 * Add to request history
 */
function addToRequestHistory(request, params = {}) {
    const MAX_HISTORY = 20;
    
    // Add to history
    window.appState.requestHistory.unshift({
        request,
        params,
        timestamp: new Date().toISOString(),
        success: null // Will be updated when process finishes
    });
    
    // Limit history size
    if (window.appState.requestHistory.length > MAX_HISTORY) {
        window.appState.requestHistory = window.appState.requestHistory.slice(0, MAX_HISTORY);
    }
    
    // Save to localStorage
    localStorage.setItem('requestHistory', JSON.stringify(window.appState.requestHistory));
    
    // Update UI
    renderRequestHistory();
}

/**
 * Render request history
 */
function renderRequestHistory() {
    const historyContainer = document.getElementById('historyContainer');
    if (!historyContainer) return;
    
    historyContainer.innerHTML = '';
    
    if (window.appState.requestHistory.length === 0) {
        historyContainer.innerHTML = '<div class="p-4 text-center text-gray-500">No history yet</div>';
        return;
    }
    
    window.appState.requestHistory.forEach((item, index) => {
        const historyItem = document.createElement('div');
        historyItem.classList.add('history-item');
        
        const timestamp = new Date(item.timestamp).toLocaleString();
        const truncatedRequest = item.request.length > 30 ? item.request.substring(0, 30) + '...' : item.request;
        
        historyItem.innerHTML = `
            <div>
                <div class="font-medium">${escapeHTML(truncatedRequest)}</div>
                <div class="text-xs opacity-75">${timestamp}</div>
            </div>
            <div class="flex space-x-2">
                <button class="text-sm text-blue-600 dark:text-blue-400 history-rerun" data-index="${index}">
                    <i class="fas fa-redo-alt"></i>
                </button>
                <button class="text-sm text-red-600 dark:text-red-400 history-delete" data-index="${index}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        historyContainer.appendChild(historyItem);
    });
    
    // Add event listeners
    document.querySelectorAll('.history-rerun').forEach(button => {
        button.addEventListener('click', () => {
            const index = parseInt(button.dataset.index);
            const item = window.appState.requestHistory[index];
            
            // Add user message to chat
            addChatMessage(item.request, 'user', item.params);
            
            // Start process
            startProcess(item.request, item.params);
            
            // Hide history panel
            document.getElementById('historyPanel').classList.add('hidden');
        });
    });
    
    document.querySelectorAll('.history-delete').forEach(button => {
        button.addEventListener('click', () => {
            const index = parseInt(button.dataset.index);
            
            // Remove from history
            window.appState.requestHistory.splice(index, 1);
            
            // Save to localStorage
            localStorage.setItem('requestHistory', JSON.stringify(window.appState.requestHistory));
            
            // Update UI
            renderRequestHistory();
        });
    });
}

/**
 * Toggle history panel
 */
function toggleHistoryPanel() {
    const historyPanel = document.getElementById('historyPanel');
    historyPanel.classList.toggle('hidden');
    
    if (!historyPanel.classList.contains('hidden')) {
        renderRequestHistory();
    }
}

/**
 * Render chat history
 */
function renderChatHistory() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = '';
    
    window.appState.chatHistory.forEach(item => {
        addChatMessage(item.message, item.sender, item.params);
    });
}

/**
 * Switch AI role
 */
function switchRole(role) {
    window.appState.currentRole = role;
    localStorage.setItem('currentRole', role);
    
    // Update UI
    document.querySelectorAll('.role-option').forEach(option => {
        if (option.dataset.role === role) {
            option.classList.add('active');
        } else {
            option.classList.remove('active');
        }
    });
    
    // Update current role display
    document.getElementById('currentRole').textContent = role.charAt(0).toUpperCase() + role.slice(1);
    
    // Close menu
    document.getElementById('userMenu').classList.add('hidden');
    
    showNotification(`Switched to ${role} role`);
}

/**
 * Switch environment
 */
function switchEnvironment(env) {
    window.appState.currentEnvironment = env;
    localStorage.setItem('currentEnvironment', env);
    
    // Update UI
    document.querySelectorAll('.environment-option').forEach(option => {
        if (option.dataset.env === env) {
            option.classList.add('active');
        } else {
            option.classList.remove('active');
        }
    });
    
    // Update current environment display
    document.getElementById('currentEnvironment').textContent = env.charAt(0).toUpperCase() + env.slice(1);
    
    // Load environment parameters if available
    if (window.appState.environments[env]) {
        const params = window.appState.environments[env];
        
        // Fill parameter inputs
        Object.keys(params).forEach(key => {
            const input = document.querySelector(`#paramsSection input[name="${key}"], #paramsSection select[name="${key}"]`);
            if (input) {
                input.value = params[key];
            }
        });
    }
    
    // Close menu
    document.getElementById('viewMenu').classList.add('hidden');
    
    showNotification(`Switched to ${env} environment`);
}

/**
 * Update metrics
 */
function updateMetrics(event) {
    switch (event) {
        case 'start':
            window.appState.metrics.totalRequests++;
            localStorage.setItem('metrics_totalRequests', window.appState.metrics.totalRequests);
            break;
        case 'success':
            window.appState.metrics.successfulRequests++;
            localStorage.setItem('metrics_successfulRequests', window.appState.metrics.successfulRequests);
            break;
        case 'failure':
            window.appState.metrics.failedRequests++;
            localStorage.setItem('metrics_failedRequests', window.appState.metrics.failedRequests);
            break;
    }
    
    // Update UI if metrics dashboard is open
    updateMetricsDashboard();
}

/**
 * Update metrics dashboard
 */
function updateMetricsDashboard() {
    const totalRequestsElement = document.getElementById('metricTotalRequests');
    const successRateElement = document.getElementById('metricSuccessRate');
    const failureRateElement = document.getElementById('metricFailureRate');
    
    if (totalRequestsElement) {
        totalRequestsElement.textContent = window.appState.metrics.totalRequests;
    }
    
    if (successRateElement && window.appState.metrics.totalRequests > 0) {
        const successRate = (window.appState.metrics.successfulRequests / window.appState.metrics.totalRequests * 100).toFixed(1);
        successRateElement.textContent = `${successRate}%`;
    }
    
    if (failureRateElement && window.appState.metrics.totalRequests > 0) {
        const failureRate = (window.appState.metrics.failedRequests / window.appState.metrics.totalRequests * 100).toFixed(1);
        failureRateElement.textContent = `${failureRate}%`;
    }
}

/**
 * Initialize tooltips
 */
function initTooltips() {
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        element.classList.add('tooltip');
    });
}

/**
 * Initialize tabs
 */
function initTabs() {
    const tabContainers = document.querySelectorAll('.tab-container');
    
    tabContainers.forEach(container => {
        const tabs = container.querySelectorAll('.tab');
        const tabContents = container.querySelectorAll('.tab-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.dataset.tab;
                
                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Show active content
                tabContents.forEach(content => {
                    if (content.dataset.tabContent === tabId) {
                        content.classList.remove('hidden');
                        
                        // Initialize charts if metrics tab
                        if (tabId === 'metrics') {
                            initCharts();
                        }
                        
                        // Initialize todo list if todo tab
                        if (tabId === 'todo') {
                            renderTodoList();
                        }
                        
                        // Initialize security settings if security tab
                        if (tabId === 'security') {
                            initSecuritySettings();
                        }
                    } else {
                        content.classList.add('hidden');
                    }
                });
            });
        });
    });
}

/**
 * Initialize security settings
 */
function initSecuritySettings() {
    const securityContainer = document.getElementById('securitySettings');
    if (!securityContainer) return;
    
    // Clear container
    securityContainer.innerHTML = '';
    
    // Create security level selector
    const securityLevelSection = document.createElement('div');
    securityLevelSection.className = 'mb-6';
    securityLevelSection.innerHTML = `
        <h3 class="text-lg font-medium mb-2">Security Level</h3>
        <div class="flex flex-col space-y-2">
            <div class="flex items-center">
                <input type="radio" id="securityStandard" name="securityLevel" value="standard" 
                    ${window.appState.securityLevel === 'standard' ? 'checked' : ''}>
                <label for="securityStandard" class="ml-2">Standard</label>
            </div>
            <div class="flex items-center">
                <input type="radio" id="securityEnhanced" name="securityLevel" value="enhanced" 
                    ${window.appState.securityLevel === 'enhanced' ? 'checked' : ''}>
                <label for="securityEnhanced" class="ml-2">Enhanced</label>
            </div>
            <div class="flex items-center">
                <input type="radio" id="securityMaximum" name="securityLevel" value="maximum" 
                    ${window.appState.securityLevel === 'maximum' ? 'checked' : ''}>
                <label for="securityMaximum" class="ml-2">Maximum</label>
            </div>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
            Higher security levels enable additional protections and logging.
        </p>
    `;
    securityContainer.appendChild(securityLevelSection);
    
    // Create security log section
    const securityLogSection = document.createElement('div');
    securityLogSection.className = 'mb-6';
    securityLogSection.innerHTML = `
        <div class="flex justify-between items-center mb-2">
            <h3 class="text-lg font-medium">Security Log</h3>
            <button id="clearSecurityLog" class="text-sm text-red-600 dark:text-red-400 hover:underline">
                Clear Log
            </button>
        </div>
        <div id="securityLogContainer" class="bg-gray-100 dark:bg-gray-800 p-3 rounded-md h-64 overflow-y-auto text-sm font-mono">
        </div>
    `;
    securityContainer.appendChild(securityLogSection);
    
    // Add event listeners for security level radios
    document.querySelectorAll('input[name="securityLevel"]').forEach(radio => {
        radio.addEventListener('change', (e) => {
            changeSecurityLevel(e.target.value);
        });
    });
    
    // Add event listener for clear log button
    document.getElementById('clearSecurityLog').addEventListener('click', clearSecurityLog);
    
    // Display security log
    displaySecurityLog();
}

/**
 * Change security level
 */
function changeSecurityLevel(level) {
    if (!['standard', 'enhanced', 'maximum'].includes(level)) {
        level = 'standard';
    }
    
    window.appState.securityLevel = level;
    localStorage.setItem('securityLevel', level);
    
    // Log the security level change
    logSecurityEvent({
        action: 'change_security_level',
        timestamp: new Date().toISOString(),
        user: window.appState.username || 'anonymous',
        newLevel: level
    });
    
    showNotification(`Security level changed to ${level}`);
    
    // Update UI based on security level
    updateSecurityUI();
}

/**
 * Update UI based on security level
 */
function updateSecurityUI() {
    const securityLevel = window.appState.securityLevel;
    
    // Update security indicator in header
    const securityIndicator = document.getElementById('securityIndicator');
    if (securityIndicator) {
        securityIndicator.className = 'ml-2 w-2 h-2 rounded-full';
        
        switch (securityLevel) {
            case 'standard':
                securityIndicator.classList.add('bg-yellow-500');
                securityIndicator.title = 'Standard Security';
                break;
            case 'enhanced':
                securityIndicator.classList.add('bg-blue-500');
                securityIndicator.title = 'Enhanced Security';
                break;
            case 'maximum':
                securityIndicator.classList.add('bg-green-500');
                securityIndicator.title = 'Maximum Security';
                break;
        }
    }
    
    // Show/hide additional security features based on level
    const enhancedFeatures = document.querySelectorAll('.enhanced-security');
    const maximumFeatures = document.querySelectorAll('.maximum-security');
    
    enhancedFeatures.forEach(el => {
        el.classList.toggle('hidden', securityLevel === 'standard');
    });
    
    maximumFeatures.forEach(el => {
        el.classList.toggle('hidden', securityLevel !== 'maximum');
    });
}

/**
 * Display security log
 */
function displaySecurityLog() {
    const logContainer = document.getElementById('securityLogContainer');
    if (!logContainer) return;
    
    // Get security log
    const securityLog = JSON.parse(localStorage.getItem('securityLog') || '[]');
    
    // Clear container
    logContainer.innerHTML = '';
    
    if (securityLog.length === 0) {
        logContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No security events logged yet.</p>';
        return;
    }
    
    // Display most recent events first
    securityLog.slice().reverse().forEach(event => {
        const logEntry = document.createElement('div');
        logEntry.className = 'mb-2 pb-2 border-b border-gray-200 dark:border-gray-700';
        
        // Format timestamp
        const timestamp = new Date(event.timestamp);
        const formattedTime = timestamp.toLocaleString();
        
        // Format event details
        let details = '';
        switch (event.action) {
            case 'start_process':
                details = `Request: ${event.requestText || 'N/A'}`;
                break;
            case 'stop_process':
                details = `Request ID: ${event.requestId || 'N/A'}`;
                break;
            case 'pause':
            case 'resume':
                details = `Request ID: ${event.requestId || 'N/A'}`;
                break;
            case 'change_security_level':
                details = `New Level: ${event.newLevel || 'N/A'}`;
                break;
            case 'sensitive_pattern_detected':
                details = `Patterns: ${event.patterns ? event.patterns.join(', ') : 'N/A'}`;
                break;
            case 'rate_limit_warning':
                details = `Time since last request: ${event.timeSinceLastRequest}ms`;
                break;
            default:
                details = Object.entries(event)
                    .filter(([key]) => !['action', 'timestamp', 'user'].includes(key))
                    .map(([key, value]) => `${key}: ${value}`)
                    .join(', ');
        }
        
        logEntry.innerHTML = `
            <div class="flex justify-between">
                <span class="font-semibold">${event.action.replace(/_/g, ' ').toUpperCase()}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">${formattedTime}</span>
            </div>
            <div class="text-xs mt-1">
                <span class="text-gray-500 dark:text-gray-400">User: ${event.user}</span>
            </div>
            <div class="text-xs mt-1">${details}</div>
        `;
        
        logContainer.appendChild(logEntry);
    });
}

/**
 * Clear security log
 */
function clearSecurityLog() {
    // Confirm before clearing
    if (confirm('Are you sure you want to clear the security log? This action cannot be undone.')) {
        localStorage.removeItem('securityLog');
        displaySecurityLog();
        showNotification('Security log cleared');
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notification
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create new notification
    const toast = document.createElement('div');
    toast.classList.add('toast', 'animate-fade-in');
    
    let icon;
    switch (type) {
        case 'success':
            icon = 'fa-check-circle';
            break;
        case 'warning':
            icon = 'fa-exclamation-triangle';
            break;
        case 'error':
            icon = 'fa-times-circle';
            break;
        default:
            icon = 'fa-info-circle';
    }
    
    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${icon} mr-2"></i>
            <span>${escapeHTML(message)}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('animate-fade-out');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

/**
 * Show error modal
 */
function showErrorModal(title, message) {
    const errorModal = document.getElementById('errorModal');
    const errorTitle = document.getElementById('errorTitle');
    const errorMessage = document.getElementById('errorMessage');
    
    errorTitle.textContent = title;
    errorMessage.textContent = message;
    
    errorModal.classList.remove('hidden');
    
    document.getElementById('closeError').addEventListener('click', () => {
        errorModal.classList.add('hidden');
    });
}

/**
 * Save application state before unload
 */
function saveAppState() {
    localStorage.setItem('chatHistory', JSON.stringify(window.appState.chatHistory));
    localStorage.setItem('requestHistory', JSON.stringify(window.appState.requestHistory));
    localStorage.setItem('requestQueue', JSON.stringify(window.appState.requestQueue));
    localStorage.setItem('environments', JSON.stringify(window.appState.environments));
    localStorage.setItem('todoList', JSON.stringify(window.appState.todoList));
    localStorage.setItem('securityLevel', window.appState.securityLevel);
}

/**
 * Log security events for audit purposes
 */
function logSecurityEvent(event) {
    // Get existing security log or initialize new one
    const securityLog = JSON.parse(localStorage.getItem('securityLog') || '[]');
    
    // Add new event to log
    securityLog.push(event);
    
    // Limit log size to prevent localStorage overflow
    if (securityLog.length > 1000) {
        securityLog.shift(); // Remove oldest entry
    }
    
    // Save updated log
    localStorage.setItem('securityLog', JSON.stringify(securityLog));
    
    // For maximum security level, also log to console
    if (window.appState.securityLevel === 'maximum') {
        console.log('Security Event:', event);
        
        // In a real application, this would send the event to a server
        // For demo purposes, we're just logging to localStorage and console
    }
}

/**
 * Check for sensitive patterns in request
 */
function checkSensitivePatterns(text) {
    if (!text) return { hasSensitivePattern: false, patterns: [] };
    
    const sensitivePatterns = [
        { pattern: /password\s*[:=]\s*\S+/gi, name: 'password' },
        { pattern: /api[_\-\s]*key\s*[:=]\s*\S+/gi, name: 'api_key' },
        { pattern: /token\s*[:=]\s*\S+/gi, name: 'token' },
        { pattern: /secret\s*[:=]\s*\S+/gi, name: 'secret' },
        { pattern: /credential\s*[:=]\s*\S+/gi, name: 'credential' },
        { pattern: /\b(?:\d[ -]*?){13,16}\b/g, name: 'credit_card' }, // Credit card pattern
        { pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b/g, name: 'email' },
        { pattern: /\b(?:\d{3}[ -]?){2}\d{4}\b/g, name: 'ssn' }, // SSN pattern
        { pattern: /\b(?:\d{1,3}\.){3}\d{1,3}\b/g, name: 'ip_address' }
    ];
    
    // Disable pattern checking for now to fix the false positive issue
    // We'll return no patterns found regardless of input
    return {
        hasSensitivePattern: false,
        patterns: []
    };
}

/**
 * Handle sensitive pattern detection
 */
function handleSensitivePattern(text, requestId) {
    const result = checkSensitivePatterns(text);
    
    if (result.hasSensitivePattern) {
        // Log the security event
        logSecurityEvent({
            action: 'sensitive_pattern_detected',
            timestamp: new Date().toISOString(),
            user: window.appState.username || 'anonymous',
            requestId: requestId,
            patterns: result.patterns
        });
        
        // For enhanced and maximum security, show warning
        if (window.appState.securityLevel !== 'standard') {
            showNotification(`Warning: Sensitive information detected (${result.patterns.join(', ')}). Consider removing this information.`, 'warning');
            
            // For maximum security, require confirmation
            if (window.appState.securityLevel === 'maximum') {
                return confirm('Sensitive information detected in your request. Do you want to proceed anyway?');
            }
        }
    }
    
    return true; // Proceed with request by default
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHTML(str) {
    return str.replace(/[&<>"']/g, (match) => {
        return {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        }[match];
    });
}

/**
 * Basic encryption for demo purposes
 * In a real app, use a proper encryption library
 */
function encryptData(data) {
    // This is just a simple base64 encoding for demo purposes
    // In a real app, use a proper encryption method
    return btoa(data);
}

/**
 * Basic decryption for demo purposes
 */
function decryptData(encryptedData) {
    // This is just a simple base64 decoding for demo purposes
    try {
        return atob(encryptedData);
    } catch (e) {
        return null;
    }
}

/**
 * Initialize charts for metrics dashboard
 */
function initCharts() {
    // Check if charts container exists
    const chartsContainer = document.getElementById('metricsCharts');
    if (!chartsContainer) return;
    
    // Clear previous charts
    chartsContainer.innerHTML = '';
    
    // Create canvas elements
    const requestsChartCanvas = document.createElement('canvas');
    requestsChartCanvas.id = 'requestsChart';
    const responseTimeChartCanvas = document.createElement('canvas');
    responseTimeChartCanvas.id = 'responseTimeChart';
    
    // Add to container
    chartsContainer.appendChild(requestsChartCanvas);
    chartsContainer.appendChild(responseTimeChartCanvas);
    
    // Get theme-aware colors
    const isDarkMode = document.documentElement.classList.contains('dark');
    const textColor = isDarkMode ? '#e2e8f0' : '#4a5568';
    const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
    
    // Create requests chart
    const requestsCtx = requestsChartCanvas.getContext('2d');
    new Chart(requestsCtx, {
        type: 'doughnut',
        data: {
            labels: ['Successful', 'Failed'],
            datasets: [{
                data: [
                    window.appState.metrics.successfulRequests,
                    window.appState.metrics.failedRequests
                ],
                backgroundColor: [
                    '#10b981', // Success - green
                    '#ef4444'  // Failed - red
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: textColor
                    }
                },
                title: {
                    display: true,
                    text: 'Request Success Rate',
                    color: textColor
                }
            }
        }
    });
    
    // Create response time chart (using dummy data for demo)
    const responseTimeCtx = responseTimeChartCanvas.getContext('2d');
    
    // Generate some dummy data for the response time chart
    const labels = [];
    const data = [];
    const now = new Date();
    for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        data.push(Math.random() * 2 + 1); // Random response time between 1-3 seconds
    }
    
    new Chart(responseTimeCtx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Avg. Response Time (s)',
                data: data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: textColor
                    }
                },
                title: {
                    display: true,
                    text: 'Response Time Trend',
                    color: textColor
                }
            },
            scales: {
                x: {
                    grid: {
                        color: gridColor
                    },
                    ticks: {
                        color: textColor
                    }
                },
                y: {
                    grid: {
                        color: gridColor
                    },
                    ticks: {
                        color: textColor
                    },
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Render Todo list
 */
function renderTodoList() {
    const todoContainer = document.getElementById('todoContainer');
    if (!todoContainer) return;
    
    // Clear container
    todoContainer.innerHTML = '';
    
    // Add header with add button
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center mb-4';
    header.innerHTML = `
        <h3 class="text-lg font-medium">Task List</h3>
        <button id="addTodoBtn" class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
            <i class="fas fa-plus"></i>
        </button>
    `;
    todoContainer.appendChild(header);
    
    // Add todo items
    if (window.appState.todoList.length === 0) {
        const emptyState = document.createElement('div');
        emptyState.className = 'text-center py-4 text-gray-500 dark:text-gray-400';
        emptyState.textContent = 'No tasks yet. Click the + button to add one.';
        todoContainer.appendChild(emptyState);
    } else {
        const todoList = document.createElement('div');
        todoList.className = 'space-y-2';
        
        window.appState.todoList.forEach((todo, index) => {
            const todoItem = document.createElement('div');
            todoItem.className = 'flex items-center p-2 rounded-md ' + 
                (todo.completed ? 'bg-gray-100 dark:bg-gray-800 opacity-60' : 'bg-white dark:bg-gray-700');
            
            todoItem.innerHTML = `
                <input type="checkbox" class="mr-2 todo-checkbox" data-index="${index}" ${todo.completed ? 'checked' : ''}>
                <span class="flex-1 ${todo.completed ? 'line-through' : ''}">${escapeHTML(todo.text)}</span>
                <div class="flex space-x-1">
                    <button class="p-1 text-sm text-blue-600 dark:text-blue-400 todo-edit" data-index="${index}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="p-1 text-sm text-red-600 dark:text-red-400 todo-delete" data-index="${index}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            `;
            
            todoList.appendChild(todoItem);
        });
        
        todoContainer.appendChild(todoList);
    }
    
    // Add event listeners
    document.getElementById('addTodoBtn').addEventListener('click', showAddTodoModal);
    
    document.querySelectorAll('.todo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const index = parseInt(e.target.dataset.index);
            toggleTodoCompleted(index);
        });
    });
    
    document.querySelectorAll('.todo-edit').forEach(button => {
        button.addEventListener('click', (e) => {
            const index = parseInt(e.target.closest('.todo-edit').dataset.index);
            showEditTodoModal(index);
        });
    });
    
    document.querySelectorAll('.todo-delete').forEach(button => {
        button.addEventListener('click', (e) => {
            const index = parseInt(e.target.closest('.todo-delete').dataset.index);
            deleteTodo(index);
        });
    });
}

/**
 * Show add todo modal
 */
function showAddTodoModal() {
    // Create modal if it doesn't exist
    let todoModal = document.getElementById('todoModal');
    if (!todoModal) {
        todoModal = document.createElement('div');
        todoModal.id = 'todoModal';
        todoModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in hidden';
        todoModal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md animate-slide-in">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium" id="todoModalTitle">Add Task</h3>
                    <button id="closeTodoModal" class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="todoForm">
                    <div class="mb-4">
                        <label for="todoText" class="block text-sm font-medium mb-1">Task Description</label>
                        <input type="text" id="todoText" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" required>
                    </div>
                    <div class="mb-4">
                        <label for="todoPriority" class="block text-sm font-medium mb-1">Priority</label>
                        <select id="todoPriority" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" id="cancelTodo" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">Cancel</button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md">Save</button>
                    </div>
                </form>
            </div>
        `;
        document.body.appendChild(todoModal);
    }
    
    // Reset form
    document.getElementById('todoModalTitle').textContent = 'Add Task';
    document.getElementById('todoForm').reset();
    
    // Show modal
    todoModal.classList.remove('hidden');
    
    // Focus input
    document.getElementById('todoText').focus();
    
    // Add event listeners
    document.getElementById('closeTodoModal').addEventListener('click', () => {
        todoModal.classList.add('hidden');
    });
    
    document.getElementById('cancelTodo').addEventListener('click', () => {
        todoModal.classList.add('hidden');
    });
    
    document.getElementById('todoForm').addEventListener('submit', (e) => {
        e.preventDefault();
        
        const text = document.getElementById('todoText').value.trim();
        const priority = document.getElementById('todoPriority').value;
        
        if (text) {
            addTodo(text, priority);
            todoModal.classList.add('hidden');
        }
    });
}

/**
 * Show edit todo modal
 */
function showEditTodoModal(index) {
    const todo = window.appState.todoList[index];
    if (!todo) return;
    
    // Show modal
    showAddTodoModal();
    
    // Update title and form
    document.getElementById('todoModalTitle').textContent = 'Edit Task';
    document.getElementById('todoText').value = todo.text;
    document.getElementById('todoPriority').value = todo.priority || 'medium';
    
    // Override submit handler
    const todoForm = document.getElementById('todoForm');
    const originalSubmitHandler = todoForm.onsubmit;
    
    todoForm.onsubmit = (e) => {
        e.preventDefault();
        
        const text = document.getElementById('todoText').value.trim();
        const priority = document.getElementById('todoPriority').value;
        
        if (text) {
            updateTodo(index, text, priority);
            document.getElementById('todoModal').classList.add('hidden');
        }
    };
}

/**
 * Add a new todo
 */
function addTodo(text, priority = 'medium') {
    window.appState.todoList.push({
        text,
        priority,
        completed: false,
        createdAt: new Date().toISOString()
    });
    
    // Save to localStorage
    localStorage.setItem('todoList', JSON.stringify(window.appState.todoList));
    
    // Update UI
    renderTodoList();
    
    // Show notification
    showNotification('Task added successfully');
}

/**
 * Update a todo
 */
function updateTodo(index, text, priority) {
    if (index < 0 || index >= window.appState.todoList.length) return;
    
    window.appState.todoList[index].text = text;
    window.appState.todoList[index].priority = priority;
    window.appState.todoList[index].updatedAt = new Date().toISOString();
    
    // Save to localStorage
    localStorage.setItem('todoList', JSON.stringify(window.appState.todoList));
    
    // Update UI
    renderTodoList();
    
    // Show notification
    showNotification('Task updated successfully');
}

/**
 * Toggle todo completed status
 */
function toggleTodoCompleted(index) {
    if (index < 0 || index >= window.appState.todoList.length) return;
    
    window.appState.todoList[index].completed = !window.appState.todoList[index].completed;
    
    // Save to localStorage
    localStorage.setItem('todoList', JSON.stringify(window.appState.todoList));
    
    // Update UI
    renderTodoList();
}

/**
 * Delete a todo
 */
function deleteTodo(index) {
    if (index < 0 || index >= window.appState.todoList.length) return;
    
    // Remove from array
    window.appState.todoList.splice(index, 1);
    
    // Save to localStorage
    localStorage.setItem('todoList', JSON.stringify(window.appState.todoList));
    
    // Update UI
    renderTodoList();
    
    // Show notification
    showNotification('Task deleted successfully');
}