#!/usr/bin/env python3
"""
HexStrike AI - Simplified Test Server
Testing core functionality without complex dependencies
"""

import os
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import JWTManager, jwt_required, create_access_token, get_jwt_identity
from functools import wraps
import requests
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app configuration
app = Flask(__name__)
app.config['JSON_SORT_KEYS'] = False

# Security Configuration
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'hexstrike-ai-jwt-secret-key-2024')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
app.config['DEBUG'] = False

# Initialize JWT and CORS
jwt = JWTManager(app)
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'], 
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'Authorization', 'X-Requested-With'])

# Configuration
API_PORT = int(os.environ.get('CHAT_BOX_PORT', 8888))
API_HOST = os.environ.get('CHAT_BOX_HOST', '0.0.0.0')

# Security middleware
@app.after_request
def after_request(response):
    """Add security headers to all responses"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    return response

# Authentication decorator
def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            verify_jwt_in_request()
            return f(*args, **kwargs)
        except Exception as e:
            return jsonify({'error': 'Authentication required', 'message': str(e)}), 401
    return decorated_function

# Authentication routes
@app.route('/api/auth/login', methods=['POST'])
def login():
    """Simple login endpoint for testing"""
    try:
        data = request.get_json()
        username = data.get('username', '')
        password = data.get('password', '')
        
        # Simple authentication (for testing only)
        if username == 'admin' and password == 'hexstrike2024':
            access_token = create_access_token(identity=username)
            return jsonify({
                'access_token': access_token,
                'user': username,
                'message': 'Login successful'
            })
        else:
            return jsonify({'error': 'Invalid credentials'}), 401
    except Exception as e:
        return jsonify({'error': 'Login failed', 'message': str(e)}), 500

@app.route('/api/auth/verify', methods=['GET'])
@require_auth
def verify_token():
    """Verify JWT token"""
    current_user = get_jwt_identity()
    return jsonify({
        'valid': True,
        'user': current_user,
        'message': 'Token is valid'
    })

# Root endpoint
@app.route('/', methods=['GET'])
def root():
    """Root endpoint with API documentation"""
    return jsonify({
        'service': 'HexStrike AI Test Server',
        'version': '1.0.0',
        'status': 'operational',
        'timestamp': datetime.now().isoformat(),
        'documentation': {
            'health': '/health',
            'features': '/api/features',
            'authentication': {
                'login': '/api/auth/login',
                'verify': '/api/auth/verify'
            },
            'testing': {
                'public': '/api/test/public',
                'protected': '/api/test/protected'
            }
        },
        'test_credentials': {
            'username': 'admin',
            'password': 'hexstrike2024'
        },
        'security': {
            'jwt': True,
            'cors': True,
            'security_headers': True
        }
    })

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'HexStrike AI Test Server',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat(),
        'features': {
            'authentication': True,
            'cors': True,
            'security_headers': True
        }
    })

# Test endpoints
@app.route('/api/test/public', methods=['GET'])
def test_public():
    """Public test endpoint"""
    return jsonify({
        'message': 'Public endpoint working',
        'timestamp': datetime.now().isoformat(),
        'security': 'CORS and security headers applied'
    })

@app.route('/api/test/protected', methods=['GET'])
@require_auth
def test_protected():
    """Protected test endpoint"""
    current_user = get_jwt_identity()
    return jsonify({
        'message': 'Protected endpoint working',
        'user': current_user,
        'timestamp': datetime.now().isoformat(),
        'security': 'JWT authentication verified'
    })

@app.route('/api/features', methods=['GET'])
def list_features():
    """List available features"""
    return jsonify({
        'features': {
            'authentication': {
                'jwt': True,
                'login_endpoint': '/api/auth/login',
                'verify_endpoint': '/api/auth/verify'
            },
            'security': {
                'cors': True,
                'security_headers': True,
                'csrf_protection': False
            },
            'testing': {
                'health_check': '/health',
                'public_test': '/api/test/public',
                'protected_test': '/api/test/protected'
            }
        },
        'status': 'All core features operational'
    })

if __name__ == '__main__':
    print(f"\n🚀 HexStrike AI Test Server Starting...")
    print(f"📡 Server: http://{API_HOST}:{API_PORT}")
    print(f"🔒 Security: JWT + CORS + Headers")
    print(f"🧪 Test Login: admin / hexstrike2024")
    print(f"\n📋 Available Endpoints:")
    print(f"   GET  /health - Health check")
    print(f"   GET  /api/features - Feature list")
    print(f"   POST /api/auth/login - Authentication")
    print(f"   GET  /api/auth/verify - Token verification")
    print(f"   GET  /api/test/public - Public test")
    print(f"   GET  /api/test/protected - Protected test")
    print(f"\n🎯 Ready for testing!\n")
    
    app.run(host=API_HOST, port=API_PORT, debug=False)