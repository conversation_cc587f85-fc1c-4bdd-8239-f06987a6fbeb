<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexStrike AI - All Features & Options</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --secondary: #764ba2;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e5e7eb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --border: #d1d5db;
            --border-light: #e5e7eb;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
            --radius: 8px;
            --radius-lg: 12px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-light);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .controls {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
        }

        .search-section {
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 2px solid var(--border);
            border-radius: var(--radius);
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .filters-section {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
        }

        .status-filter {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: var(--radius);
            padding: 4px;
        }

        .status-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: calc(var(--radius) - 4px);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .status-btn.active {
            background: var(--bg-primary);
            color: var(--primary);
            box-shadow: var(--shadow);
        }

        .tag-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-chip {
            padding: 6px 12px;
            background: var(--bg-tertiary);
            border: 2px solid transparent;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tag-chip.active {
            background: var(--primary);
            color: white;
        }

        .bulk-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: 2px solid var(--border);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: var(--radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            border-color: var(--primary);
            color: var(--primary);
        }

        .btn-primary {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .stats {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary);
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .features-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .feature-group {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .group-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 16px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .group-header:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--secondary));
        }

        .group-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .group-counter {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .group-toggle {
            transition: transform 0.3s ease;
        }

        .group-toggle.expanded {
            transform: rotate(180deg);
        }

        .group-content {
            padding: 20px;
            display: none;
        }

        .group-content.expanded {
            display: block;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 16px;
        }

        .feature-item {
            border: 2px solid var(--border-light);
            border-radius: var(--radius);
            padding: 16px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .feature-item:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow);
        }

        .feature-item.selected {
            border-color: var(--primary);
            background: rgba(102, 126, 234, 0.05);
        }

        .feature-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .feature-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 8px;
        }

        .feature-checkbox {
            width: 18px;
            height: 18px;
            margin-top: 2px;
            accent-color: var(--primary);
        }

        .feature-info {
            flex: 1;
        }

        .feature-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-key {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 4px;
            color: var(--text-secondary);
        }

        .feature-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .feature-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 8px;
        }

        .feature-tag {
            font-size: 11px;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 6px;
            border-radius: 10px;
        }

        .feature-badges {
            display: flex;
            gap: 6px;
        }

        .badge {
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            text-transform: uppercase;
        }

        .badge-beta {
            background: var(--warning);
            color: white;
        }

        .badge-disabled {
            background: var(--text-muted);
            color: white;
        }

        .dependency-info {
            margin-top: 8px;
            font-size: 12px;
            color: var(--text-muted);
        }

        .apply-section {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            border-top: 1px solid var(--border-light);
            padding: 20px;
            box-shadow: var(--shadow-lg);
            z-index: 50;
        }

        .apply-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .selection-summary {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .apply-btn {
            padding: 12px 32px;
            font-size: 16px;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            .filters-section {
                flex-direction: column;
                align-items: stretch;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .feature-list {
                grid-template-columns: 1fr;
            }

            .apply-content {
                flex-direction: column;
                align-items: stretch;
            }

            .apply-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles */
        .feature-item:focus-within {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        .btn:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="title">🔧 All Features & Options</h1>
                <div class="bulk-actions">
                    <button class="btn" id="selectAllBtn" aria-label="Select all visible features">
                        ✓ Select All
                    </button>
                    <button class="btn" id="clearAllBtn" aria-label="Clear all selections">
                        ✗ Clear All
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="controls">
            <div class="search-section">
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input 
                        type="text" 
                        class="search-input" 
                        id="searchInput" 
                        placeholder="Search features, keys, or tags..."
                        aria-label="Search features"
                    >
                </div>
            </div>

            <div class="filters-section">
                <div class="filter-group">
                    <label class="filter-label">Status Filter</label>
                    <div class="status-filter" role="radiogroup" aria-label="Filter by status">
                        <button class="status-btn active" data-status="all" role="radio" aria-checked="true">All</button>
                        <button class="status-btn" data-status="enabled" role="radio" aria-checked="false">Enabled</button>
                        <button class="status-btn" data-status="beta" role="radio" aria-checked="false">Beta</button>
                        <button class="status-btn" data-status="disabled" role="radio" aria-checked="false">Disabled</button>
                    </div>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Tag Filters</label>
                    <div class="tag-filters" id="tagFilters" role="group" aria-label="Filter by tags">
                        <!-- Tag chips will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <div class="stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalCount">0</div>
                    <div class="stat-label">Total Features</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="visibleCount">0</div>
                    <div class="stat-label">Visible</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="selectedVisibleCount">0</div>
                    <div class="stat-label">Selected (Visible)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="selectedTotalCount">0</div>
                    <div class="stat-label">Selected (Total)</div>
                </div>
            </div>
        </div>

        <div class="features-container" id="featuresContainer">
            <!-- Feature groups will be populated by JavaScript -->
        </div>

        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="empty-icon">🔍</div>
            <h3>No features found</h3>
            <p>Try adjusting your search or filter criteria</p>
        </div>
    </div>

    <div class="apply-section">
        <div class="container">
            <div class="apply-content">
                <div class="selection-summary" id="selectionSummary">
                    0 features selected
                </div>
                <button class="btn btn-primary apply-btn" id="applyBtn">
                    Apply Selection
                </button>
            </div>
        </div>
    </div>

    <script>
        // Feature data following the specified schema
        const FEATURES = [
            // Data Scraping & Intelligence
            {
                key: "scrape.web",
                label: "Web Scraping",
                group: "Data Scraping & Intelligence",
                description: "Extract HTML data from websites in real-time.",
                tags: ["scraping", "html", "realtime"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "scrape.pdf",
                label: "PDF Extraction",
                group: "Data Scraping & Intelligence",
                description: "Extract text and data from PDF documents.",
                tags: ["scraping", "pdf", "documents"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "scrape.realtime",
                label: "Real-Time Scraping",
                group: "Data Scraping & Intelligence",
                description: "Live data extraction with WebSocket connections.",
                tags: ["scraping", "realtime", "websocket"],
                default: false,
                dependsOn: ["scrape.web"],
                conflictsWith: [],
                beta: true,
                disabled: false
            },
            {
                key: "scrape.files",
                label: "File Data Extraction",
                group: "Data Scraping & Intelligence",
                description: "Extract data from various file formats (DOCX, XLSX, CSV).",
                tags: ["scraping", "files", "documents"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "scrape.export",
                label: "Structured Export",
                group: "Data Scraping & Intelligence",
                description: "Export scraped data in multiple formats.",
                tags: ["export", "csv", "json"],
                default: true,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "scrape.api",
                label: "API Integration Connectors",
                group: "Data Scraping & Intelligence",
                description: "Connect to external APIs for data enrichment.",
                tags: ["api", "integration", "connectors"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },

            // Security Testing
            {
                key: "sec.net",
                label: "Network Scanners",
                group: "Security Testing",
                description: "Comprehensive network discovery and port scanning.",
                tags: ["security", "network", "scanning"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "sec.web",
                label: "Web App Scanners",
                group: "Security Testing",
                description: "OWASP Top 10 and custom web application testing.",
                tags: ["security", "web", "owasp"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "sec.pass",
                label: "Password Auditors",
                group: "Security Testing",
                description: "Brute force and hash cracking capabilities.",
                tags: ["security", "password", "cracking"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "sec.bin",
                label: "Binary Analysis Tools",
                group: "Security Testing",
                description: "Static and dynamic binary analysis.",
                tags: ["security", "binary", "analysis"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: true,
                disabled: false
            },
            {
                key: "sec.cloud",
                label: "Cloud Security Tools",
                group: "Security Testing",
                description: "AWS, Azure, GCP security assessment.",
                tags: ["security", "cloud", "aws", "azure"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "sec.osint",
                label: "OSINT Recon Tools",
                group: "Security Testing",
                description: "Open source intelligence gathering.",
                tags: ["security", "osint", "reconnaissance"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },

            // AI Agents
            {
                key: "ai.bugbounty",
                label: "Bug Bounty Agent",
                group: "AI Agents",
                description: "Automated vulnerability hunting workflows.",
                tags: ["ai", "bugbounty", "automation"],
                default: false,
                dependsOn: ["sec.web", "sec.net"],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "ai.ctf",
                label: "CTF Solver Agent",
                group: "AI Agents",
                description: "Capture The Flag challenge automation.",
                tags: ["ai", "ctf", "challenges"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: true,
                disabled: false
            },
            {
                key: "ai.cve",
                label: "CVE Intelligence Agent",
                group: "AI Agents",
                description: "Vulnerability database analysis and monitoring.",
                tags: ["ai", "cve", "intelligence"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "ai.exploit",
                label: "Exploit Generator Agent",
                group: "AI Agents",
                description: "Custom exploit development and payload creation.",
                tags: ["ai", "exploit", "payloads"],
                default: false,
                dependsOn: ["ai.cve"],
                conflictsWith: [],
                beta: true,
                disabled: false
            },
            {
                key: "ai.reverse",
                label: "Malware Reverse Agent",
                group: "AI Agents",
                description: "Automated malware analysis and reverse engineering.",
                tags: ["ai", "malware", "reverse"],
                default: false,
                dependsOn: ["sec.bin"],
                conflictsWith: [],
                beta: true,
                disabled: false
            },
            {
                key: "ai.threat",
                label: "Threat Modeling Agent",
                group: "AI Agents",
                description: "Automated threat model generation and analysis.",
                tags: ["ai", "threat", "modeling"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "ai.soceng",
                label: "Social Engineering Simulator",
                group: "AI Agents",
                description: "Phishing simulation and awareness training.",
                tags: ["ai", "social", "phishing"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "ai.planner",
                label: "Pentest Planner Agent",
                group: "AI Agents",
                description: "Automated penetration testing workflow planning.",
                tags: ["ai", "pentest", "planning"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "ai.audit",
                label: "Compliance Auditor Agent",
                group: "AI Agents",
                description: "Regulatory compliance checking and reporting.",
                tags: ["ai", "compliance", "audit"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "ai.graph",
                label: "Attack Graph Builder",
                group: "AI Agents",
                description: "Visual attack path analysis and mapping.",
                tags: ["ai", "graph", "visualization"],
                default: false,
                dependsOn: ["ai.threat"],
                conflictsWith: [],
                beta: true,
                disabled: false
            },
            {
                key: "ai.redteam",
                label: "AI Red Team Agent",
                group: "AI Agents",
                description: "Automated red team operations and tactics.",
                tags: ["ai", "redteam", "tactics"],
                default: false,
                dependsOn: ["ai.bugbounty"],
                conflictsWith: ["ai.blueteam"],
                beta: true,
                disabled: false
            },
            {
                key: "ai.blueteam",
                label: "AI Blue Team Agent",
                group: "AI Agents",
                description: "Automated defense and incident response.",
                tags: ["ai", "blueteam", "defense"],
                default: false,
                dependsOn: [],
                conflictsWith: ["ai.redteam"],
                beta: true,
                disabled: false
            },

            // Reporting & Visualization
            {
                key: "rep.dash",
                label: "Interactive Dashboards",
                group: "Reporting & Visualization",
                description: "Real-time dashboards with live data updates.",
                tags: ["reporting", "dashboard", "realtime"],
                default: true,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "rep.heatmap",
                label: "Vulnerability Heatmaps",
                group: "Reporting & Visualization",
                description: "Visual vulnerability distribution and severity mapping.",
                tags: ["reporting", "heatmap", "visualization"],
                default: false,
                dependsOn: ["rep.dash"],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "rep.charts",
                label: "Analytics Charts",
                group: "Reporting & Visualization",
                description: "Interactive charts and graphs for data analysis.",
                tags: ["reporting", "charts", "analytics"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "rep.export",
                label: "Export to PDF/DOCX/CSV",
                group: "Reporting & Visualization",
                description: "Multi-format report generation and export.",
                tags: ["reporting", "export", "pdf"],
                default: true,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "rep.collab",
                label: "Live Collaboration Boards",
                group: "Reporting & Visualization",
                description: "Real-time collaborative workspaces.",
                tags: ["reporting", "collaboration", "realtime"],
                default: false,
                dependsOn: ["rep.dash"],
                conflictsWith: [],
                beta: true,
                disabled: false
            },

            // Process Management
            {
                key: "proc.cache",
                label: "Smart Caching Engine",
                group: "Process Management",
                description: "Intelligent caching for improved performance.",
                tags: ["process", "cache", "performance"],
                default: true,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "proc.optimize",
                label: "Resource Load Optimizer",
                group: "Process Management",
                description: "Automatic resource allocation and optimization.",
                tags: ["process", "optimization", "resources"],
                default: false,
                dependsOn: ["proc.cache"],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "proc.queue",
                label: "Task Scheduler & Queue",
                group: "Process Management",
                description: "Advanced task scheduling and queue management.",
                tags: ["process", "queue", "scheduling"],
                default: true,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "proc.rbac",
                label: "Role-Based Security Control",
                group: "Process Management",
                description: "Granular access control and permissions.",
                tags: ["process", "security", "rbac"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "proc.audit",
                label: "Audit Logging & Tracing",
                group: "Process Management",
                description: "Comprehensive activity logging and tracing.",
                tags: ["process", "audit", "logging"],
                default: true,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            },
            {
                key: "proc.backup",
                label: "Backup & Recovery",
                group: "Process Management",
                description: "Automated backup and disaster recovery.",
                tags: ["process", "backup", "recovery"],
                default: false,
                dependsOn: [],
                conflictsWith: [],
                beta: false,
                disabled: false
            }
        ];

        // Application state
        const state = {
            search: "",
            filters: {
                tags: [],
                status: "all"
            },
            expandedGroups: {},
            selected: {},
            features: FEATURES,
            counts: {
                total: 0,
                visible: 0,
                selectedVisible: 0,
                selectedTotal: 0
            }
        };

        // Initialize default selections
        FEATURES.forEach(feature => {
            if (feature.default) {
                state.selected[feature.key] = true;
            }
        });

        // Get all unique tags
        const getAllTags = () => {
            const tags = new Set();
            FEATURES.forEach(feature => {
                feature.tags.forEach(tag => tags.add(tag));
            });
            return Array.from(tags).sort();
        };

        // Filter features based on current state
        const getFilteredFeatures = () => {
            return FEATURES.filter(feature => {
                // Search filter
                if (state.search) {
                    const searchLower = state.search.toLowerCase();
                    const matchesSearch = 
                        feature.label.toLowerCase().includes(searchLower) ||
                        feature.key.toLowerCase().includes(searchLower) ||
                        feature.description.toLowerCase().includes(searchLower) ||
                        feature.tags.some(tag => tag.toLowerCase().includes(searchLower));
                    
                    if (!matchesSearch) return false;
                }

                // Status filter
                if (state.filters.status !== "all") {
                    if (state.filters.status === "enabled" && (feature.beta || feature.disabled)) return false;
                    if (state.filters.status === "beta" && !feature.beta) return false;
                    if (state.filters.status === "disabled" && !feature.disabled) return false;
                }

                // Tag filters
                if (state.filters.tags.length > 0) {
                    const hasMatchingTag = state.filters.tags.some(tag => feature.tags.includes(tag));
                    if (!hasMatchingTag) return false;
                }

                return true;
            });
        };

        // Group features by group
        const groupFeatures = (features) => {
            const groups = {};
            features.forEach(feature => {
                if (!groups[feature.group]) {
                    groups[feature.group] = [];
                }
                groups[feature.group].push(feature);
            });
            return groups;
        };

        // Update counts
        const updateCounts = () => {
            const filteredFeatures = getFilteredFeatures();
            const selectedKeys = Object.keys(state.selected).filter(key => state.selected[key]);
            const selectedVisible = filteredFeatures.filter(f => state.selected[f.key]).length;

            state.counts = {
                total: FEATURES.length,
                visible: filteredFeatures.length,
                selectedVisible: selectedVisible,
                selectedTotal: selectedKeys.length
            };

            // Update UI
            document.getElementById('totalCount').textContent = state.counts.total;
            document.getElementById('visibleCount').textContent = state.counts.visible;
            document.getElementById('selectedVisibleCount').textContent = state.counts.selectedVisible;
            document.getElementById('selectedTotalCount').textContent = state.counts.selectedTotal;
            document.getElementById('selectionSummary').textContent = 
                `${state.counts.selectedTotal} features selected`;
        };

        // Handle dependency logic
        const handleDependencies = (featureKey, isSelected) => {
            const feature = FEATURES.find(f => f.key === featureKey);
            if (!feature) return;

            if (isSelected) {
                // Auto-enable dependencies
                feature.dependsOn.forEach(depKey => {
                    if (!state.selected[depKey]) {
                        state.selected[depKey] = true;
                        const checkbox = document.querySelector(`input[data-key="${depKey}"]`);
                        if (checkbox) checkbox.checked = true;
                    }
                });

                // Disable conflicts
                feature.conflictsWith.forEach(conflictKey => {
                    if (state.selected[conflictKey]) {
                        state.selected[conflictKey] = false;
                        const checkbox = document.querySelector(`input[data-key="${conflictKey}"]`);
                        if (checkbox) checkbox.checked = false;
                    }
                });
            }
        };

        // Render tag filters
        const renderTagFilters = () => {
            const tagFilters = document.getElementById('tagFilters');
            const allTags = getAllTags();
            
            tagFilters.innerHTML = allTags.map(tag => `
                <button 
                    class="tag-chip ${state.filters.tags.includes(tag) ? 'active' : ''}" 
                    data-tag="${tag}"
                    aria-pressed="${state.filters.tags.includes(tag)}"
                >
                    ${tag}
                </button>
            `).join('');
        };

        // Render features
        const renderFeatures = () => {
            const container = document.getElementById('featuresContainer');
            const emptyState = document.getElementById('emptyState');
            const filteredFeatures = getFilteredFeatures();
            const groupedFeatures = groupFeatures(filteredFeatures);

            if (filteredFeatures.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'block';
            emptyState.style.display = 'none';

            container.innerHTML = Object.entries(groupedFeatures).map(([groupName, features]) => {
                const selectedInGroup = features.filter(f => state.selected[f.key]).length;
                const isExpanded = state.expandedGroups[groupName] !== false;

                return `
                    <div class="feature-group" role="group" aria-labelledby="group-${groupName.replace(/\s+/g, '-')}">
                        <div 
                            class="group-header" 
                            data-group="${groupName}"
                            role="button"
                            aria-expanded="${isExpanded}"
                            tabindex="0"
                        >
                            <div class="group-title">
                                <span id="group-${groupName.replace(/\s+/g, '-')}">${groupName}</span>
                                <span class="group-counter">${selectedInGroup}/${features.length}</span>
                            </div>
                            <span class="group-toggle ${isExpanded ? 'expanded' : ''}">
                                ▼
                            </span>
                        </div>
                        <div class="group-content ${isExpanded ? 'expanded' : ''}">
                            <div class="feature-list">
                                ${features.map(feature => {
                                    const isSelected = state.selected[feature.key] || false;
                                    const dependencyText = feature.dependsOn.length > 0 
                                        ? `Requires: ${feature.dependsOn.join(', ')}` 
                                        : '';
                                    const conflictText = feature.conflictsWith.length > 0 
                                        ? `Conflicts with: ${feature.conflictsWith.join(', ')}` 
                                        : '';
                                    
                                    return `
                                        <div class="feature-item ${isSelected ? 'selected' : ''} ${feature.disabled ? 'disabled' : ''}">
                                            <div class="feature-header">
                                                <input 
                                                    type="checkbox" 
                                                    class="feature-checkbox" 
                                                    id="feature-${feature.key}"
                                                    data-key="${feature.key}"
                                                    ${isSelected ? 'checked' : ''}
                                                    ${feature.disabled ? 'disabled' : ''}
                                                    aria-describedby="desc-${feature.key}"
                                                >
                                                <div class="feature-info">
                                                    <div class="feature-title">
                                                        <label for="feature-${feature.key}">${feature.label}</label>
                                                        <span class="feature-key">${feature.key}</span>
                                                    </div>
                                                    <div class="feature-description" id="desc-${feature.key}">
                                                        ${feature.description}
                                                    </div>
                                                    <div class="feature-tags">
                                                        ${feature.tags.map(tag => `<span class="feature-tag">${tag}</span>`).join('')}
                                                    </div>
                                                    <div class="feature-badges">
                                                        ${feature.beta ? '<span class="badge badge-beta">Beta</span>' : ''}
                                                        ${feature.disabled ? '<span class="badge badge-disabled">Disabled</span>' : ''}
                                                    </div>
                                                    ${dependencyText || conflictText ? `
                                                        <div class="dependency-info">
                                                            ${dependencyText ? `<div>${dependencyText}</div>` : ''}
                                                            ${conflictText ? `<div>${conflictText}</div>` : ''}
                                                        </div>
                                                    ` : ''}
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        };

        // Event handlers
        const handleSearch = (e) => {
            state.search = e.target.value;
            renderFeatures();
            updateCounts();
        };

        const handleStatusFilter = (e) => {
            if (e.target.classList.contains('status-btn')) {
                document.querySelectorAll('.status-btn').forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-checked', 'false');
                });
                e.target.classList.add('active');
                e.target.setAttribute('aria-checked', 'true');
                state.filters.status = e.target.dataset.status;
                renderFeatures();
                updateCounts();
            }
        };

        const handleTagFilter = (e) => {
            if (e.target.classList.contains('tag-chip')) {
                const tag = e.target.dataset.tag;
                const isActive = state.filters.tags.includes(tag);
                
                if (isActive) {
                    state.filters.tags = state.filters.tags.filter(t => t !== tag);
                } else {
                    state.filters.tags.push(tag);
                }
                
                e.target.classList.toggle('active');
                e.target.setAttribute('aria-pressed', (!isActive).toString());
                renderFeatures();
                updateCounts();
            }
        };

        const handleGroupToggle = (e) => {
            const groupName = e.target.closest('.group-header').dataset.group;
            state.expandedGroups[groupName] = !state.expandedGroups[groupName];
            renderFeatures();
        };

        const handleFeatureToggle = (e) => {
            if (e.target.classList.contains('feature-checkbox')) {
                const key = e.target.dataset.key;
                const isChecked = e.target.checked;
                
                state.selected[key] = isChecked;
                handleDependencies(key, isChecked);
                renderFeatures();
                updateCounts();
            }
        };

        const handleSelectAll = () => {
            const filteredFeatures = getFilteredFeatures();
            filteredFeatures.forEach(feature => {
                if (!feature.disabled) {
                    state.selected[feature.key] = true;
                }
            });
            renderFeatures();
            updateCounts();
        };

        const handleClearAll = () => {
            Object.keys(state.selected).forEach(key => {
                state.selected[key] = false;
            });
            renderFeatures();
            updateCounts();
        };

        const handleApply = () => {
            const selectedKeys = Object.keys(state.selected).filter(key => state.selected[key]);
            const selectedByGroup = {};
            
            // Group selected features
            FEATURES.forEach(feature => {
                if (!selectedByGroup[feature.group]) {
                    selectedByGroup[feature.group] = [];
                }
                if (state.selected[feature.key]) {
                    selectedByGroup[feature.group].push(feature.key);
                }
            });

            const result = {
                selectedKeys,
                selectedByGroup,
                count: selectedKeys.length,
                timestamp: new Date().toISOString()
            };

            // Output to console and show alert
            console.log('Selected Features:', result);
            alert(`Selection applied! ${selectedKeys.length} features selected.\n\nCheck console for full JSON output.`);
            
            // You can also send this to your backend or parent application
            // window.parent.postMessage({ type: 'FEATURE_SELECTION', data: result }, '*');
        };

        // Debounce function
        const debounce = (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        };

        // Initialize the application
        const init = () => {
            // Set up event listeners
            document.getElementById('searchInput').addEventListener('input', debounce(handleSearch, 300));
            document.querySelector('.status-filter').addEventListener('click', handleStatusFilter);
            document.getElementById('tagFilters').addEventListener('click', handleTagFilter);
            document.getElementById('featuresContainer').addEventListener('click', handleGroupToggle);
            document.getElementById('featuresContainer').addEventListener('change', handleFeatureToggle);
            document.getElementById('selectAllBtn').addEventListener('click', handleSelectAll);
            document.getElementById('clearAllBtn').addEventListener('click', handleClearAll);
            document.getElementById('applyBtn').addEventListener('click', handleApply);

            // Keyboard navigation for group headers
            document.addEventListener('keydown', (e) => {
                if (e.target.classList.contains('group-header') && (e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault();
                    handleGroupToggle(e);
                }
            });

            // Initialize expanded groups (all expanded by default)
            const groups = [...new Set(FEATURES.map(f => f.group))];
            groups.forEach(group => {
                state.expandedGroups[group] = true;
            });

            // Initial render
            renderTagFilters();
            renderFeatures();
            updateCounts();
        };

        // Start the application
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>